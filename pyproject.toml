[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[tool.pylint.MASTER]
init-hook = '''
import sys
from pathlib import Path
project_root = Path(__file__).parent.resolve()
sys.path.extend([
    str(project_root),
    str(project_root / "src"),
])
'''

[tool.pytest.ini_options]
pythonpath = [".", "src"]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v"

# Ensure consistent environment for pre-commit hooks and CI/CD
[tool.pre-commit]
py_root = "src"

# Configure pytest for consistent behavior across environments
[tool.pytest]
python_paths = [".", "src"]
env = [
    "PYTHONPATH=.:src",
    "PYTEST_ADDOPTS=--import-mode=importlib"
]
