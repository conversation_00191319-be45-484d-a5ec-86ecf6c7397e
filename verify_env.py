"""
Simple script to verify the Python environment.
"""

import os
import sys

print("Python version:", sys.version)
print("Current working directory:", os.getcwd())
print("Python path:", sys.path)

try:
    from src.api.config import Settings

    print("Successfully imported settings")
    print("Supabase profile:", Settings.SUPABASE_PROFILE)
except ImportError as e:
    print("Failed to import settings:", e)

try:
    from src.database.supabase_client import get_supabase_client

    print("Successfully imported get_supabase_client", get_supabase_client)
except ImportError as e:
    print("Failed to import get_supabase_client:", e)
