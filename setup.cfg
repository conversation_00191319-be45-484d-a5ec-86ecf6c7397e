[tool:pytest]
pythonpath = src
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v
asyncio_mode = auto
markers =
    asyncio: mark a test as an asyncio test

[pylint]
init-hook=
    import sys;
    from pathlib import Path;
    project_root = Path(__file__).parent.resolve();
    sys.path.extend([
        str(project_root / "src"),
        str(project_root / ".env/lib/python3.12/site-packages")
    ])
