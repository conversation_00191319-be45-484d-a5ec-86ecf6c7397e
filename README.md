# README #

## What is this repository for? ##

This project contains Deep Learning models to train and use trained best selected models to classify images of coins.

## Structure ##

Folder structure

- `data/`: Scripts for loading and preprocessing data.
- `models/`: Model architecture definitions.
- `train/`: Scripts for training models.
- `evaluate/`: Scripts to evaluate models and calculate metrics.
- `utils/`: Utility functions.
- `scripts/`: Scripts for running the model, training, and evaluation.
- `database/`: Database connectivity and operations with Supabase.
- `api/`: FastAPI application to expose trigger actions for training, prediction and communication with external environments.

## Organising data ##

Setup model meta parameters externally -> Load model setup from database -> train -> test with dev set -> adapt (N epochs). Then run the model once with your test set to get no over-fitting

Organise your data in the following folders

### Training set ###

`data/train/` - Allocate 80% of your data for training

### Dev set ###

`data/dev/` - Allocate 10% of your data for dev testing to figure out if your model is biased for overfitting. Allow your model to learn from the dev set

### Test set ###

`data/test/` - Allocate 10% of your data for actual testing. Do not show this data to the model. Use only to verify performance

## Requirements ##

- Python 3.12

## How do I get set up? ##

### Local environment ###

First, make sure you have a compatible Python version installed. You can check your Python version with:

```bash
python3 --version
```

If you need to install a compatible Python version (e.g., Python 3.12), you can:

```bash
# On macOS (using Homebrew):
brew install python@3.12

# On Ubuntu/Debian:
sudo apt update
sudo apt install python3.12 python3.12-venv

```

- On Windows:
  Download Python 3.12 from the official Python website: <https://www.python.org/downloads/>

## Virtual Environment

This project uses a virtual environment named `.env` to manage Python dependencies. The `.env` directory is the standard location for the virtual environment in this project.

### Creating the Virtual Environment

```bash
# Create virtual environment with Python 3.12 in the .env directory
python3.12 -m venv .env
```

### Activating the Virtual Environment

```bash
# On Unix/macOS
source .env/bin/activate

# On Windows
.env\Scripts\activate
```

> **Note**: The `.env` directory is in `.gitignore` and should not be committed to version control. Each developer should create their own virtual environment.

# Install PyTorch first (choose the appropriate command for your system)
# For CPU only:
pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# For CUDA 11.8:
pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# For CUDA 12.1:
pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cu121

# For macOS (including M1/M2):
pip3 install torch torchvision

# After PyTorch is installed, install other dependencies
pip3 install -r requirements.txt
```

### Linting ###

To maintain code quality in high standard, the following linters and formatters are used

- Linters: flake8, pylint
- Formatter: black

Run this command to generate in case it does not exist in the repo

```bash
pylint --generate-rcfile > .pylintrc
```

### Installation ###

Install the project in edit mode

```bash
pip3 install -e .
```

- TODO: How to run tests
- TODO: Deployment instructions

## Development Guide ##

### Setting Up Development Environment ###

1. Activate the virtual environment:

   ```bash
   # On Unix/macOS
   source .env/bin/activate

   # On Windows
   .env\Scripts\activate
   ```

2. Install the package in development mode:

   ```bash
   pip install -e ".[test]"
   ```

3. Install pre-commit hooks (required for code quality checks before every commit):

   ```bash
   pre-commit install
   ```

   This command sets up git hooks so that code style, linting, and other checks are automatically run before each commit. If you skip this step, your commits will not be checked automatically.

### Running the API Server ###

1. Make sure your virtual environment is activated
2. Start the development server:

   ```bash
   # Option 1: Using the run script (from project root)
   PYTHONPATH=src .env/bin/python src/run_api.py

   # Option 2: Using uvicorn directly (from project root)
   PYTHONPATH=src uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
   ```

The API server will start at <http://localhost:8000>

Available endpoints:

- API documentation: <http://localhost:8000/docs>
- OpenAPI spec: <http://localhost:8000/openapi.json>
- Health check: <http://localhost:8000/health>
- Root endpoint: <http://localhost:8000/>

## API Documentation ##

The API documentation is automatically generated using FastAPI's built-in support for OpenAPI (formerly Swagger) and provides interactive documentation interfaces.

### Available Documentation Interfaces ###

Once the API server is running, you can access the documentation at:

- **Swagger UI** (Interactive Documentation): <http://localhost:8000/docs>
  
  Features:
  - Interactive API testing interface
  - Request/response examples
  - Schema definitions
  - Authentication testing
  - Real-time request execution

- **ReDoc** (Alternative Documentation View): <http://localhost:8000/redoc>
  
  Features:
  - Clean, three-panel layout
  - Search functionality
  - Right-hand schema definitions
  - Printer-friendly

- **OpenAPI JSON Schema**: <http://localhost:8000/openapi.json>

  Raw OpenAPI specification in JSON format, useful for:
  - Generating client libraries
  - Integration with other tools
  - API documentation generation

### Documentation Updates ###

The API documentation is automatically generated and updated based on:

- Route decorators and path operations
- Function docstrings
- Type hints
- Pydantic models
- Response models
- Example values
- HTTP status codes

No manual documentation updates are required - just ensure your code includes:

- Proper type hints
- Detailed docstrings
- Pydantic models for request/response validation
- Examples in model configurations

### Using the Documentation ###

1. Start the API server:

   ```bash
   PYTHONPATH=src python src/run_api.py
   ```

2. Open <http://localhost:8000/docs> in your browser

3. In the Swagger UI, you can:
   - Browse available endpoints
   - Read endpoint descriptions
   - Test endpoints directly from the browser
   - View response status codes and bodies

4. For each endpoint, you'll find:
   - HTTP method and path
   - Description and summary
   - Request parameters
   - Request body schema (if applicable)
   - Example values
   - Possible error responses

### Supabase Integration ###

This project uses Supabase for database operations. To configure Supabase:

1. Copy the `.env.example` file to `.env`:

   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and set your Supabase URL and API key:

   ```bash
   # Development profile
   COINY_CLASSIFIER_SUPABASE_URL=your_development_supabase_url
   COINY_CLASSIFIER_SUPABASE_KEY=your_development_supabase_anon_key
   ```

   You can use the same Supabase credentials as in the web project.

3. The Supabase client is available through the `supabase_client` singleton:

   ```python
   from database.supabase_client import supabase_client

   # Example: Fetch data from a table
   datasets = supabase_client.fetch_data("datasets")
   ```

4. The API includes example endpoints for working with datasets in Supabase:
   - GET `/api/v1/datasets` - List all datasets
   - GET `/api/v1/datasets/{dataset_id}` - Get a specific dataset

## Development Workflow ##

1. Make sure your virtual environment is active
2. Write your code following the project structure
3. Ensure best practices and standards are followed
4. Write tests for your code. Do not aim at covering 100% of the code. Ensure essential parts are covered.
5. Run linting and tests before committing:

   ```bash
   # Run all linters
   flake8 src tests
   pylint src tests
   black src tests --check

   # Run tests
   pytest
   ```

6. If all checks pass, commit your changes:

   ```bash
   git add .
   git commit -m "Your descriptive commit message"
   ```

   The pre-commit hooks will automatically run linting and tests
7. Submit pull request for review before merging into 

### Common Development Tasks ###

```bash
# Format code
black src tests
   
# Run specific test file:
pytest tests/test_api.py -v

# Run tests with coverage:
pytest --cov=src tests/
```

### Manual Testing ###

#### Endpoints ####

```bash
# Fire GET requests
% curl -H "X-Supabase-Profile: development" http://localhost:8000/api/v1/datasets/4c85e54b-24e8-4203-b7bc-4a2f6b888704

# Fire POST requests
curl -X POST http://localhost:8000/api/v1/train \
  -H "Content-Type: application/json" \
  -H "X-Supabase-Profile: development" \
  -d '{
    "model_version_uuid": "f72599b5-942e-4877-bbea-1717244908da",
    "dataset_uuid": "8b1003f7-c430-4fdc-999b-7c5fec055269",
    "experiment_uuid": "uuid_E1"
  }'
```

```bash
# Test with development profile (default)
.env/bin/python tests/manual/api/routes/test_train.py 75d9e619-bff0-49a9-a4d5-2734c0f5d8e6

# Test with production profile
.env/bin/python tests/manual/api/routes/test_train.py 75d9e619-bff0-49a9-a4d5-2734c0f5d8e6 --profile production

# Test with staging profile
.env/bin/python tests/manual/api/routes/test_train.py 75d9e619-bff0-49a9-a4d5-2734c0f5d8e6 --profile staging
```

#### Models ####

<!-- TODO: -->

### Troubleshooting ###

If you encounter import errors while running tests or linting:

1. Ensure your virtual environment is activated
2. Verify the package is installed in development mode:

   ```bash
   pip install -e ".[test]"
   ```

3. Check that you're running commands from the project root directory

If the API server fails to start:

1. Check if port 8000 is already in use:

   ```bash
   # On Unix/macOS
   lsof -i :8000

   # On Windows
   netstat -ano | findstr :8000
   ```

2. Verify all dependencies are installed:

   ```bash
   pip install -r requirements.txt
   ```
