#!/bin/bash
# Fast test execution script for CI/commits
# Uses parallel execution and optimized settings

set -e

echo "🚀 Running fast test suite..."
echo "Using parallel execution with pytest-xdist"

# Set PYTHONPATH to ensure proper imports
export PYTHONPATH=".:src:$PYTHONPATH"

# Run tests with parallel execution and minimal output
python -m pytest tests/ \
    --tb=short \
    -q \
    -n auto \
    --disable-warnings \
    "$@"

echo "✅ Fast test suite completed!"
