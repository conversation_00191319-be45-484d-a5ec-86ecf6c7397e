#!/bin/bash
# Comprehensive test execution script for development/CI
# Uses parallel execution with verbose output

set -e

echo "🔍 Running comprehensive test suite..."
echo "Using parallel execution with detailed output"

# Set PYTHONPATH to ensure proper imports
export PYTHONPATH=".:src:$PYTHONPATH"

# Run tests with parallel execution and verbose output
python -m pytest tests/ \
    --tb=short \
    -v \
    -n auto \
    "$@"

echo "✅ Comprehensive test suite completed!"
