"""
Setup configuration for the coiny classifier.
"""

from pathlib import Path

from setuptools import find_packages, setup

this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text()

setup(
    name="coiny-classifier",
    version="0.1.0",
    author="<PERSON><PERSON>",
    author_email="<EMAIL>",
    description="A deep learning project to classify images of coins",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://<EMAIL>/coiny-pro/coiny-classifier",
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    package_data={
        "pylint_plugins": ["*.py"],
    },
    include_package_data=True,
    install_requires=[
        "numpy",
        "pandas",
        "scikit-learn",
        "torch",
        "matplotlib",
        "seaborn",
        "torchvision",
        "flake8",
        "pylint",
        "scipy",
        "IPython",
        "black",
        "pre-commit",
        "pytest",
        # API dependencies
        "fastapi",
        "uvicorn[standard]",
        "python-multipart",  # for handling form/file uploads
        "pydantic",  # already included with FastAPI, but good to specify
        "pydantic-settings",
        # Database dependencies
        "supabase",
        "python-dotenv",
    ],
    extras_require={
        "test": [
            "pytest",
            "pytest-cov",
            "httpx",
        ],
        "dev": [
            "black",
            "flake8",
            "pylint",
            "pre-commit",
        ],
    },
    python_requires=">=3.9,<3.13",
)
