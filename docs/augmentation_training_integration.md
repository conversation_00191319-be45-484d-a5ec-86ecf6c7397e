# Augmentation Training Pipeline Integration

This document describes the complete integration of the augmentations module with the training pipeline, enabling seamless data augmentation during training and inference compatibility.

## Overview

The augmentation integration provides:

- **Automatic augmentation pipeline creation** from database configurations
- **Training/inference consistency** with separate transform pipelines
- **Artifact persistence** for deployment and inference
- **Backward compatibility** with existing training infrastructure

## Architecture

### Components

1. **AugmentedDataset** - Custom PyTorch Dataset with transform support
2. **DataLoadingService** - Service for creating augmented data loaders
3. **TrainingJobDispatcher** - Enhanced with augmentation extraction
4. **ModelTrainer** - Enhanced with augmentation artifact saving

### Data Flow

```mermaid
graph TD
    A[Model Run with Augmentations] --> B[TrainingJobDispatcher]
    B --> C[Extract Augmentations]
    C --> D[Create Transform Pipelines]
    D --> E[DataLoadingService]
    E --> F[AugmentedDataset]
    F --> G[DataLoader]
    G --> H[ModelTrainer]
    H --> I[Save Model + Augmentation Artifacts]
```

## Usage

### 1. Database Configuration

Augmentations are stored in the `model_run.augmentations` field:

```json
{
  "model_run": {
    "uuid": "run-123",
    "augmentations": [
      {"resize_dimensions": [224, 224]},
      {"horizontal_flip": true},
      {"rotation_range": 15.0},
      {"normalization_mean": [0.485, 0.456, 0.406]},
      {"normalization_std": [0.229, 0.224, 0.225]}
    ]
  }
}
```

### 2. Automatic Pipeline Creation

The system automatically creates two pipelines:

```python
# Training pipeline (with random augmentations)
train_transforms = AugmentationPipelineFactory.create_training_pipeline(augmentations)

# Inference pipeline (deterministic only)
inference_transforms = AugmentationPipelineFactory.create_inference_pipeline(augmentations)
```

### 3. Data Loading

```python
# Automatic data loader creation with augmentations
data_loaders = await DataLoadingService.create_data_loaders(
    dataset_uuid="dataset-123",
    augmentations=augmentations,
    batch_size=32,
    test_size=0.2,
    profile="production"
)
```

### 4. Training Integration

The TrainingJobDispatcher automatically:

1. Extracts augmentations from training data
2. Creates transform pipelines
3. Prepares augmented data loaders
4. Passes them to ModelTrainer

### 5. Artifact Persistence

ModelTrainer automatically saves:

- `augmentation_config.json` - Human-readable configuration
- `inference_transforms.pkl` - Serialized inference pipeline
- `training_transforms.pkl` - Serialized training pipeline

## API Reference

### AugmentedDataset

```python
class AugmentedDataset(Dataset):
    def __init__(
        self,
        images: torch.Tensor,
        labels: torch.Tensor,
        transform: Optional[transforms.Compose] = None,
        target_transform: Optional[transforms.Compose] = None,
    )
```

**Key Methods:**
- `__getitem__(idx)` - Get transformed sample
- `get_raw_sample(idx)` - Get untransformed sample
- `update_transform(transform)` - Change transform pipeline
- `get_dataset_info()` - Get dataset statistics

### DataLoadingService

```python
class DataLoadingService:
    @classmethod
    async def create_data_loaders(
        cls,
        dataset_uuid: Union[str, UUID],
        augmentations: Optional[List[AugmentationItemType]] = None,
        batch_size: int = 32,
        test_size: float = 0.2,
        profile: Optional[str] = None,
        use_mock_data: bool = True,
    ) -> Dict[str, DataLoader]
```

**Key Methods:**
- `create_data_loaders()` - Create augmented data loaders
- `get_data_loader_info()` - Get data loader statistics
- `validate_data_loaders()` - Validate data loader configuration

### TrainingJobDispatcher Integration

The dispatcher now includes:

```python
async def _prepare_augmented_data_loaders(
    self, training_data: Dict[str, Any], job: TrainingJob
) -> Dict[str, Any]
```

This method:
1. Extracts augmentations from `training_data["model_run"]["augmentations"]`
2. Converts dictionary format to augmentation objects
3. Creates training and inference pipelines
4. Uses DataLoadingService to create data loaders
5. Stores metadata for artifact saving

### ModelTrainer Enhancement

Enhanced with augmentation artifact saving:

```python
def _save_augmentation_artifacts(self, output_dir: Path):
    """Save augmentation pipelines and configurations for inference compatibility."""
    # Save augmentation configuration
    AugmentationSerializer.save_augmentation_config(
        augmentations, output_dir / "augmentation_config.json"
    )

    # Save inference pipeline (most important for deployment)
    AugmentationSerializer.save_pipeline(
        val_transforms, output_dir / "inference_transforms.pkl"
    )

    # Save training pipeline (for reproducibility)
    AugmentationSerializer.save_pipeline(
        train_transforms, output_dir / "training_transforms.pkl"
    )
```

## Configuration Examples

### Basic Augmentations

```python
augmentations = [
    ResizeDimensions(resize_dimensions=[224, 224]),
    NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
    NormalizationStd(normalization_std=[0.229, 0.224, 0.225])
]
```

### Advanced Augmentations

```python
augmentations = [
    ResizeDimensions(resize_dimensions=[256, 256]),
    HorizontalFlip(horizontal_flip=True),
    RotationRange(rotation_range=20.0),
    ZoomRange(zoom_range=0.1),
    NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
    NormalizationStd(normalization_std=[0.229, 0.224, 0.225])
]
```

## Inference Deployment

### Loading Saved Artifacts

```python
from augmentations import AugmentationSerializer

# Load inference pipeline (most common use case)
inference_pipeline = AugmentationSerializer.load_pipeline("model_artifacts/inference_transforms.pkl")

# Load configuration (optional, for inspection)
config = AugmentationSerializer.load_augmentation_config("model_artifacts/augmentation_config.json")

# Load all inference artifacts at once (alternative approach)
artifacts = AugmentationSerializer.load_inference_artifacts("model_artifacts/")
inference_pipeline = artifacts["inference_pipeline"]
config = artifacts.get("config")  # Optional
```

### Applying to New Images

```python
# Apply same preprocessing as training
processed_image = inference_pipeline(raw_image)
prediction = model(processed_image.unsqueeze(0))
```

## Testing

Comprehensive test coverage includes:

- **Unit tests** for individual components (`AugmentationUtils`, `AugmentationPipelineFactory`)
- **Integration tests** for end-to-end pipeline
- **Mock data testing** for development
- **Error handling** validation

### Test Structure

```text
tests/
├── unit/augmentations/
│   ├── test_factory.py      # Pipeline factory tests
│   └── test_utils.py        # Utility function tests
└── integration/
    └── test_augmentation_training_integration.py  # End-to-end tests
```

### Running Tests

```bash
# Run augmentation integration tests
pytest tests/integration/test_augmentation_training_integration.py -v

# Run all augmentation tests
pytest tests/unit/augmentations/ tests/integration/test_augmentation_training_integration.py -v

# Run specific test categories
pytest tests/unit/augmentations/test_factory.py -v  # Factory tests only
pytest tests/unit/augmentations/test_utils.py -v   # Utils tests only
```

## Performance Considerations

### Training Overhead

- **Resize operations**: Moderate GPU/CPU usage
- **Random augmentations**: Low overhead
- **Normalization**: Minimal overhead
- **Complex transforms**: Higher memory usage

### Memory Usage

- **Transform caching**: Minimal memory impact
- **Batch processing**: Scales with batch size
- **Pipeline serialization**: Small disk footprint

### Optimization Tips

1. **Use appropriate batch sizes** for your hardware
2. **Enable GPU acceleration** for transform operations
3. **Cache frequently used pipelines** for inference
4. **Monitor memory usage** with complex augmentations

## Recent Improvements

### Code Simplification (v2.0)

Recent updates have simplified the augmentation system:

- **Removed redundant validation**: `AugmentationValidator` class removed in favor of `AugmentationPipelineFactory.validate_augmentations()`
- **Streamlined artifact saving**: Individual serialization methods used directly instead of wrapper functions
- **Cleaner API**: Focused on essential functionality, removed unused utility methods
- **Better separation of concerns**: Each component has a single, clear responsibility

### Validation

Validation is now handled by the factory:

```python
from augmentations import AugmentationPipelineFactory

# Validate augmentation configuration
result = AugmentationPipelineFactory.validate_augmentations(augmentations)
if result["valid"]:
    print("Configuration is valid")
else:
    print(f"Errors: {result['errors']}")
```

## Migration Guide

### From Manual Transforms

**Before:**

```python
transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])
```

**After:**

```python
augmentations = [
    ResizeDimensions(resize_dimensions=[224, 224]),
    NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
    NormalizationStd(normalization_std=[0.229, 0.224, 0.225])
]
transform = AugmentationPipelineFactory.create_training_pipeline(augmentations)
```

### Benefits of Migration

- **Database-driven configuration**
- **Training/inference consistency**
- **Automatic artifact saving**
- **Validation and error handling**
- **Comprehensive logging**
