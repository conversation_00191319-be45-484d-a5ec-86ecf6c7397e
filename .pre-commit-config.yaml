# Define consistent environment variables for all hooks
default_language_version:
  python: python3.12

# Set consistent environment variables for all hooks
default_stages: [pre-commit, pre-push]

repos:
  - repo: local
    hooks:
      - id: black
        name: black
        entry: .env/bin/black
        language: system
        types: [python]

      - id: flake8
        name: flake8
        entry: .env/bin/flake8
        language: system
        types: [python]

      - id: pylint
        name: pylint
        entry: .env/bin/pylint
        language: system
        types: [python]
        exclude: ^setup\.py$  # Exclude setup.py from pylint
        args:
          - --disable=R0903

      - id: pytest-fast
        name: pytest (fast)
        entry: env PYTHONPATH=.:src .env/bin/python -m pytest tests/ --tb=short -q -n auto --disable-warnings
        language: system
        types: [python]
        pass_filenames: false
        # Fast parallel test execution optimized for pre-commit hooks (~9 seconds)
        additional_dependencies: []
        always_run: true
        verbose: false  # Keep output minimal for faster commits
        stages: [pre-commit]

      - id: pytest-comprehensive
        name: pytest (comprehensive)
        entry: env PYTHONPATH=.:src .env/bin/python -m pytest tests/ --tb=short -v -n auto
        language: system
        types: [python]
        pass_filenames: false
        # More verbose test execution for pre-push hooks
        additional_dependencies: []
        always_run: true
        verbose: true
        stages: [pre-push]
