[pytest]
testpaths = tests
norecursedirs = tests/manual
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
pythonpath = src
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
markers =
    asyncio: mark a test as an asyncio test
    integration: mark a test as an integration test
    slow: mark a test as slow (skipped in fast mode)
    fast: mark a test as fast (always run)
    parallel: mark a test as safe for parallel execution
