"""
Global PyTest configuration file.
This file contains global fixtures and configuration that apply to all tests.
Component-specific fixtures are in their respective fixture files.
"""

import sys
from pathlib import Path

# Add the project root and src directory to Python path before any imports
project_root = Path(__file__).parent.parent.resolve()
src_path = project_root / "src"

# Add paths to sys.path if they're not already there
paths_to_add = [
    str(project_root),  # Project root
    str(src_path),  # src directory
]

# Add each path to the front of sys.path if not already present
for path in paths_to_add:
    if path not in sys.path:
        sys.path.insert(0, path)
        print(f"Added to sys.path: {path}")

# Register fixture modules to make them available to all tests
pytest_plugins = [
    "tests.fixtures.api",
    "tests.fixtures.async_utils",
    "tests.fixtures.cache",
    "tests.fixtures.data",
    "tests.fixtures.database",
    "tests.fixtures.job_callbacks",
    "tests.fixtures.mocks",
    "tests.fixtures.model",
    "tests.fixtures.paths",
    "tests.fixtures.performance",
    "tests.fixtures.trainer",
]


def pytest_configure(config):
    """Register custom markers for the mocking system."""
    config.addinivalue_line("markers", "mock_api: mock API modules")
    config.addinivalue_line("markers", "mock_database: mock database modules")
    config.addinivalue_line("markers", "mock_ml: mock machine learning modules")
    config.addinivalue_line("markers", "mock_all: mock all modules")
    config.addinivalue_line(
        "markers", "auto_mock: automatically mock modules for tests"
    )
    config.addinivalue_line("markers", "no_mocks: disable automatic mocking")
