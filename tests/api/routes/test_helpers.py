"""
Helper functions for API route tests.

This module provides common functions used across different test modules,
ensuring consistency and reducing duplication.
"""

from tests.fixtures.data import (
    get_sample_dataset_data,
    get_sample_model_run_data,
    get_sample_model_version_data,
)


def get_test_data():
    """
    Get common test data used in multiple test files.

    Returns:
        tuple: A tuple containing (sample_dataset, sample_model_version, sample_model_run)
    """
    sample_dataset = get_sample_dataset_data()
    sample_model_version = get_sample_model_version_data()
    sample_model_run = get_sample_model_run_data()
    return sample_dataset, sample_model_version, sample_model_run


def get_mock_training_data():
    """
    Get mock training data response for tests.

    Returns:
        dict: A dictionary with the structure expected from TrainingDataService.get_training_data
    """
    sample_dataset, sample_model_version, sample_model_run = get_test_data()

    # Make a copy of the sample_model_version to avoid modifying the original
    model_version = sample_model_version.copy()
    # Ensure the model_version has the name field set correctly
    model_version["name"] = f"Version {model_version['version_number']}"

    return {
        "model": {"uuid": "model-123", "name": "ResNet50"},
        "model_version": model_version,
        "dataset": sample_dataset,
        "model_run": sample_model_run,
        "training_parameters": {"batch_size": 32, "epochs": 10},
        "dataset_metadata": {"images_count": 1000},
    }


def verify_train_response(response_data):
    """
    Verify the response from the train endpoint.

    Args:
        response_data: The JSON response data from the train endpoint

    Returns:
        None
    """
    # Get sample data for assertions
    sample_dataset, sample_model_version, sample_model_run = get_test_data()

    # Check response structure
    assert response_data["status"] == "training_initiated"
    assert response_data["experiment_uuid"] == sample_model_run["experiment_uuid"]

    # Check model version data
    assert response_data["model_version"]["uuid"] == sample_model_version["uuid"]
    assert (
        response_data["model_version"]["name"]
        == f"Version {sample_model_version['version_number']}"
    )

    # Check dataset data
    assert response_data["dataset"]["uuid"] == sample_dataset["uuid"]
    assert response_data["dataset"]["name"] == sample_dataset["name"]
