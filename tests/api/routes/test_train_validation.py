"""
Validation tests for the train endpoint.
"""

from unittest.mock import patch

from fastapi import status

from database.services.model_metadata_service import ModelMetadataError


def test_train_endpoint_empty_model_run_uuid(test_client):
    """
    Test the train endpoint with an empty model_run_uuid.

    This test verifies that the train endpoint returns a 422 Unprocessable Entity
    response when the model_run_uuid is empty.
    """
    # Define test data with empty model_run_uuid
    payload = {"model_run_uuid": ""}

    # Make request
    with patch("api.utils.get_supabase_profile", return_value="development"):
        response = test_client.post(
            "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
        )

    # Verify response - the API now returns 400 for empty model_run_uuid
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert "detail" in response_data


def test_train_endpoint_missing_model_run(test_client, monkeypatch):
    """
    Test the train endpoint with a model_run_uuid that doesn't exist.

    This test verifies that the train endpoint returns a 404 Not Found response
    when the specified model_run doesn't exist.
    """

    # Mock the ModelMetadataService.get_model_metadata method to raise a ModelMetadataError
    async def mock_get_model_metadata(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        raise ModelMetadataError("Model run not found with UUID: nonexistent-uuid")

    monkeypatch.setattr(
        "database.services.model_metadata_service."
        "ModelMetadataService.get_model_metadata",
        mock_get_model_metadata,
    )

    # Define test data
    payload = {"model_run_uuid": "nonexistent-uuid"}

    # Make request
    with patch("api.utils.get_supabase_profile", return_value="development"):
        response = test_client.post(
            "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
        )

    # Verify response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert "detail" in response_data
    assert "not found" in response_data["detail"].lower()
    assert "nonexistent-uuid" in response_data["detail"]
