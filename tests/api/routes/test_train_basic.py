"""
Basic tests for the train endpoint.
"""

from unittest.mock import patch

from fastapi import status

from tests.api.routes.test_helpers import get_mock_training_data, verify_train_response


def test_train_endpoint_basic(test_client, monkeypatch):
    """
    Basic test for the train endpoint.

    This test verifies that the train endpoint works with the basic fixtures.
    """
    # Sample data is provided via fixtures

    # Mock the TrainingDataService.get_training_data method
    async def mock_get_training_data(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        return get_mock_training_data()

    monkeypatch.setattr(
        "database.services.training_data_service.TrainingDataService.get_training_data",
        mock_get_training_data,
    )

    # Define test data
    payload = {"model_run_uuid": "f72599b5-942e-4877-bbea-1717244908da"}

    # Make request
    with patch("api.utils.get_supabase_profile", return_value="development"):
        response = test_client.post(
            "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
        )

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    # Verify the response using the helper function
    verify_train_response(response_data)
