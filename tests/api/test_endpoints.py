"""
Tests for the API endpoints.
"""

import sys
from pathlib import Path

from fastapi.testclient import TestClient

from src.api.main import app  # Use absolute import from src

print("DEBUG: Python Path in test_api.py:", sys.path)
print("DEBUG: Current directory:", Path.cwd())


client = TestClient(app)


def test_root_endpoint():
    """Test the root endpoint returns correct response."""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {
        "status": "ok",
        "message": "Coiny Classifier API is running",
    }
