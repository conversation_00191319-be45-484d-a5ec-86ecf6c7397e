"""
Tests for the Supabase profile middleware.
"""

from unittest.mock import <PERSON><PERSON><PERSON>, patch

import pytest
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.testclient import TestClient

from api.config import settings
from api.middleware.supabase_profile import SupabaseProfileMiddleware


@pytest.fixture(name="base_app")
def fixture_base_app():
    """Create a base test FastAPI app.

    This fixture provides a clean FastAPI instance for basic testing.
    """
    app = FastAPI()
    return app


@pytest.fixture(name="app_with_middleware")
def fixture_app_with_middleware():
    """Create a test FastAPI app with the SupabaseProfileMiddleware.

    This fixture sets up a FastAPI app with:
    1. The SupabaseProfileMiddleware installed
    2. A test endpoint that returns the current Supabase profile
    """
    app = FastAPI()
    app.add_middleware(SupabaseProfileMiddleware)

    @app.get("/test-profile")
    async def test_profile(request: Request):
        # Use settings from request state if available, otherwise use global settings
        current_settings = getattr(request.state, "settings", settings)
        return {"profile": current_settings.SUPABASE_PROFILE}

    return app


@pytest.fixture(name="test_client")
def fixture_test_client(app_with_middleware):
    """Create a TestClient for the test app.

    This fixture provides a configured TestClient that can be used to make
    requests to our test app with the Supabase middleware installed.
    """
    return TestClient(app_with_middleware)


def test_default_profile(test_client):
    """Test that the default profile is 'development' when no header is provided.

    This test verifies the middleware's default behavior when no profile
    is explicitly specified in the request headers.
    """
    response = test_client.get("/test-profile")
    assert response.status_code == 200
    assert response.json() == {"profile": "development"}


def test_development_profile(test_client):
    """Test that the profile is set to 'development' when the header is provided.

    This test ensures that explicitly requesting the development profile
    works correctly through the X-Supabase-Profile header.
    """
    response = test_client.get(
        "/test-profile", headers={"X-Supabase-Profile": "development"}
    )
    assert response.status_code == 200
    assert response.json() == {"profile": "development"}


def test_staging_profile(test_client):
    """Test that the profile is set to 'staging' when the header is provided.

    This test verifies that:
    1. The middleware correctly handles the staging profile header
    2. The settings are properly updated with the new profile
    3. The response reflects the changed profile
    """
    with patch("api.middleware.supabase_profile.settings") as mock_settings:
        new_settings = MagicMock()
        new_settings.SUPABASE_PROFILE = "staging"
        mock_settings.model_copy.return_value = new_settings

        response = test_client.get(
            "/test-profile", headers={"X-Supabase-Profile": "staging"}
        )

        assert response.status_code == 200
        assert response.json() == {"profile": "staging"}


def test_production_profile(test_client):
    """Test that the profile is set to 'production' when the header is provided.

    Similar to the staging test, this verifies the production profile:
    1. Middleware handles production profile header
    2. Settings are updated accordingly
    3. Response shows production profile
    """
    with patch("api.middleware.supabase_profile.settings") as mock_settings:
        new_settings = MagicMock()
        new_settings.SUPABASE_PROFILE = "production"
        mock_settings.model_copy.return_value = new_settings

        response = test_client.get(
            "/test-profile", headers={"X-Supabase-Profile": "production"}
        )

        assert response.status_code == 200
        assert response.json() == {"profile": "production"}


def test_invalid_profile(test_client):
    """Test that an invalid profile is ignored and defaults to 'development'.

    This test ensures the middleware gracefully handles invalid profiles by:
    1. Sending an invalid profile in the header
    2. Verifying it falls back to the default 'development' profile
    """
    response = test_client.get(
        "/test-profile", headers={"X-Supabase-Profile": "invalid"}
    )
    assert response.status_code == 200
    assert response.json() == {"profile": "development"}


def test_middleware_updates_settings():
    """Test that the middleware updates the settings with the profile from the header.

    This test verifies the internal behavior of the middleware:
    1. Creates a new FastAPI app with the middleware
    2. Mocks the settings module
    3. Verifies that the settings are updated with the correct profile
    """
    app = FastAPI()

    with patch("api.middleware.supabase_profile.settings") as mock_settings:
        new_settings = MagicMock()
        mock_settings.model_copy.return_value = new_settings

        app.add_middleware(SupabaseProfileMiddleware)
        test_client = TestClient(app)

        test_client.get("/", headers={"X-Supabase-Profile": "production"})

        mock_settings.model_copy.assert_called_once_with(
            update={"SUPABASE_PROFILE": "production"}
        )


@pytest.mark.parametrize(
    "profile,expected_url,expected_key",
    [
        ("development", "https://dev.supabase.co", "dev-key"),
        ("staging", "https://staging.supabase.co", "staging-key"),
    ],
)
def test_supabase_client_initialization(profile, expected_url, expected_key):
    """Test that the Supabase client is initialized with the correct URL and key.

    This parametrized test verifies that:
    1. Different profiles use the correct Supabase credentials
    2. The middleware properly initializes the client with these credentials
    3. The response contains the expected profile-specific values

    Parameters:
        profile: The Supabase profile to test ('development' or 'staging')
        expected_url: The expected Supabase URL for this profile
        expected_key: The expected API key for this profile
    """
    app = FastAPI()

    mock_settings = MagicMock()
    mock_settings.SUPABASE_PROFILE = profile

    mock_credentials = MagicMock()
    mock_credentials.url = expected_url
    mock_credentials.anon_key = expected_key

    mock_settings.active_supabase_credentials = mock_credentials

    @app.get("/test-credentials")
    async def test_credentials():
        return {
            "profile": profile,
            "url": mock_credentials.url,
            "key": mock_credentials.anon_key,
        }

    with patch("api.middleware.supabase_profile.settings", mock_settings):
        app.add_middleware(SupabaseProfileMiddleware)
        test_client = TestClient(app)

        response = test_client.get(
            "/test-credentials", headers={"X-Supabase-Profile": profile}
        )

        assert response.status_code == 200
        response_data = response.json()

        assert (
            response_data["profile"] == profile
        ), f"Expected profile {profile}, got {response_data['profile']}"
        assert (
            response_data["url"] == expected_url
        ), f"Expected URL {expected_url}, got {response_data['url']}"
        assert (
            response_data["key"] == expected_key
        ), f"Expected key {expected_key}, got {response_data['key']}"
