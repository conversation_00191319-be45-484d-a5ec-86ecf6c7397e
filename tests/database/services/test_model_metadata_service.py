"""
Tests for the ModelMetadataService.
"""

from uuid import uuid4

import pytest

from src.database.services.model_metadata_service import (
    ModelMetadataError,
    ModelMetadataService,
)


@pytest.mark.auto_mock
class TestModelMetadataService:
    """Tests for the ModelMetadataService."""

    @pytest.mark.asyncio
    async def test_get_model_metadata_with_foreign_tables(self, mocker):
        """Test get_model_metadata using foreign_tables for efficient data fetching."""
        # Mock data
        model_run_uuid = str(uuid4())
        model_version_uuid = str(uuid4())
        model_uuid = str(uuid4())

        # Mock the model data structure that would be returned with foreign tables
        mock_model = {
            "uuid": model_uuid,
            "name": "Test Model",
            "architecture": "ResNet",
            "description": "Test model for testing",
            "created_at": "2025-06-04T12:00:00Z",
        }

        mock_model_version = {
            "uuid": model_version_uuid,
            "model_uuid": model_uuid,
            "version_number": 1,
            "parameters": {"layers": 5},
            "created_at": "2025-06-04T12:30:00Z",
            "models": [mock_model],  # Foreign table relationship
        }

        mock_model_run = {
            "uuid": model_run_uuid,
            "model_version_uuid": model_version_uuid,
            "dataset_uuid": str(uuid4()),
            "status": "pending",
            "created_at": "2025-06-04T13:00:00Z",
            "model_versions": [mock_model_version],  # Foreign table relationship
        }

        # Mock the fetch_data function to return our mock data
        mock_fetch_data = mocker.patch(
            "src.database.services.model_metadata_service.fetch_data",
            return_value=[mock_model_run],
        )

        # Call the method under test
        result = await ModelMetadataService.get_model_metadata(
            model_run_uuid=model_run_uuid, profile="development"
        )

        # Verify fetch_data was called with the correct parameters
        mock_fetch_data.assert_called_once()
        args, kwargs = mock_fetch_data.call_args
        assert args[0] == "model_runs"
        assert "select" in args[1]
        assert args[1]["select"] == "*, model_versions(*, models(*))"
        assert kwargs["profile"] == "development"

        # Verify the result contains the expected data
        assert result["model_run"] == mock_model_run
        assert result["model_version"] == mock_model_version
        assert result["model"] == mock_model

    @pytest.mark.asyncio
    async def test_get_model_metadata_no_model_run(self, mocker):
        """Test get_model_metadata when model run is not found."""
        # Mock fetch_data to return an empty list
        mocker.patch(
            "src.database.services.model_metadata_service.fetch_data",
            return_value=[],
        )

        # Call the method and expect an exception
        with pytest.raises(ModelMetadataError, match="Model run not found"):
            await ModelMetadataService.get_model_metadata(
                model_run_uuid=str(uuid4()), profile="development"
            )

    @pytest.mark.asyncio
    async def test_get_model_metadata_no_model_version(self, mocker):
        """Test get_model_metadata when model version is not found."""
        model_run_uuid = str(uuid4())

        # Mock model run with empty model_versions
        mock_model_run = {
            "uuid": model_run_uuid,
            "model_version_uuid": str(uuid4()),
            "model_versions": [],  # Empty model versions
        }

        mocker.patch(
            "src.database.services.model_metadata_service.fetch_data",
            return_value=[mock_model_run],
        )

        # Call the method and expect an exception
        with pytest.raises(ModelMetadataError, match="Model version not found"):
            await ModelMetadataService.get_model_metadata(
                model_run_uuid=model_run_uuid, profile="development"
            )

    @pytest.mark.asyncio
    async def test_get_model_metadata_no_model(self, mocker):
        """Test get_model_metadata when model is not found."""
        model_run_uuid = str(uuid4())
        model_version_uuid = str(uuid4())

        # Mock model version with empty models
        mock_model_version = {
            "uuid": model_version_uuid,
            "model_uuid": str(uuid4()),
            "models": [],  # Empty models
        }

        # Mock model run with model version that has no models
        mock_model_run = {
            "uuid": model_run_uuid,
            "model_version_uuid": model_version_uuid,
            "model_versions": [mock_model_version],
        }

        mocker.patch(
            "src.database.services.model_metadata_service.fetch_data",
            return_value=[mock_model_run],
        )

        # Call the method and expect an exception
        with pytest.raises(ModelMetadataError, match="Model not found"):
            await ModelMetadataService.get_model_metadata(
                model_run_uuid=model_run_uuid, profile="development"
            )
