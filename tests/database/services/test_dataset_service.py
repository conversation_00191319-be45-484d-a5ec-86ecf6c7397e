"""
Tests for the DatasetService class.
"""

from unittest.mock import patch

import pytest

from database.models.dataset_set import DatasetSet, SetType
from database.services.dataset_service import DatasetError, DatasetService
from tests.fixtures.data import (
    TEST_DATASET_UUID,
    get_sample_dataset_data,
    get_sample_dataset_sets,
)

# Apply the auto_mock marker to this module to use the centralized mocking system
pytestmark = [pytest.mark.auto_mock(categories=["ml"])]

# Using constants from tests.fixtures.data instead of defining them here


@pytest.fixture
def sample_dataset_sets_fixture():
    """Fixture for sample dataset sets with valid UUIDs."""
    # Use centralized function with custom UUIDs and values
    return get_sample_dataset_sets(
        count=2,
        custom_values_list=[
            {
                "uuid": "11111111-1111-1111-1111-111111111111",
                "set_type": SetType.TRAIN,
                "created_at": "2023-01-01T00:00:00Z",
                "images_reviews": [{"image_url": "http://example.com/image1.jpg"}],
            },
            {
                "uuid": "*************-2222-2222-************",
                "set_type": SetType.VALIDATION,
                "created_at": "2023-01-01T00:00:00Z",
                "images_reviews": [{"image_url": "http://example.com/image2.jpg"}],
            },
        ],
    )


class TestDatasetService:
    """Test cases for the DatasetService class."""

    def test_get_dataset_success(self):
        """Test successful dataset retrieval."""
        # Setup
        with patch("database.services.dataset_service.fetch_data") as mock_fetch:
            dataset_uuid = TEST_DATASET_UUID
            # Use centralized data function with custom values
            sample_data = get_sample_dataset_data(
                custom_values={
                    "uuid": dataset_uuid,
                    "name": "Test Dataset",
                    "images_count": 100,
                    "created_at": "2023-01-01T00:00:00Z",
                    "content_updated_at": "2023-01-01T00:00:00Z",
                }
            )
            mock_fetch.return_value = [sample_data]

            # Test
            result = DatasetService.get_dataset(dataset_uuid)

            # Assert
            assert result["uuid"] == dataset_uuid
            assert result["name"] == sample_data["name"]
            assert result["images_count"] == 100
            mock_fetch.assert_called_once_with(
                "datasets",
                {"filters": [{"column": "uuid", "value": dataset_uuid}]},
                profile=None,
            )

    def test_get_dataset_not_found(self):
        """Test dataset not found scenario."""
        with patch("database.services.dataset_service.fetch_data") as mock_fetch:
            non_existent_uuid = "00000000-0000-0000-0000-000000000000"
            mock_fetch.return_value = []

            with pytest.raises(
                DatasetError, match=f"Dataset not found with UUID: {non_existent_uuid}"
            ):
                DatasetService.get_dataset(non_existent_uuid)

            mock_fetch.assert_called_once_with(
                "datasets",
                {"filters": [{"column": "uuid", "value": non_existent_uuid}]},
                profile=None,
            )

    def test_get_dataset_sets_success(
        self, sample_dataset_sets_fixture
    ):  # pylint: disable=redefined-outer-name
        """Test successful dataset sets retrieval."""
        with patch("database.services.dataset_service.fetch_data") as mock_fetch:
            # Setup
            count_result = [{"count": len(sample_dataset_sets_fixture)}]
            # Return count first, then the actual data
            mock_fetch.side_effect = [count_result, sample_dataset_sets_fixture]

            # Test - get the generator and consume it
            dataset_sets = DatasetService.get_dataset_sets(
                TEST_DATASET_UUID, batch_size=2
            )
            # Convert generator to list of batches
            # Each batch is a list of DatasetSet objects
            all_batches = list(dataset_sets)

            # Flatten the batches
            all_sets = [item for batch in all_batches for item in batch]

            # Assert
            assert len(all_sets) == len(sample_dataset_sets_fixture)
            assert all(isinstance(ds, DatasetSet) for ds in all_sets)
            # Check that all dataset_uuid values match TEST_DATASET_UUID
            assert all(str(ds.dataset_uuid) == TEST_DATASET_UUID for ds in all_sets)

            # Verify fetch was called twice (count + data)
            assert mock_fetch.call_count == 2

    def test_get_dataset_sets_with_type_filter(
        self, sample_dataset_sets_fixture
    ):  # pylint: disable=redefined-outer-name
        """Test dataset sets retrieval with type filter."""
        with patch("database.services.dataset_service.fetch_data") as mock_fetch:
            # Setup
            filtered_sets = [
                s for s in sample_dataset_sets_fixture if s["set_type"] == SetType.TRAIN
            ]
            count_result = [{"count": len(filtered_sets)}]
            mock_fetch.side_effect = [count_result, filtered_sets]

            # Test - get the generator and consume it
            dataset_sets = DatasetService.get_dataset_sets(
                TEST_DATASET_UUID, set_type=SetType.TRAIN, batch_size=10
            )
            # Convert generator to list of batches
            all_batches = list(dataset_sets)
            # Flatten the batches
            all_sets = [item for batch in all_batches for item in batch]

            # Assert
            assert len(all_sets) == len(filtered_sets)
            assert all(isinstance(ds, DatasetSet) for ds in all_sets)
            assert all(ds.set_type == SetType.TRAIN for ds in all_sets)
            assert mock_fetch.call_count == 2

    def test_get_dataset_sets_empty_result(self):
        """Test dataset sets retrieval when no sets are found."""
        with patch("database.services.dataset_service.fetch_data") as mock_fetch:
            # Setup
            dataset_uuid = "00000000-0000-0000-0000-000000000000"
            count_result = [{"count": 0}]
            mock_fetch.return_value = count_result

            # Test
            dataset_sets = DatasetService.get_dataset_sets(
                dataset_uuid, batch_size=1000
            )
            result = list(dataset_sets)  # Consume the generator

            # Assert
            assert len(result) == 0
            mock_fetch.assert_called_once()  # Only count query should be made

    def test_get_dataset_sets_invalid_batch_size(self):
        """Test dataset sets retrieval with invalid batch size."""
        # Test with batch_size = 0
        with pytest.raises(ValueError, match="Batch size must be a positive integer"):
            # The error is raised when we try to consume the generator
            next(DatasetService.get_dataset_sets(TEST_DATASET_UUID, batch_size=0))

        # Test with batch_size = -1
        with pytest.raises(ValueError, match="Batch size must be a positive integer"):
            # The error is raised when we try to consume the generator
            next(DatasetService.get_dataset_sets(TEST_DATASET_UUID, batch_size=-1))
