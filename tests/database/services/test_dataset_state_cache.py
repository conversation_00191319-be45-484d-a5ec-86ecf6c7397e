"""
Tests for the DatasetStateCacheService class.
"""

from datetime import UTC, datetime
from uuid import uuid4

from database.services.dataset_state_cache import (
    DatasetCacheState,
    DatasetStateCacheService,
)
from tests.fixtures.cache import (
    perform_cache_invalidation_test,
    verify_cache_invalidation,
    verify_cache_validity,
)


class TestDatasetStateCacheService:
    """Test cases for the DatasetStateCacheService class."""

    def setup_method(self):
        """Clear cache before each test."""
        DatasetStateCacheService.clear_cache()

    def teardown_method(self):
        """Clear cache after each test."""
        DatasetStateCacheService.clear_cache()

    def test_generate_cache_key_with_timestamp(self):
        """Test cache key generation with timestamp."""
        dataset_uuid = str(uuid4())
        timestamp = datetime(2025, 6, 28, 10, 0, 0)

        cache_key = DatasetStateCacheService.generate_cache_key(dataset_uuid, timestamp)

        expected_key = f"{dataset_uuid}_{timestamp.isoformat()}"
        assert cache_key == expected_key

    def test_generate_cache_key_without_timestamp(self):
        """Test cache key generation without timestamp."""
        dataset_uuid = str(uuid4())

        cache_key = DatasetStateCacheService.generate_cache_key(dataset_uuid, None)

        expected_key = f"{dataset_uuid}_none"
        assert cache_key == expected_key

    def test_is_cache_valid_empty_cache(self):
        """Test cache validity check with empty cache."""
        dataset_uuid = str(uuid4())
        timestamp = datetime.now(UTC)

        is_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)

        assert not is_valid

    def test_is_cache_valid_with_prepared_state(self, cache_prepared_state):
        """Test cache validity check with prepared state."""
        dataset_uuid, timestamp = cache_prepared_state

        is_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)

        assert is_valid

    def test_is_cache_valid_with_unprepared_state(self, cache_unprepared_state):
        """Test cache validity check with unprepared state."""
        dataset_uuid, timestamp = cache_unprepared_state

        is_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)

        assert not is_valid

    def test_get_cached_state_exists(self):
        """Test getting cached state when it exists."""
        dataset_uuid = str(uuid4())
        timestamp = datetime.now(UTC)

        # Set cache state
        original_state = DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid,
            content_updated_at=timestamp,
            is_prepared=True,
        )

        cached_state = DatasetStateCacheService.get_cached_state(
            dataset_uuid, timestamp
        )

        assert cached_state is not None
        assert cached_state.dataset_uuid == str(dataset_uuid)
        assert cached_state.content_updated_at == timestamp
        assert cached_state.is_prepared is True
        assert cached_state.cache_key == original_state.cache_key

    def test_get_cached_state_not_exists(self):
        """Test getting cached state when it doesn't exist."""
        dataset_uuid = str(uuid4())
        timestamp = datetime.now(UTC)

        cached_state = DatasetStateCacheService.get_cached_state(
            dataset_uuid, timestamp
        )

        assert cached_state is None

    def test_set_cache_state(self):
        """Test setting cache state."""
        dataset_uuid = str(uuid4())
        timestamp = datetime.now(UTC)

        cache_state = DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid,
            content_updated_at=timestamp,
            is_prepared=True,
        )

        assert isinstance(cache_state, DatasetCacheState)
        assert cache_state.dataset_uuid == str(dataset_uuid)
        assert cache_state.content_updated_at == timestamp
        assert cache_state.is_prepared is True
        assert str(cache_state.cache_key).startswith(str(dataset_uuid))

    def test_invalidate_dataset_cache_single_entry(
        self, cache_dataset_uuid, cache_timestamp
    ):
        """Test invalidating cache for a dataset with single entry."""
        invalidated_count = perform_cache_invalidation_test(
            cache_dataset_uuid, cache_timestamp
        )

        assert invalidated_count == 1

    def test_invalidate_dataset_cache_multiple_entries(
        self, cache_multiple_entries_setup
    ):
        """Test invalidating cache for a dataset with multiple entries."""
        dataset_uuid, timestamp1, timestamp2 = cache_multiple_entries_setup

        # Verify both caches exist
        verify_cache_validity(dataset_uuid, timestamp1, True)
        verify_cache_validity(dataset_uuid, timestamp2, True)

        # Invalidate cache
        invalidated_count = DatasetStateCacheService.invalidate_dataset_cache(
            dataset_uuid
        )

        assert invalidated_count == 2
        verify_cache_validity(dataset_uuid, timestamp1, False)
        verify_cache_validity(dataset_uuid, timestamp2, False)

    def test_invalidate_dataset_cache_no_entries(self):
        """Test invalidating cache for a dataset with no entries."""
        dataset_uuid = str(uuid4())

        invalidated_count = DatasetStateCacheService.invalidate_dataset_cache(
            dataset_uuid
        )

        assert invalidated_count == 0

    def test_clear_cache(self):
        """Test clearing all cache entries."""
        dataset_uuid1 = str(uuid4())
        dataset_uuid2 = str(uuid4())
        timestamp = datetime.now(UTC)

        # Set multiple cache states
        DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid1,
            content_updated_at=timestamp,
            is_prepared=True,
        )
        DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid2,
            content_updated_at=timestamp,
            is_prepared=True,
        )

        # Verify caches exist
        assert DatasetStateCacheService.is_cache_valid(dataset_uuid1, timestamp)
        assert DatasetStateCacheService.is_cache_valid(dataset_uuid2, timestamp)

        # Clear cache
        cleared_count = DatasetStateCacheService.clear_cache()

        assert cleared_count == 2
        assert not DatasetStateCacheService.is_cache_valid(dataset_uuid1, timestamp)
        assert not DatasetStateCacheService.is_cache_valid(dataset_uuid2, timestamp)

    def test_get_cache_stats(self):
        """Test getting cache statistics."""
        dataset_uuid1 = str(uuid4())
        dataset_uuid2 = str(uuid4())
        timestamp = datetime.now(UTC)

        # Initially empty cache
        stats = DatasetStateCacheService.get_cache_stats()
        assert stats["total_entries"] == 0
        assert stats["prepared_entries"] == 0
        assert stats["unprepared_entries"] == 0

        # Add prepared and unprepared entries
        DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid1,
            content_updated_at=timestamp,
            is_prepared=True,
        )
        DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid2,
            content_updated_at=timestamp,
            is_prepared=False,
        )

        stats = DatasetStateCacheService.get_cache_stats()
        assert stats["total_entries"] == 2
        assert stats["prepared_entries"] == 1
        assert stats["unprepared_entries"] == 1

    def test_cache_invalidation_on_content_update(self, cache_invalidation_setup):
        """Test that cache is invalidated when content_updated_at changes."""
        dataset_uuid, old_timestamp, new_timestamp = cache_invalidation_setup

        # Verify cache invalidation behavior
        verify_cache_invalidation(dataset_uuid, old_timestamp, new_timestamp)
