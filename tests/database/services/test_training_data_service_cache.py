"""
Tests for TrainingDataService cache invalidation functionality.
"""

import asyncio
from datetime import UTC, datetime
from unittest.mock import patch
from uuid import uuid4

from database.services.dataset_state_cache import DatasetStateCacheService
from database.services.training_data_service import TrainingDataService
from tests.fixtures.data import (
    get_sample_dataset_data,
    get_sample_model_data,
    get_sample_model_run_data,
    get_sample_model_version_data,
)


class TestTrainingDataServiceCache:
    """Test cases for TrainingDataService cache functionality."""

    def setup_method(self):
        """Clear cache before each test."""
        DatasetStateCacheService.clear_cache()

    def teardown_method(self):
        """Clear cache after each test."""
        DatasetStateCacheService.clear_cache()

    def test_check_dataset_cache_state_cache_miss(self):
        """Test cache state check when cache is empty (cache miss)."""
        dataset_uuid = str(uuid4())
        timestamp = datetime.now(UTC)

        cache_state_info = TrainingDataService.check_dataset_cache_state(
            dataset_uuid, timestamp
        )

        assert cache_state_info["is_cache_hit"] is False
        assert cache_state_info["dataset_preparation_skipped"] is <PERSON>alse
        assert cache_state_info["cache_key"].startswith(dataset_uuid)
        assert cache_state_info["cached_at"] is None  # No cached state on cache miss

    def test_check_dataset_cache_state_cache_hit(self, cache_prepared_state):
        """Test cache state check when cache exists (cache hit)."""
        dataset_uuid, timestamp = cache_prepared_state

        cache_state_info = TrainingDataService.check_dataset_cache_state(
            dataset_uuid, timestamp
        )

        assert cache_state_info["is_cache_hit"] is True
        assert cache_state_info["dataset_preparation_skipped"] is True
        assert cache_state_info["cache_key"].startswith(dataset_uuid)
        assert cache_state_info["cached_at"] is not None

    def test_should_skip_dataset_sets_fetching_with_cache_hit(self):
        """Test that dataset_sets fetching is skipped when cache indicates it should be."""
        training_data = {
            "cache_state": {
                "dataset_preparation_skipped": True,
                "is_cache_hit": True,
            }
        }

        should_skip = TrainingDataService.should_skip_dataset_sets_fetching(
            training_data
        )

        assert should_skip is True

    def test_should_skip_dataset_sets_fetching_with_cache_miss(self):
        """Test that dataset_sets fetching is not skipped when cache indicates it shouldn't be."""
        training_data = {
            "cache_state": {
                "dataset_preparation_skipped": False,
                "is_cache_hit": False,
            }
        }

        should_skip = TrainingDataService.should_skip_dataset_sets_fetching(
            training_data
        )

        assert should_skip is False

    def test_should_skip_dataset_sets_fetching_no_cache_state(self):
        """Test that dataset_sets fetching is not skipped when no cache state exists."""
        training_data = {}

        should_skip = TrainingDataService.should_skip_dataset_sets_fetching(
            training_data
        )

        assert should_skip is False

    def test_mark_dataset_preparation_complete(self):
        """Test marking dataset preparation as complete."""
        dataset_uuid = str(uuid4())
        timestamp = datetime.now(UTC)

        # Initially cache should be invalid
        assert not DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)

        # Mark as complete
        TrainingDataService.mark_dataset_preparation_complete(dataset_uuid, timestamp)

        # Now cache should be valid
        assert DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)

    def test_invalidate_dataset_cache(self, cache_dataset_uuid, cache_timestamp):
        """Test invalidating dataset cache."""
        # Set up cache state first
        DatasetStateCacheService.set_cache_state(
            dataset_uuid=cache_dataset_uuid,
            content_updated_at=cache_timestamp,
            is_prepared=True,
        )

        # Verify cache exists
        assert DatasetStateCacheService.is_cache_valid(
            cache_dataset_uuid, cache_timestamp
        )

        # Use TrainingDataService method to invalidate
        invalidated_count = TrainingDataService.invalidate_dataset_cache(
            cache_dataset_uuid
        )

        assert invalidated_count == 1
        assert not DatasetStateCacheService.is_cache_valid(
            cache_dataset_uuid, cache_timestamp
        )

    @patch(
        "database.services.training_data_service.ModelMetadataService.get_model_metadata"
    )
    @patch("database.services.training_data_service.DatasetService.get_dataset")
    def test_get_training_data_includes_cache_state(
        self, mock_get_dataset, mock_get_metadata
    ):
        """Test that get_training_data includes cache state information."""
        # Setup test data
        dataset_uuid = str(uuid4())
        model_run_uuid = str(uuid4())
        timestamp = datetime.now(UTC)

        dataset_data = get_sample_dataset_data(
            custom_values={
                "uuid": dataset_uuid,
                "content_updated_at": timestamp,
            }
        )

        model_metadata = {
            "model": get_sample_model_data(),
            "model_version": get_sample_model_version_data(),
            "model_run": get_sample_model_run_data(
                custom_values={"dataset_uuid": dataset_uuid}
            ),
        }

        # Mock the service calls
        async def mock_get_metadata_async(*_args, **_kwargs):
            return model_metadata

        async def mock_get_dataset_async(*_args, **_kwargs):
            return dataset_data

        mock_get_metadata.side_effect = mock_get_metadata_async
        mock_get_dataset.side_effect = mock_get_dataset_async

        # Get training data
        training_data = asyncio.run(
            TrainingDataService.get_training_data(model_run_uuid)
        )

        # Verify cache state is included
        assert "cache_state" in training_data
        cache_state = training_data["cache_state"]
        assert "cache_key" in cache_state
        assert "is_cache_hit" in cache_state
        assert "dataset_preparation_skipped" in cache_state
        assert cache_state["cache_key"].startswith(dataset_uuid)

    @patch(
        "database.services.training_data_service.ModelMetadataService.get_model_metadata"
    )
    @patch("database.services.training_data_service.DatasetService.get_dataset")
    def test_get_training_data_cache_hit_scenario(
        self, mock_get_dataset, mock_get_metadata
    ):
        """Test get_training_data with cache hit scenario."""
        # Setup test data
        dataset_uuid = str(uuid4())
        model_run_uuid = str(uuid4())
        timestamp = datetime.now(UTC)

        # Pre-populate cache
        DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid,
            content_updated_at=timestamp,
            is_prepared=True,
        )

        dataset_data = get_sample_dataset_data(
            custom_values={
                "uuid": dataset_uuid,
                "content_updated_at": timestamp,
            }
        )

        model_metadata = {
            "model": get_sample_model_data(),
            "model_version": get_sample_model_version_data(),
            "model_run": get_sample_model_run_data(
                custom_values={"dataset_uuid": dataset_uuid}
            ),
        }

        # Mock the service calls
        async def mock_get_metadata_async(*_args, **_kwargs):
            return model_metadata

        async def mock_get_dataset_async(*_args, **_kwargs):
            return dataset_data

        mock_get_metadata.side_effect = mock_get_metadata_async
        mock_get_dataset.side_effect = mock_get_dataset_async

        # Get training data
        training_data = asyncio.run(
            TrainingDataService.get_training_data(model_run_uuid)
        )

        # Verify cache hit
        cache_state = training_data["cache_state"]
        assert cache_state["is_cache_hit"] is True
        assert cache_state["dataset_preparation_skipped"] is True

    def test_cache_invalidation_on_content_update(self, cache_invalidation_setup):
        """Test that cache is properly invalidated when content_updated_at changes."""
        dataset_uuid, old_timestamp, new_timestamp = cache_invalidation_setup

        # Check cache state with old timestamp (should be hit)
        cache_state_old = TrainingDataService.check_dataset_cache_state(
            dataset_uuid, old_timestamp
        )
        assert cache_state_old["is_cache_hit"] is True

        # Check cache state with new timestamp (should be miss)
        cache_state_new = TrainingDataService.check_dataset_cache_state(
            dataset_uuid, new_timestamp
        )
        assert cache_state_new["is_cache_hit"] is False
        assert cache_state_new["cache_key"] != cache_state_old["cache_key"]

    def test_cache_key_format_consistency(self):
        """Test that cache keys are generated consistently."""
        dataset_uuid = str(uuid4())
        timestamp = datetime(2025, 6, 28, 10, 0, 0)

        # Generate cache key multiple times
        key1 = DatasetStateCacheService.generate_cache_key(dataset_uuid, timestamp)
        key2 = DatasetStateCacheService.generate_cache_key(dataset_uuid, timestamp)

        assert key1 == key2
        assert key1 == f"{dataset_uuid}_{timestamp.isoformat()}"

    def test_cache_state_with_none_timestamp(self):
        """Test cache state handling when content_updated_at is None."""
        dataset_uuid = str(uuid4())

        cache_state_info = TrainingDataService.check_dataset_cache_state(
            dataset_uuid, None
        )

        assert cache_state_info["cache_key"] == f"{dataset_uuid}_none"
        assert cache_state_info["is_cache_hit"] is False
