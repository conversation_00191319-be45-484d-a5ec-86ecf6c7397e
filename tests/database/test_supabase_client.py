"""
Tests for the Supabase client functionality.
"""

from unittest.mock import MagicMock, patch

import pytest

from api.config import settings
from database.supabase_client import (
    fetch_data,
    fetch_model,
    fetch_model_and_version,
    fetch_model_run,
    fetch_model_version,
    get_fresh_supabase_client,
    get_supabase_client,
)
from tests.fixtures.data import get_sample_model_run_data


@pytest.fixture(name="supabase_mock")
def fixture_supabase_client():
    """Mock Supabase client for testing."""
    mock_client = MagicMock()
    mock_client.table.return_value.select.return_value.execute.return_value.data = [
        {"id": 1, "name": "Test Dataset"}
    ]
    return mock_client


@pytest.fixture(name="mock_fetch_client")
def fixture_mock_fetch_client():
    """Create a higher-level mock for fetch_data function that focuses on behavior.

    This fixture mocks the Supabase client's behavior without tightly coupling to
    implementation details of the method chain.
    """
    with patch("database.supabase_client.get_supabase_client") as mock_get_client:
        # Create a client that will return predefined data when used
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client

        # Store test data that can be modified by tests
        mock_client.test_data = [{"id": 1, "name": "Test Dataset"}]

        # Create a function to set up the expected behavior
        def setup_mock_response(table_name, columns="*", filters=None, limit=None):
            # Configure the mock to return the test data
            mock_result = MagicMock()
            mock_result.data = mock_client.test_data

            # Track calls for verification
            mock_client.last_table = table_name
            mock_client.last_columns = columns
            mock_client.last_filters = filters
            mock_client.last_limit = limit

            # Set up the chain to return our controlled result
            mock_select = MagicMock()
            mock_select.execute.return_value = mock_result

            if filters:
                mock_select.eq.return_value = mock_select

            mock_table = MagicMock()
            mock_table.select.return_value = mock_select

            mock_client.table.return_value = mock_table

            return mock_client

        # Attach the setup function to the mock client
        mock_client.setup = setup_mock_response

        yield mock_client


@pytest.fixture(name="create_client_mock")
def fixture_create_client():
    """Mock the create_client function from supabase module."""
    with patch("database.supabase_client.create_client") as mock:
        # Return a new MagicMock instance for each call
        mock.side_effect = MagicMock
        yield mock


def test_get_supabase_client(create_client_mock):
    """Test that get_supabase_client creates a client with correct credentials."""
    # Call the function
    client = get_supabase_client()

    # Verify create_client was called with correct credentials
    credentials = settings.active_supabase_credentials
    create_client_mock.assert_called_once_with(credentials.url, credentials.anon_key)

    # Verify we got a client back
    assert client is not None


def test_get_supabase_client_caching(create_client_mock):
    """Test that get_supabase_client caches the client."""
    # First call should create a new client
    client1 = get_supabase_client()
    initial_call_count = create_client_mock.call_count

    # Second call should return cached client
    client2 = get_supabase_client()
    assert create_client_mock.call_count == initial_call_count
    assert client1 is client2


def test_get_fresh_supabase_client(create_client_mock):
    """Test that get_fresh_supabase_client clears the cache and creates a new client."""
    # Call get_supabase_client to cache a client
    client1 = get_supabase_client()

    # Reset the mock to clear call history
    create_client_mock.reset_mock()

    # Call get_fresh_supabase_client
    client2 = get_fresh_supabase_client()

    # Verify create_client was called again
    credentials = settings.active_supabase_credentials
    create_client_mock.assert_called_once_with(credentials.url, credentials.anon_key)

    # Verify we got a different client
    assert client1 is not client2


def test_fetch_data_basic(mock_fetch_client):
    """Test fetch_data with basic parameters.

    This test focuses on the behavior (inputs/outputs) rather than
    implementation details of how the Supabase client is used.
    """
    # Setup expected data
    expected_data = [{"id": 1, "name": "Test Dataset"}]
    mock_fetch_client.test_data = expected_data
    mock_fetch_client.setup("test_table")

    # Call the function
    result = fetch_data("test_table")

    # Verify the function was called with correct parameters
    assert mock_fetch_client.last_table == "test_table"

    # Verify we got the expected data back
    assert result == expected_data


def test_fetch_data_with_filters(mock_fetch_client):
    """Test fetch_data with filter parameters.

    This test focuses on the behavior rather than implementation details.
    """
    # Setup expected data
    expected_data = [{"id": 1, "name": "Test Dataset", "type": "training"}]
    mock_fetch_client.test_data = expected_data

    # Define filters
    filters = [
        {"column": "type", "value": "training"},
    ]

    # Setup the mock with filters
    mock_fetch_client.setup("test_table", filters=filters)

    # Call the function with filters
    result = fetch_data("test_table", {"filters": filters})

    # Verify the function was called with correct parameters
    assert mock_fetch_client.last_table == "test_table"
    assert mock_fetch_client.last_filters == filters

    # Verify we got the expected data back
    assert result == expected_data


@patch("database.supabase_client.fetch_data")
def test_fetch_model_success(mock_fetch_data):
    """Test successful fetch of a model by UUID."""
    # Setup mock
    mock_fetch_data.return_value = [
        {
            "uuid": "model-123",
            "name": "test-model",
            "description": "Test model",
        }
    ]

    # Call the function
    result = fetch_model("model-123")

    # Verify fetch_data was called correctly
    mock_fetch_data.assert_called_once_with(
        "models",
        {
            "filters": [{"column": "uuid", "value": "model-123"}],
            "limit": 1,
        },
        profile=None,
    )

    # Verify we got data back
    assert result == {
        "uuid": "model-123",
        "name": "test-model",
        "description": "Test model",
    }


@patch("database.supabase_client.fetch_data")
def test_fetch_model_not_found(mock_fetch_data):
    """Test fetch_model when model is not found."""
    # Setup mock to return empty list (model not found)
    mock_fetch_data.return_value = []

    # Call the function
    result = fetch_model("non-existent-model")

    # Verify fetch_data was called correctly
    mock_fetch_data.assert_called_once_with(
        "models",
        {
            "filters": [{"column": "uuid", "value": "non-existent-model"}],
            "limit": 1,
        },
        profile=None,
    )

    # Verify the result is None when model is not found
    assert result is None


@patch("database.supabase_client.fetch_data")
def test_fetch_model_version_success(mock_fetch_data):
    """Test successful fetch of a model version by UUID."""
    # Setup mock
    mock_fetch_data.return_value = [
        {
            "uuid": "version-123",
            "model_uuid": "model-123",
            "version": "1.0.0",
        }
    ]

    # Call the function
    result = fetch_model_version("version-123")

    # Verify fetch_data was called correctly
    mock_fetch_data.assert_called_once_with(
        "model_versions",
        {
            "filters": [{"column": "uuid", "value": "version-123"}],
            "limit": 1,
        },
        profile=None,
    )

    # Verify we got the expected data back
    assert result == {
        "uuid": "version-123",
        "model_uuid": "model-123",
        "version": "1.0.0",
    }


@pytest.fixture(name="model_run_fixture")
def fixture_model_run():
    """Fixture for model run data with configurable values.

    Returns a function that can be called with custom values to generate
    model run data for testing.
    """

    def _create_model_run(
        run_id="run-123", version_id="version-123", status="completed"
    ):
        # Create sample model run data using the centralized function
        sample_run = get_sample_model_run_data(
            custom_values={
                "uuid": run_id,
                "model_version_uuid": version_id,
                "status": status,
            }
        )
        return sample_run

    return _create_model_run


@pytest.mark.parametrize(
    "run_id,version_id,status",
    [
        ("run-123", "version-123", "completed"),
        ("run-456", "version-456", "failed"),
        ("run-789", "version-789", "running"),
    ],
)
@patch("database.supabase_client.fetch_data")
def test_fetch_model_run_success(
    mock_fetch_data, model_run_fixture, run_id, version_id, status
):
    """Test successful fetch of a model run by UUID using parameterized testing.

    This test is data-driven and will run multiple times with different parameters.
    """
    # Create the model run data using our fixture
    sample_run = model_run_fixture(run_id, version_id, status)

    # Setup the mock to return our sample data
    mock_fetch_data.return_value = [sample_run]

    # Call the function
    result = fetch_model_run(run_id)

    # Verify fetch_data was called correctly
    mock_fetch_data.assert_called_once_with(
        "model_runs",
        {
            "filters": [{"column": "uuid", "value": run_id}],
            "limit": 1,
        },
        profile=None,
    )

    # Verify we got the expected data back
    assert result == sample_run
    assert result["uuid"] == run_id
    assert result["model_version_uuid"] == version_id
    assert result["status"] == status


@patch("database.supabase_client.fetch_model")
@patch("database.supabase_client.fetch_model_version")
def test_fetch_model_and_version_success(mock_fetch_version, mock_fetch_model):
    """Test successful fetch of both model and version."""
    # Setup mocks
    mock_version = {
        "uuid": "version-123",
        "model_uuid": "model-123",
        "version": "1.0.0",
    }
    mock_model = {"uuid": "model-123", "name": "test-model"}

    mock_fetch_version.return_value = mock_version
    mock_fetch_model.return_value = mock_model

    # Call the function
    result = fetch_model_and_version("version-123")

    # Verify the helper functions were called correctly
    mock_fetch_version.assert_called_once_with("version-123", profile=None)
    mock_fetch_model.assert_called_once_with("model-123", profile=None)

    # Verify the combined result
    assert result == {
        "model": {"uuid": "model-123", "name": "test-model"},
        "model_version": {
            "uuid": "version-123",
            "model_uuid": "model-123",
            "version": "1.0.0",
        },
    }


@patch("database.supabase_client.fetch_model")
@patch("database.supabase_client.fetch_model_version")
def test_fetch_model_and_version_version_not_found(
    mock_fetch_version, mock_fetch_model
):
    """Test fetch_model_and_version when version is not found."""
    # Setup mocks
    mock_fetch_version.return_value = None
    # fetch_model should not be called if version is not found
    mock_fetch_model.side_effect = AssertionError("fetch_model should not be called")

    # Call the function
    result = fetch_model_and_version("non-existent-version")

    # Verify the result is None and fetch_model was not called
    assert result is None
    mock_fetch_version.assert_called_once_with("non-existent-version", profile=None)
    mock_fetch_model.assert_not_called()


@patch("database.supabase_client.fetch_model")
@patch("database.supabase_client.fetch_model_version")
def test_fetch_model_and_version_model_not_found(mock_fetch_version, mock_fetch_model):
    """Test fetch_model_and_version when model is not found."""
    # Setup mocks
    mock_fetch_version.return_value = {"uuid": "version-123", "model_uuid": "model-123"}
    mock_fetch_model.return_value = None

    # Call the function
    result = fetch_model_and_version("version-123")

    # Verify the result is None when model is not found
    assert result is None
    mock_fetch_version.assert_called_once_with("version-123", profile=None)
    mock_fetch_model.assert_called_once_with("model-123", profile=None)
