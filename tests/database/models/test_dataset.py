"""
Tests for the Dataset database model.
"""

from database.models.dataset import Dataset
from tests.fixtures.data import get_sample_dataset_data


def test_dataset_creation(mock_supabase_client):
    """Test dataset creation with mocked client."""
    client = mock_supabase_client
    # Use the client in the test
    assert client is not None


def test_dataset_model_from_dict():
    """Test Dataset creation from dictionary."""
    # Use the centralized data function with custom values
    data = get_sample_dataset_data(
        custom_values={
            "uuid": "123",
            "name": "Test Dataset",
            "description": "Test Description",
        }
    )
    dataset = Dataset(**data)  # Use direct instantiation instead of from_dict
    assert dataset.uuid == "123"
    assert dataset.name == "Test Dataset"


def test_dataset_model_to_dict():
    """Test Dataset conversion to dictionary."""
    # Use the centralized data function with custom values
    sample_data = get_sample_dataset_data(
        custom_values={
            "uuid": "123",
            "name": "Test Dataset",
            "description": "Test Description",
        }
    )
    dataset = Dataset(**sample_data)
    # Use model_dump instead of to_dict
    data = dataset.model_dump(by_alias=True)
    assert data["uuid"] == "123"
    assert data["name"] == "Test Dataset"


# Note: Tests for get_by_id and get_all methods have been removed as they are not
# implemented yet and we don't want endpoint-specific tests for datasets as they
# are only for testing the database connection.
