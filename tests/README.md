# Testing Documentation for Coiny Classifier

This directory contains comprehensive tests for the Coiny Classifier project. The tests are organized by component and type to ensure maintainability and clear separation of concerns.

## Directory Structure

```bash
tests/
├── __init__.py
├── conftest.py                  # Global test configuration and fixture registration
├── README.md                    # This file
├── fixtures/                    # Centralized test fixtures and utilities
│   ├── __init__.py
│   ├── README.md                # Fixture documentation
│   ├── api.py                   # API-related fixtures
│   ├── async_utils.py           # Async testing utilities
│   ├── cache.py                 # Cache-related fixtures
│   ├── data.py                  # Test data fixtures
│   ├── database.py              # Database fixtures and mocks
│   ├── job_callbacks.py         # Job callback fixtures
│   ├── mocks.py                 # Centralized mocking system
│   ├── model.py                 # ML model fixtures
│   ├── paths.py                 # Path isolation fixtures (critical for test safety)
│   ├── performance.py           # Performance optimization fixtures
│   └── trainer.py               # Training-related fixtures
├── api/                         # API component tests
│   ├── __init__.py
│   ├── routes/                  # API route tests
│   ├── test_config.py           # API configuration tests
│   ├── test_endpoints.py        # API endpoint tests
│   └── test_middleware.py       # Middleware tests
├── database/                    # Database component tests
│   ├── __init__.py
│   ├── models/                  # Database model tests
│   ├── services/                # Database service tests
│   └── test_supabase_client.py  # Supabase client tests
├── integration/                 # Cross-component integration tests
│   ├── __init__.py
│   ├── conftest.py              # Integration-specific fixtures
│   ├── test_augmentation_training_integration.py
│   ├── test_background_class_integration.py
│   ├── test_data_flow_integration.py
│   ├── test_dataset_service_integration.py
│   ├── test_job_callbacks_integration.py
│   ├── test_trainer_integration.py
│   └── trainer_test_utils.py    # Integration test utilities
├── manual/                      # Manual tests and scripts
│   ├── __init__.py
│   ├── README.md                # Manual testing documentation
│   ├── api/                     # Manual API tests
│   ├── examples/                # Example scripts
│   ├── models/                  # Manual ML model tests
│   ├── output/                  # Manual test output directory
│   └── test_*.py                # Various manual test scripts
├── models/                      # ML model tests
│   ├── __init__.py
│   ├── test_architectures.py    # Model architecture tests
│   ├── test_dynamic_cnn.py      # Dynamic CNN tests
│   └── test_ml_factory.py       # ML factory tests
└── unit/                        # Unit tests for individual modules
    ├── __init__.py
    ├── conftest.py              # Unit test configuration
    ├── augmentations/           # Augmentation unit tests
    ├── common/                  # Common utility tests
    ├── database/                # Database unit tests
    ├── datasets/                # Dataset handling tests
    ├── jobs/                    # Job system tests
    ├── utils/                   # Utility function tests
    ├── test_*.py                # Various unit tests
    └── test_runs_isolation.py   # Test isolation verification
```

## Running Tests

### Running All Tests

```bash
pytest
```

### Running Tests for a Specific Component

```bash
# Run all API tests
pytest tests/api/

# Run all database tests
pytest tests/database/

# Run all model tests
pytest tests/models/

# Run all integration tests
pytest tests/integration/
```

### Running a Specific Test File

```bash
# Run a specific test file
pytest tests/api/test_endpoints.py
```

### Running with Coverage

```bash
# Run with coverage for all components
pytest --cov=src

# Run with coverage for specific components
pytest --cov=src/api tests/api/
pytest --cov=src/database tests/database/
pytest --cov=src/models tests/models/
```

## Test Organization

### API Tests

Tests in the `api/` directory focus on the REST API components:

- Endpoints
- Middleware
- Configuration

### Database Tests

Tests in the `database/` directory focus on database-related components:

- Supabase client
- Database models

### Model Tests

Tests in the `models/` directory focus on machine learning models:

- CoinsNet model
- Model training
- Model inference

### Integration Tests

Tests in the `integration/` directory focus on testing the integration between different components:

- API and database integration
- API and model integration

## Test Fixtures and Architecture

### Centralized Fixture System

The test suite uses a centralized fixture system located in `tests/fixtures/` to promote code reuse and maintain consistency:

- **`tests/fixtures/paths.py`** - Critical path isolation fixtures that prevent test contamination
- **`tests/fixtures/mocks.py`** - Centralized mocking system with configurable module mocking
- **`tests/fixtures/data.py`** - Test data generation and management
- **`tests/fixtures/database.py`** - Database-related fixtures and utilities
- **`tests/fixtures/performance.py`** - Performance optimization fixtures (library warming, etc.)

### Fixture Registration

Fixtures are automatically registered through the global `conftest.py` using the `pytest_plugins` mechanism:

```python
pytest_plugins = [
    "tests.fixtures.api",
    "tests.fixtures.async_utils",
    "tests.fixtures.cache",
    "tests.fixtures.data",
    "tests.fixtures.database",
    "tests.fixtures.job_callbacks",
    "tests.fixtures.mocks",
    "tests.fixtures.model",
    "tests.fixtures.paths",
    "tests.fixtures.performance",
    "tests.fixtures.trainer",
]
```

### Component-Specific Configuration

Some test directories have their own `conftest.py` files for component-specific configuration:

- **`tests/unit/conftest.py`** - Applies automatic database mocking to all unit tests
- **`tests/integration/conftest.py`** - Integration-specific fixtures and client setup

## Adding New Tests

When adding new tests:

1. **Choose the right directory** based on test type:
   - `unit/` for isolated component tests
   - `integration/` for cross-component tests
   - `manual/` for exploratory or development tests
2. **Use naming conventions**: `test_*.py` for files, `test_*` for functions
3. **Leverage existing fixtures** from `tests/fixtures/` before creating new ones
4. **Add new fixtures** to the appropriate fixture module in `tests/fixtures/`
5. **Follow isolation principles** - tests should not affect each other or production data

## Test Isolation and Safety

### Critical Safety Features

The test suite includes robust isolation mechanisms to ensure tests never interfere with production data:

**🔒 Automatic Directory Isolation (Auto-enabled for ALL tests)**

All tests are automatically isolated from production directories through autouse fixtures in `tests/fixtures/paths.py`:

- **Run Directory Isolation**:
  - Session-scoped `test_runs_dir` creates temporary directories
  - `isolate_runs_directory` fixture patches both legacy `RUNS_BASE_DIR` and new `_runs_config` global instance
  - All run-related functions (`get_run_dir`, `ensure_run_directories`, `get_logs_dir`, etc.) automatically use isolated directories
  - Supports both `src.config.paths` and `config.paths` import patterns
  - **Recent Fix**: Now properly handles the new `RunsConfig` class system

- **Dataset Directory Isolation**:
  - Session-scoped `test_datasets_dir` creates temporary directories
  - `isolate_datasets_directory` fixture patches `DATASETS_BASE_DIR` and `_dataset_config`
  - All dataset functions (`get_dataset_dir`, `get_dataset_images_dir`, etc.) use isolated directories
  - Prevents test datasets from contaminating production `/datasets` directory

**🧹 Automatic Cleanup**

- All temporary directories are automatically cleaned up when test sessions end
- No manual cleanup required for standard test operations
- Production directories (`/runs`, `/datasets`) remain completely clean after test execution

### Manual Cleanup for Specific Tests

For tests that create run directories and want explicit cleanup control:

```python
def test_something_with_cleanup(cleanup_test_runs):
    model_run_uuid = "test-uuid-123"
    cleanup_test_runs.add_run_uuid(model_run_uuid)

    # Create directories and files
    directories = ensure_run_directories(model_run_uuid)
    # ... test code ...

    # Cleanup happens automatically after test completes
```

For tests that create dataset directories and want explicit cleanup control:

```python
def test_dataset_functionality(cleanup_test_datasets):
    dataset_uuid = str(uuid.uuid4())
    cleanup_test_datasets.add_dataset_uuid(dataset_uuid)

    # Create dataset directories and files
    directories = ensure_dataset_directories(dataset_uuid)
    # ... test code ...

    # Cleanup happens automatically after test completes
```

### Virtual Directory Safety

The isolation system ensures that:

- Tests never create directories in the real `/runs` or `/datasets` folders
- Even if a test uses a UUID that matches a real model run or dataset, it operates in isolation
- No test content can overwrite or interfere with production data
- All test artifacts are automatically cleaned up
- Dataset tests use virtual directories that mirror the production structure but are completely isolated

## Testing Environment Setup

The project is configured to ensure consistent test behavior across different environments:

1. **Local Development**: Running tests directly with `pytest`
2. **Pre-commit Hooks**: Tests run through pre-commit hooks
3. **CI/CD Pipeline**: Tests run in continuous integration

### Key Configuration Files

- **pyproject.toml**: Contains pytest configuration and Python path settings
- **.pre-commit-config.yaml**: Configures pre-commit hooks with environment variables
- **tests/conftest.py**: Contains shared test fixtures

## Advanced Testing Features

### Centralized Mocking System

The test suite includes a sophisticated mocking system in `tests/fixtures/mocks.py` that provides:

**🎭 Configurable Module Mocking**

```python
# Automatic mocking based on test markers
@pytest.mark.auto_mock(categories=["database"])
def test_with_database_mocks():
    # All database modules are automatically mocked
    pass

@pytest.mark.mock_all
def test_with_all_mocks():
    # All external dependencies are mocked
    pass
```

**📦 Category-Based Mocking**

- **API**: Mock FastAPI, HTTP clients, and API dependencies
- **Database**: Mock Supabase, database connections, and ORM operations
- **ML**: Mock PyTorch, NumPy, PIL, and ML model operations

**🔧 Flexible Mock Configuration**

- Unit tests automatically apply database mocking via `tests/unit/conftest.py`
- Integration tests can selectively mock external dependencies
- Manual tests typically use real services for end-to-end validation

### Performance Optimizations

**⚡ Library Warming**

The `tests/fixtures/performance.py` module includes automatic library warming:

- Pre-loads heavy libraries (PyTorch, NumPy, PIL) during test session startup
- Reduces individual test execution time
- Provides session-scoped cleanup for optimal memory management

### Recent Improvements

**🔧 Runs Directory Isolation Fix (Latest)**

- Fixed regression where test folders were created in production `/runs` directory
- Updated `isolate_runs_directory` fixture to properly patch new `RunsConfig` class
- Ensures complete isolation for both legacy and modern path configuration systems
- Added comprehensive test coverage to prevent future regressions

**📊 Enhanced Test Organization**

- Migrated to centralized fixture system for better maintainability
- Improved test categorization and directory structure
- Added automated fixture registration system
- Enhanced documentation and inline code examples

## Best Practices for Reliable Tests

### Pytest Fixture Best Practices

#### 1. Avoid Redefined-Outer-Name Warnings

One common issue in pytest is the `redefined-outer-name` warning, which occurs when a test function parameter has the same name as a fixture. This can lead to confusion and potential bugs.

```python
# BAD: This will trigger a redefined-outer-name warning
@pytest.fixture
def model():
    return SimpleModel()

def test_model_prediction(model):
    # The parameter 'model' shadows the fixture 'model'
    model = model.train()  # This is confusing! Which model are we referring to?
    assert model.predict() == expected_result
```

Solutions:

1. **Use different names for local variables inside test methods**:

```python
# GOOD: Use a prefix for local variables
def test_model_prediction(model):
    test_model = model.train()  # Clear distinction between fixture and local variable
    assert test_model.predict() == expected_result
```

1. **Use the `request` fixture to access other fixtures**:

```python
# GOOD: Use request.getfixturevalue() to avoid parameter naming conflicts
def test_model_prediction(request):
    model = request.getfixturevalue("model")
    trained_model = model.train()
    assert trained_model.predict() == expected_result
```

1. **Avoid variable name conflicts in fixtures themselves**:

```python
# BAD: The fixture parameter has the same name as the local variable
@pytest.fixture
def temp_dir():
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir  # This triggers a warning

# GOOD: Use different names
@pytest.fixture
def temp_dir():
    with tempfile.TemporaryDirectory() as temp_dir_path:
        yield temp_dir_path
```

#### 2. Fixture Dependency Best Practices

- **Be explicit about fixture dependencies**:

```python
# GOOD: Explicitly declare fixture dependencies
@pytest.fixture
def model_trainer(model, optimizer, dataset):
    return ModelTrainer(model, optimizer, dataset)
```

- **Use `usefixtures` for fixtures that are needed but not directly used**:

```python
# GOOD: Use usefixtures for setup/teardown fixtures
@pytest.mark.usefixtures("database_connection")
def test_database_query():
    # The database_connection fixture is used for setup but not directly accessed
    result = execute_query("SELECT * FROM table")
    assert result is not None
```

#### 3. Fixture Scope Management

- **Choose appropriate fixture scopes**:

```python
# Expensive setup should use broader scopes
@pytest.fixture(scope="module")
def large_dataset():
    # This will only be created once per module
    return load_large_dataset()

# Stateful fixtures that need to be reset should use narrower scopes
@pytest.fixture(scope="function")
def database():
    # This will be reset for each test function
    db = create_database()
    yield db
    db.reset()
```

#### 4. Fixture Organization

- **Place shared fixtures in conftest.py**
- **Place component-specific fixtures in component-specific conftest.py files**
- **Document fixture purpose with clear docstrings**

```python
@pytest.fixture
def model():
    """Create a trained model instance for testing.
    
    Returns a model with pre-loaded weights for consistent test results.
    """
    model = SimpleModel()
    model.load_weights("test_weights.pt")
    return model
```

### 1. Always Use Absolute Imports

```python
# Good - absolute imports
from src.models.CoinsNet import create_coins_net

# Bad - relative imports
from models.CoinsNet import create_coins_net
```

### 2. Use Absolute Paths for File Operations

```python
# Good - absolute path construction
def get_trained_models_dir():
    current_file = Path(os.path.abspath(__file__))
    project_root = current_file.parent.parent.parent
    models_dir = project_root / "src" / "models" / "trained"
    return str(models_dir)

# Bad - relative paths
DIR_TRAINED_MODELS = "src/models/trained"
```

### 3. Ensure All Directories Have `__init__.py` Files

All directories in the project should have an `__init__.py` file to ensure they are recognized as Python packages, which is essential for reliable imports.

### 4. Handle Environment Detection in Tests

```python
def is_running_in_pre_commit():
    """Detect if we're running in pre-commit mode."""
    pytest_opts = os.environ.get('PYTEST_ADDOPTS', '')
    if '--import-mode=importlib' in pytest_opts:
        return True

    cwd = os.getcwd()
    if '.git/hooks' in cwd or 'pre-commit' in cwd:
        return True
    
    return False

# Skip tests that don't work in pre-commit mode
@pytest.mark.skipif(IN_PRE_COMMIT, reason="Test skipped in pre-commit mode")
def test_problematic_function():
    # Test implementation
```

### 5. Use Robust Module Loading

```python
def get_module():
    """Get a module using multiple fallback approaches."""
    # Try absolute import first
    try:
        import src.models.SomeModule as module
        return module
    except ImportError:
        # Try alternative approaches...
```

## Common Issues and Solutions

### 1. Mocking Issues

**Problem**: Mocks fail when using relative imports or system-dependent paths.

**Solution**:

- Always use absolute imports
- Use absolute paths for file operations
- Use context managers for patching
- Use `autospec=True` for more accurate mocking

```python
# Good mocking approach
with patch('src.models.CoinsNet.load_model', autospec=True) as mock_load:
    # Test code
```

#### Using `autospec=True`

The `autospec=True` parameter creates a mock that automatically inherits the specifications of the object being patched:

- The mock will have the same attributes and methods as the original object
- If you try to access an attribute or call a method that doesn't exist on the original, you'll get an `AttributeError`
- Method signatures are enforced, so calling a method with incorrect arguments will raise a `TypeError`

This helps catch interface changes early and makes tests more robust. It can be used with both context managers and decorators:

```python
# With context manager
with patch('src.models.CoinsNet.load_model', autospec=True) as mock_load:
    # Test code

# With decorator
@patch('torch.load', autospec=True)
def test_function(mock_torch_load):
    # Test code
```

### 2. Path Resolution Issues

**Problem**: Tests fail because they can't find files in different environments.

**Solution**:

- Use Path objects for path manipulation
- Construct absolute paths from the current file location
- Ensure directories exist before using them

### 3. Import Mode Conflicts

**Problem**: Tests pass locally but fail in pre-commit hooks due to different import modes.

**Solution**:

- Use the `--import-mode=importlib` option consistently
- Set `PYTHONPATH=.:src` in all environments
- Skip problematic tests in specific environments

## Environment Variables

The following environment variables are set in pre-commit hooks:

```yaml
env:
  - PYTHONPATH=.:src
  - PYTEST_ADDOPTS=--import-mode=importlib
```

These ensure consistent behavior across environments.

## Test Skipping Strategy

Some tests are skipped in pre-commit mode due to mocking limitations. This is a deliberate choice to ensure tests don't fail in pre-commit hooks while still providing full test coverage in local development.

## Common Pytest Linting Issues and Solutions

### 1. Redefined-Outer-Name (W0621)

This is one of the most common pylint warnings in pytest code. It occurs when a test function parameter has the same name as a fixture.

```python
# This triggers W0621: redefined-outer-name
def test_function(model):  # 'model' is also the name of a fixture
    model = modify_model(model)  # Confusing reuse of the name
```

**Solutions:**

1. Use the `request` fixture to access other fixtures indirectly:

```python
def test_function(request):
    model = request.getfixturevalue("model")
    modified_model = modify_model(model)
```

1. Use prefixed variable names to distinguish local variables from fixtures:

```python
def test_function(model):
    test_model = modify_model(model)
```

1. In fixtures, avoid using the same name for local variables and the fixture itself:

```python
@pytest.fixture
def temp_dir():
    with tempfile.TemporaryDirectory() as temp_dir_path:  # Not 'temp_dir'
        yield temp_dir_path
```

### 2. Unused Argument (W0613)

This occurs when a test function accepts a fixture but doesn't use it directly.

```python
# This triggers W0613: unused-argument
def test_function(database):  # 'database' is never used in the function
    result = get_result()
    assert result is not None
```

**Solutions:**

1. Use `@pytest.mark.usefixtures` instead of accepting the fixture as a parameter:

```python
@pytest.mark.usefixtures("database")
def test_function():
    result = get_result()
    assert result is not None
```

1. If you need the fixture for setup but not in the test body, consider restructuring your fixtures.

### 3. Too Many Arguments (R0913)

This occurs when a test function has too many parameters (often fixtures).

**Solutions:**

1. Create composite fixtures that combine multiple dependencies:

```python
@pytest.fixture
def test_environment(model, optimizer, dataset, config):
    return TestEnvironment(model, optimizer, dataset, config)

def test_function(test_environment):
    # Use the combined environment instead of individual fixtures
```

1. Use the `request` fixture to access multiple fixtures as needed:

```python
def test_function(request):
    model = request.getfixturevalue("model")
    # Only get other fixtures if needed
    if condition:
        optimizer = request.getfixturevalue("optimizer")
```

### 4. Function Too Complex (R0912, R0915)

Test functions should be simple and focused. If they're too complex, they're hard to maintain.

**Solutions:**

1. Break down complex tests into multiple smaller tests
2. Move complex setup logic into fixtures
3. Create helper functions for repetitive assertions

## Troubleshooting and Verification

### Verifying Test Isolation

To verify that test isolation is working correctly:

```bash
# Run tests and check that production directories remain clean
pytest tests/unit/test_runs_isolation.py -v

# Verify no test artifacts in production directories
ls -la runs/        # Should be empty or contain only production data
ls -la datasets/    # Should contain only CSV files, no test directories
```

### Common Issues and Solutions

**❌ "Test folders appearing in production /runs directory"**
- **Cause**: Runs isolation fixture not properly patching new configuration system
- **Solution**: Ensure `tests/fixtures/paths.py` patches both `RUNS_BASE_DIR` and `_runs_config`
- **Verification**: Run `pytest tests/unit/test_runs_isolation.py`

**❌ "Import errors in tests"**
- **Cause**: Python path configuration issues
- **Solution**: Check `pyproject.toml` pytest configuration and `conftest.py` path setup
- **Verification**: Run `python -c "import src.config.paths; print('OK')"`

**❌ "Fixtures not found"**
- **Cause**: Missing fixture registration in `pytest_plugins`
- **Solution**: Add fixture module to `tests/conftest.py` pytest_plugins list
- **Verification**: Run `pytest --fixtures` to see available fixtures

**❌ "Database connection errors in unit tests"**
- **Cause**: Database mocking not applied correctly
- **Solution**: Ensure test has `@pytest.mark.auto_mock(categories=["database"])` or is in `tests/unit/`
- **Verification**: Check that unit tests don't make real database calls

### Performance Tips

- Use `pytest -x` to stop on first failure for faster debugging
- Use `pytest --tb=short` for concise error output
- Use `pytest -k "test_name"` to run specific tests
- Use `pytest --fixtures` to see all available fixtures
- Use `pytest --collect-only` to verify test discovery without running

### Monitoring Test Health

```bash
# Run full test suite with coverage
pytest --cov=src tests/

# Run only fast unit tests
pytest tests/unit/ -v

# Run integration tests (slower)
pytest tests/integration/ -v

# Run with performance profiling
pytest --durations=10 tests/
```

## Updating Pydantic Models

The current warnings about deprecated Pydantic features can be addressed by:

1. Replacing class-based `config` with `ConfigDict`
2. Updating `json_encoders` to use custom serializers

See the [Pydantic V2 Migration Guide](https://docs.pydantic.dev/2.11/migration/) for details.

---

This is a comprehensive guide to the testing infrastructure. For more specific information about individual test components, refer to the README files in each subdirectory.
