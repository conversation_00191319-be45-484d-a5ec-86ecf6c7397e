[MASTER]
# Add src directory to Python path for test files
init-hook=
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.resolve()
    sys.path.append(str(project_root / "src"))

# Use same settings as main .pylintrc but with specific test configurations
[FORMAT]
max-line-length=100

[MESSAGES CONTROL]
# Keep all error checking enabled
disable=

[DESIGN]
# Tests often need more methods/attributes than regular code
max-args=10
max-attributes=10
min-public-methods=1
max-public-methods=30

[SIMILARITIES]
# Tests often have similar setup code
min-similarity-lines=6

[BASIC]
# Allow test_ prefix for functions and Test prefix for classes
good-names=i,j,k,ex,Run,_,id,ip,up,db,test_,Test

[TYPECHECK]
# Common test libraries that might need ignoring for dynamic attributes
ignored-classes=pytest,pytest.fixture