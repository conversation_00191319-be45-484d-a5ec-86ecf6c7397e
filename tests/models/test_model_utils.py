"""
Unit tests for model utility functions.
"""

import pytest
from torch import nn, optim
from torch.optim import lr_scheduler

from src.models.model_utils import (
    create_loss_function,
    create_optimizer,
    create_scheduler,
    get_activation,
)


class TestModelUtils:
    """Test suite for model utility functions."""

    def test_get_activation(self):
        """Test the get_activation function."""
        assert isinstance(get_activation("relu"), nn.ReLU)
        assert isinstance(get_activation("leakyrelu"), nn.LeakyReLU)
        assert isinstance(get_activation(None), nn.ReLU)  # Default case

        with pytest.raises(
            ValueError, match="Unsupported activation function: sigmoid"
        ):
            get_activation("sigmoid")

    def test_create_loss_function(self):
        """Test the create_loss_function function."""
        assert isinstance(create_loss_function({"type": "bce"}), nn.BCEWithLogitsLoss)
        assert isinstance(create_loss_function({"type": "ce"}), nn.<PERSON>nt<PERSON>y<PERSON>)
        assert isinstance(create_loss_function({"type": "mse"}), nn.MSELoss)
        assert isinstance(
            create_loss_function({}), nn.BCEWithLogitsLoss
        )  # Default case

        with pytest.raises(ValueError, match="Unsupported loss function: custom"):
            create_loss_function({"type": "custom"})

    def test_create_optimizer(self):
        """Test the create_optimizer function."""
        model = nn.Linear(10, 2)

        assert isinstance(
            create_optimizer({"type": "adam"}, model.parameters()), optim.Adam
        )
        assert isinstance(
            create_optimizer({}, model.parameters()), optim.Adam
        )  # Default case

        # Test learning rate parameter
        optimizer = create_optimizer(
            {"type": "sgd", "learning_rate": 0.1}, model.parameters()
        )
        assert isinstance(optimizer, optim.SGD)
        assert optimizer.defaults["lr"] == 0.1

        with pytest.raises(ValueError, match="Unsupported optimizer: custom"):
            create_optimizer({"type": "custom"}, model.parameters())

    def test_create_scheduler(self):
        """Test the create_scheduler function."""
        model = nn.Linear(10, 2)
        optimizer = optim.Adam(model.parameters())

        # Test creating a StepLR scheduler
        scheduler_params = {"type": "step", "step_size": 30, "gamma": 0.1}
        scheduler = create_scheduler(scheduler_params, optimizer)
        assert isinstance(scheduler, lr_scheduler.StepLR)
        assert scheduler.step_size == 30

        # Test creating a CosineAnnealingLR scheduler
        scheduler_params = {"type": "cosine", "T_max": 50}
        scheduler = create_scheduler(scheduler_params, optimizer)
        assert isinstance(scheduler, lr_scheduler.CosineAnnealingLR)

        # Test case where no scheduler type is provided
        assert create_scheduler({}, optimizer) is None

        # Test unsupported scheduler
        with pytest.raises(ValueError, match="Unsupported scheduler: custom"):
            create_scheduler({"type": "custom"}, optimizer)
