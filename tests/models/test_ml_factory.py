"""
Tests for the MLModelFactory using pytest conventions.
"""

import uuid

import pytest
import torch
from torch import nn

from src.models.ml_factory import MLModelFactory
from src.models.persistence import LoadModelOptions, ModelData


@pytest.fixture
def _ml_model_factory(tmp_path):
    """Provides an MLModelFactory instance with a temporary persistence directory."""
    return MLModelFactory(persistence_base_dir=tmp_path)


# --- Model Creation Tests ---


def test_create_cnn_model_happy_path(_ml_model_factory):
    """Test creating a standard CNN model successfully."""
    params = {
        "name": "CNN",
        "conv_layers": [
            {"out_channels": 3, "kernel_size": 3, "stride": 1, "padding": 1},
        ],
        "fc_layers": [10],
    }
    model = _ml_model_factory.create_model(params)
    assert isinstance(model, nn.Sequential)


def test_create_resnet_model_happy_path(_ml_model_factory):
    """Test creating a ResNet model from torchvision."""
    params = {"name": "ResNet", "variant": "18", "pretrained": False}
    model = _ml_model_factory.create_model(params, num_classes=10)
    assert isinstance(model, nn.Module)
    assert model.fc.out_features == 10


def test_create_model_unsupported_architecture(_ml_model_factory):
    """Test creating a model with an unsupported architecture name."""
    params = {"name": "MagicNet"}
    with pytest.raises(ValueError, match="Unsupported architecture: MagicNet"):
        _ml_model_factory.create_model(params)


def test_create_model_missing_name(_ml_model_factory):
    """Test creating a model with missing architecture name."""
    params = {"variant": "18"}
    with pytest.raises(ValueError, match="Architecture 'name' must be specified"):
        _ml_model_factory.create_model(params)


# --- Loss Function Tests ---


def test_create_loss_function_happy_path(_ml_model_factory):
    """Test creating a supported loss function."""
    params = {"type": "ce"}
    loss_fn = _ml_model_factory.create_loss_function(params)
    assert isinstance(loss_fn, nn.CrossEntropyLoss)


def test_create_loss_function_unsupported(_ml_model_factory):
    """Test creating an unsupported loss function."""
    params = {"type": "fancy_loss"}
    with pytest.raises(ValueError, match="Unsupported loss function: fancy_loss"):
        _ml_model_factory.create_loss_function(params)


# --- Optimizer Tests ---


def test_create_optimizer_happy_path(_ml_model_factory):
    """Test creating a supported optimizer."""
    model = nn.Linear(10, 2)
    params = {"type": "adamw", "learning_rate": 0.01}
    optimizer = _ml_model_factory.create_optimizer(params, model.parameters())
    assert isinstance(optimizer, torch.optim.AdamW)
    assert optimizer.defaults["lr"] == 0.01


def test_create_optimizer_unsupported(_ml_model_factory):
    """Test creating an unsupported optimizer."""
    model = nn.Linear(10, 2)
    params = {"type": "lion"}
    with pytest.raises(ValueError, match="Unsupported optimizer: lion"):
        _ml_model_factory.create_optimizer(params, model.parameters())


# --- Scheduler Tests ---


def test_create_scheduler_happy_path(_ml_model_factory):
    """Test creating a supported learning rate scheduler."""
    model = nn.Linear(10, 2)
    optimizer = torch.optim.SGD(model.parameters(), lr=0.1)
    params = {"type": "step", "step_size": 10}
    scheduler = _ml_model_factory.create_scheduler(params, optimizer)
    assert isinstance(scheduler, torch.optim.lr_scheduler.StepLR)


def test_create_scheduler_no_type(_ml_model_factory):
    """Test that no scheduler is created if type is not specified."""
    model = nn.Linear(10, 2)
    optimizer = torch.optim.SGD(model.parameters(), lr=0.1)
    params = {"step_size": 10}  # No 'type' key
    scheduler = _ml_model_factory.create_scheduler(params, optimizer)
    assert scheduler is None


# --- Persistence Tests ---


def test_model_persistence_save_and_load(_ml_model_factory):
    """Test saving and loading a model checkpoint."""
    model_id = str(uuid.uuid4())
    model = nn.Linear(10, 2)
    optimizer = torch.optim.SGD(model.parameters(), lr=0.1)
    metadata = {"epoch": 1, "loss": 0.5}

    # Save
    model_data = ModelData(model=model, optimizer=optimizer, metadata=metadata)
    _ml_model_factory.persistence.save_model(model_data, model_id)

    # Create new instances for loading
    new_model = nn.Linear(10, 2)
    new_optimizer = torch.optim.SGD(new_model.parameters(), lr=0.1)

    # Load
    load_options = LoadModelOptions(model=new_model, optimizer=new_optimizer)
    loaded_model, loaded_optimizer, loaded_metadata = (
        _ml_model_factory.persistence.load_model(load_options, model_id)
    )

    # Assertions
    assert loaded_metadata == metadata
    assert torch.equal(model.weight, loaded_model.weight)
    assert optimizer.state_dict() == loaded_optimizer.state_dict()


def test_model_persistence_discard(_ml_model_factory):
    """Test discarding a saved model."""
    model_id = str(uuid.uuid4())
    model = nn.Linear(10, 2)
    optimizer = torch.optim.SGD(model.parameters(), lr=0.1)

    model_data = ModelData(model=model, optimizer=optimizer)
    _ml_model_factory.persistence.save_model(model_data, model_id)

    model_dir = _ml_model_factory.persistence.base_dir / model_id
    assert model_dir.exists()

    assert _ml_model_factory.persistence.discard_model(model_id) is True
    assert not model_dir.exists()
