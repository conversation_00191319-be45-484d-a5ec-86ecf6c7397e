"""
Tests for the model architecture module.
"""

import unittest
from unittest.mock import patch

import pytest
import torch
from pydantic import ValidationError

from src.database.models.model import Model
from src.models.architectures import (
    ArchitectureCategory,
    ModelArchitecture,
    create_model_from_architecture,
    get_all_architectures,
    get_architecture_info,
    get_architectures_by_category,
)


class TestModelArchitectures(unittest.TestCase):
    """Test the model architecture module."""

    def test_get_architecture_info(self):
        """Test getting architecture info by name."""
        # Test exact match
        info = get_architecture_info("ResNet")
        self.assertEqual(info.name, ModelArchitecture.RESNET)
        self.assertEqual(info.category, ArchitectureCategory.MODERN)

        # Test case-insensitive match
        info = get_architecture_info("resnet")
        self.assertEqual(info.name, ModelArchitecture.RESNET)

        # Test specific variant match
        info = get_architecture_info("ResNet50")
        self.assertEqual(info.name, ModelArchitecture.RESNET)

        # Test invalid architecture
        with self.assertRaises(ValueError):
            get_architecture_info("InvalidArchitecture")

    def test_get_all_architectures(self):
        """Test getting all architectures."""
        architectures = get_all_architectures()
        self.assertEqual(len(architectures), len(ModelArchitecture))

        # Check that all architectures are included
        architecture_names = {arch.name for arch in architectures}
        self.assertEqual(architecture_names, set(ModelArchitecture))

    def test_get_architectures_by_category(self):
        """Test getting architectures by category."""
        modern_architectures = get_architectures_by_category(
            ArchitectureCategory.MODERN
        )
        self.assertTrue(
            all(
                arch.category == ArchitectureCategory.MODERN
                for arch in modern_architectures
            )
        )

        # Check specific architectures in the modern category
        modern_names = {arch.name for arch in modern_architectures}
        self.assertIn(ModelArchitecture.RESNET, modern_names)
        self.assertIn(ModelArchitecture.DENSENET, modern_names)
        self.assertIn(ModelArchitecture.MOBILENET, modern_names)

    def test_model_architecture_validation(self):
        """Test validation of architecture in the Model class."""
        # Valid architecture
        model = Model(name="Test Model", architecture="ResNet")
        self.assertEqual(model.architecture, "ResNet")

        # Valid architecture with specific variant
        model = Model(name="Test Model", architecture="ResNet50")
        self.assertEqual(model.architecture, "ResNet50")

        # Invalid architecture
        with self.assertRaises(ValidationError):
            Model(name="Test Model", architecture="InvalidArchitecture")

    @pytest.mark.skipif(not torch.cuda.is_available(), reason="CUDA not available")
    def test_create_model_from_architecture_with_gpu(self):
        """Test creating a model with GPU support if available."""
        model = create_model_from_architecture("ResNet", num_classes=10)
        self.assertIsInstance(model, torch.nn.Module)

        # Move to GPU if available
        model = model.cuda()

        # Create a dummy input tensor
        x = torch.randn(1, 3, 224, 224).cuda()

        # Forward pass
        output = model(x)

        # Check output shape
        self.assertEqual(output.shape, (1, 10))

    def test_create_model_from_architecture(self):
        """Test creating models from different architectures."""
        # Test ResNet
        model = create_model_from_architecture("ResNet", num_classes=10)
        self.assertIsInstance(model, torch.nn.Module)

        # Test CNN (custom implementation)
        model = create_model_from_architecture("CNN", image_size=224)
        self.assertIsInstance(model, torch.nn.Module)

        # Test invalid architecture
        with self.assertRaises(ValueError):
            create_model_from_architecture("InvalidArchitecture")

        # Test architecture requiring custom implementation that's not yet available
        with self.assertRaises(NotImplementedError):
            create_model_from_architecture("YOLO")

    @patch("torch.nn.Module")
    @patch("torchvision.models.resnet50")
    def test_pretrained_parameter(self, mock_resnet, mock_module):
        """Test that the pretrained parameter is passed correctly."""
        # Setup mock
        mock_resnet.return_value = mock_module
        mock_module.fc = torch.nn.Linear(2048, 1000)

        # Test with pretrained=True
        create_model_from_architecture("ResNet", pretrained=True)

        # Check that pretrained was passed correctly
        # Note: Depending on torchvision version, it might use 'pretrained' or 'weights'
        if "pretrained" in mock_resnet.call_args[1]:
            self.assertTrue(mock_resnet.call_args[1]["pretrained"])
        elif "weights" in mock_resnet.call_args[1]:
            self.assertEqual(mock_resnet.call_args[1]["weights"], "DEFAULT")


if __name__ == "__main__":
    unittest.main()
