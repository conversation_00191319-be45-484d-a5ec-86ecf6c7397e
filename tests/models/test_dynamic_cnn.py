"""
Tests for the dynamic CNN model builder.
"""

import pytest
import torch
from torch import nn

from src.models.dynamic_cnn import create_dynamic_cnn


# Default parameters for a dynamic CNN that mimic the old CoinsNet architecture
@pytest.fixture(name="dynamic_cnn_params")
def fixture_dynamic_cnn_params():
    """Provides a default set of parameters for creating a dynamic CNN."""
    return {
        "conv_layers": [
            {"out_channels": 3, "kernel_size": 3, "stride": 1, "padding": 1},
            {"out_channels": 6, "kernel_size": 3, "stride": 1, "padding": 1},
        ],
        "batch_norm": True,
        "activation": "relu",
        "pooling": ("max", 2, 2),  # (pool_type, kernel_size, stride)
        "dropout": 0.5,
        "fc_layers": [50],
    }


def test_create_dynamic_cnn(dynamic_cnn_params):
    """Test that a dynamic CNN model can be created."""
    model_run_params = {"dropout_rate": dynamic_cnn_params.get("dropout")}
    model, _, _ = create_dynamic_cnn(
        {
            "model_params": dynamic_cnn_params,
            "dropout_rate": model_run_params.get("dropout_rate"),
            "num_classes": 1,
            "image_size": 91,
            "image_channels": 1,
        }
    )
    assert isinstance(model, nn.Module)


def test_model_forward_pass(dynamic_cnn_params, sample_image_batch):
    """Test that the model can perform a forward pass."""
    model_run_params = {"dropout_rate": dynamic_cnn_params.get("dropout")}
    model, _, _ = create_dynamic_cnn(
        {
            "model_params": dynamic_cnn_params,
            "dropout_rate": model_run_params.get("dropout_rate"),
            "num_classes": 1,
            "image_size": 91,
            "image_channels": 1,
        }
    )
    model.eval()
    with torch.no_grad():
        output = model(sample_image_batch)

    assert output.shape == (32, 1)
    assert output.dtype == torch.float32


def test_model_loss_computation(dynamic_cnn_params, sample_image_batch, sample_labels):
    """Test that loss can be computed."""
    model_run_params = {"dropout_rate": dynamic_cnn_params.get("dropout")}
    model, _, _ = create_dynamic_cnn(
        {
            "model_params": dynamic_cnn_params,
            "dropout_rate": model_run_params.get("dropout_rate"),
            "num_classes": 1,
            "image_size": 91,
            "image_channels": 1,
        }
    )
    loss_fn = nn.BCEWithLogitsLoss()
    model.train()
    output = model(sample_image_batch)
    loss = loss_fn(output, sample_labels)

    assert isinstance(loss, torch.Tensor)
    assert loss.shape == ()
    assert loss.dtype == torch.float32
    assert loss.requires_grad


def test_sample_image_batch_fixture(sample_image_batch):
    """Verify the sample_image_batch fixture has correct dimensions and type."""
    assert isinstance(sample_image_batch, torch.Tensor)
    assert sample_image_batch.shape == (32, 1, 91, 91)
    assert sample_image_batch.dtype == torch.float32


def test_sample_labels_fixture(sample_labels):
    """Verify the sample_labels fixture has correct dimensions and type."""
    assert isinstance(sample_labels, torch.Tensor)
    assert sample_labels.shape == (32, 1)
    assert sample_labels.dtype == torch.float32
    assert all(label in [0.0, 1.0] for label in sample_labels.flatten())


def test_mock_model_weights_fixture(mock_model_weights):
    """Verify the mock_model_weights fixture has correct structure and dimensions."""
    assert isinstance(mock_model_weights, dict)
    # This test is less relevant for the dynamic model as layer names are numeric.
    # We'll keep it simple and check for a few expected keys.
    expected_keys = ["0.weight", "0.bias", "3.weight", "3.bias"]
    for key in expected_keys:
        assert key in mock_model_weights
        assert isinstance(mock_model_weights[key], torch.Tensor)
