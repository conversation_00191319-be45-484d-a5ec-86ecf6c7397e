"""
Manual test for the TrainingJobDispatcher.

This script demonstrates how to use the TrainingJobDispatcher to schedule
and execute training jobs asynchronously.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Import after path modification
from src.common.callbacks import (  # noqa: E402
    DatabaseUpdateCallback,
    ResourceMonitorCallback,
)
from src.jobs.training_data import TrainingJobData  # noqa: E402
from src.jobs.training_dispatcher import (  # noqa: E402
    DispatcherConfig,
    TrainingJobDispatcher,
)
from src.train.callbacks.early_stopping import EarlyStoppingCallback  # noqa: E402
from src.train.callbacks.model_checkpoint import ModelCheckpoint  # noqa: E402


async def main():
    """Run a demo of the TrainingJobDispatcher."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # Create dispatcher with custom configuration
    config = DispatcherConfig(
        max_concurrent_jobs=2,  # Allow 2 concurrent jobs
        polling_interval=10.0,  # Check for new jobs every 10 seconds
        auto_start=False,  # Don't start automatically
    )

    # Create custom callbacks
    callbacks = [
        DatabaseUpdateCallback(update_frequency=1, verbose=True),
        ResourceMonitorCallback(monitoring_interval=15.0),
    ]

    # Create dispatcher
    dispatcher = TrainingJobDispatcher(config=config, callbacks=callbacks)

    # Start the dispatcher
    await dispatcher.start()

    try:
        # Option 1: Create and schedule jobs manually
        print("Creating and scheduling training jobs manually...")
        model_run_uuid = "00000000-0000-0000-0000-000000000001"

        # Example training job data
        job_data = TrainingJobData(
            model_run_uuid=model_run_uuid,
            profile="development",
            model_components={
                "model": None,  # Would be a real model in production
                "loss_fn": None,
                "optimizer": None,
            },
            data_loaders={
                "train": None,  # Would be real data loaders in production
                "test": None,
            },
            training_config={
                "epochs": 10,
                "model_id": "test-model",
                "model_run_uuid": model_run_uuid,
            },
            callbacks=[
                EarlyStoppingCallback(patience=3),
                ModelCheckpoint(model_run_uuid=model_run_uuid),
            ],
        )

        # Schedule the job
        job_id = await dispatcher.create_training_job(
            model_run_uuid=job_data.model_run_uuid,
            training_data={
                "model_components": job_data.model_components,
                "data_loaders": job_data.data_loaders,
                "training_config": job_data.training_config,
            },
            priority=100,  # High priority
            profile="development",
        )

        print(f"Scheduled job with ID: {job_id}")

        # Option 2: Let the dispatcher poll for jobs
        print("Letting dispatcher poll for scheduled jobs...")

        # In a real application, you would create model runs with "scheduled" status
        # in the database, and the dispatcher would pick them up automatically.

        # Wait for a while to let the dispatcher process jobs
        await asyncio.sleep(10)

        # Get status of our job
        status = await dispatcher.get_job_status(job_id)
        print(f"Job {job_id} status: {status}")

        # Cancel a job (if it's still running)
        if status and status.value in ["scheduled", "running"]:
            print(f"Cancelling job {job_id}...")
            cancelled = await dispatcher.cancel_job(job_id)
            print(f"Job cancelled: {cancelled}")

        # Wait a bit more to see the results
        await asyncio.sleep(5)

    finally:
        # Stop the dispatcher
        print("Stopping dispatcher...")
        await dispatcher.stop()


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
