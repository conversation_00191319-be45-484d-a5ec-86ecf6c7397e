# Manual Test Scripts

This directory contains scripts for manually testing various components of the `coiny-classifier` project. These scripts are intended for ad-hoc testing, debugging, or deeper exploration of specific functionalities outside the automated test suite.

## Prerequisites

Before running any manual test script:

1. **Environment Setup**: Ensure your project's Python virtual environment is activated and environment variables are configured in `.env.local`:
   - `COINY_CLASSIFIER_SUPABASE_DEV_URL/KEY` (for development database)
   - `COINY_CLASSIFIER_SUPABASE_STAGING_URL/KEY` (for staging database)
   - `COINY_CLASSIFIER_IMAGES_BASE_DIR` (for image storage)

2. **Project Root**: Always run scripts from the project root directory:

   ```bash
   cd /path/to/coiny-classifier
   python tests/manual/script_name.py
   ```

## Core Manual Tests

### Dataset and Data Processing

* **`test_dataset_fetching.py`** - **Production Orchestration Dataset Testing**

  Tests the complete dataset preparation workflow using the same `DataLoadingService` orchestration that powers the training pipeline.

  **Features:**
  - Downloads real images to isolated test directories
  - Verifies dataset completeness with automatic recovery
  - Creates train/test data loaders with proper splits
  - Generates label mappings and class information
  - Provides comprehensive test reports

  **Usage:**

  ```bash
  # Basic usage with development database
  python tests/manual/test_dataset_fetching.py <dataset_uuid>

  # Using staging database
  python tests/manual/test_dataset_fetching.py <dataset_uuid> --profile staging

  # Custom output directory
  python tests/manual/test_dataset_fetching.py <dataset_uuid> --output-dir /custom/path
  ```

  **Output Structure:**
  ```
  tests/manual/output/datasets/{dataset_uuid}_{timestamp}/
  ├── datasets/                   # Downloaded images (isolated from production)
  ├── runs/                       # Model runs (isolated from production)
  ├── test_results_{uuid}.json    # Complete test results
  └── dataset_report_{uuid}.json  # Dataset analysis report
  ```

* **`test_data_utils.py`** - **Test Data Generation Utilities**

  Provides utilities for generating synthetic test data, specifically Gaussian blur images for testing CNN models.

### Training and Model Management

* **`test_dynamic_cnn_pipeline.py`** - **Concurrent Training Pipeline Testing**

  Tests the `TrainingJobDispatcher`'s concurrency capabilities by running multiple training jobs simultaneously.

  **Features:**
  - Concurrent job execution with resource management
  - Progress monitoring with detailed status updates
  - Model creation using `MLModelFactory`
  - Data loading with augmentation support
  - Training loop using `ModelTrainer`
  - Saving of model checkpoints, metrics, and plots

  **Usage:**

  ```bash
  python tests/manual/test_dynamic_cnn_pipeline.py
  ```

* **`test_training_dispatcher.py`** - **Training Job Dispatcher Demo**

  Demonstrates how to use the `TrainingJobDispatcher` to schedule and execute training jobs asynchronously.

* **`test_automatic_class_detection.py`** - **Automatic Class Detection Testing**

  Tests the automatic class detection functionality that determines the number of classes from datasets and validates model compatibility.

### Augmentation and Visualization

* **`inspect_augmentations.py`** - **Comprehensive Augmentation Inspection**

  Provides complete augmentation verification and debugging capabilities.

  **Features:**
  - Visual comparison of original vs augmented images
  - Statistical analysis with augmentation-specific detection algorithms
  - Individual augmentation testing to isolate effects
  - DataLoader integration testing for training pipeline verification

  **Outputs:**

  ```markdown
  tests/manual/output/augmentation_inspection/
  ├── augmentation_comparison.png      # Side-by-side comparisons
  ├── individual_augmentations.png     # Each augmentation separately
  └── dataloader_batch_samples.png     # Sample batch from DataLoader
  ```

  **Usage:**

  ```bash
  python tests/manual/inspect_augmentations.py
  ```

### Caching and Performance

* **`simple_cache_demo.py`** - **Dataset Cache Invalidation Demo**

  Demonstrates the dataset cache invalidation system functionality using the `DatasetStateCacheService`.

  **Features:**
  - Cache operations and key generation
  - Automatic invalidation on content updates
  - Manual cache management
  - Real-world usage scenarios with comprehensive output

  **Usage:**

  ```bash
  python tests/manual/simple_cache_demo.py
  ```

## Examples and Demonstrations

### `examples/` Directory

* **`augmentation_demo.py`** - Demonstrates augmentation module capabilities including pipeline creation, validation, and usage
* **`augmentation_training_demo.py`** - Shows augmentation integration in training workflows
* **`model_run_callback_usage.py`** - Examples of using model run callbacks for monitoring
* **`model_run_service_usage.py`** - Demonstrates `ModelRunService` for updating model run data

### `models/` Directory

* **`ml_factory_manual_script.py`** - Tests the `MLModelFactory` for creating models, loss functions, optimizers, schedulers, and managing model persistence

### `api/` Directory

* **`routes/test_train.py`** - Manual testing of API training endpoints

## Output Management

All manual tests use centralized output management through `src/config/paths.py`:

- **Test outputs**: `tests/manual/output/` (isolated from production)
- **Dataset downloads**: Isolated in test-specific directories
- **Model runs**: Separate from production `/runs` directory
- **Logs and reports**: Organized by test type and timestamp

## Adding New Manual Tests

When adding a new manual test script:

1. **Directory Structure**: Place scripts in subdirectories that mirror the `src/` structure
2. **Documentation**: Include clear docstrings with usage instructions and feature descriptions
3. **Isolation**: Use `get_manual_test_output_dir()` for outputs to avoid corrupting production data
4. **Dependencies**: Ensure scripts can run from project root with proper imports
5. **Update Documentation**: Add the new script to this README with description and usage examples
