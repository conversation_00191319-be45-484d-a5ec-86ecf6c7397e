"""
Test data utilities for manual testing and integration tests.

This module provides utilities for generating test data, specifically Gaussian blur
images for testing CNN models. These utilities are only used in tests and should
not be imported by production code.
"""

import os

# Import from the src directory for test usage
import sys

import numpy as np
import torch
from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
sys.path.insert(0, project_root)

from src.datasets.augmented_dataset import AugmentedDataset  # noqa: E402


def generate_gaussian_blurs(image_size=10, number_per_class=10):
    """
    Generate synthetic Gaussian blur images for testing CNN models.

    This function creates two classes of images with different Gaussian blur widths,
    useful for testing binary classification models.

    Parameters:
    image_size (int): Size of the square images (image_size x image_size)
    number_per_class (int): Number of images to generate per class

    Returns:
    tuple: (images, labels) where:
        - images: torch.Tensor of shape (2*number_per_class, 1, image_size, image_size)
        - labels: torch.Tensor of shape (2*number_per_class, 1) with binary labels
    """
    x = np.linspace(-4, 4, image_size)
    X, Y = np.meshgrid(x, x)

    # Two different widths for the two classes
    widths = [1.8, 2.4]

    # Initialize tensors containing images and labels
    images = torch.zeros(2 * number_per_class, 1, image_size, image_size)
    labels = torch.zeros(2 * number_per_class)

    for i in range(2 * number_per_class):
        # Create the gaussian with random centers
        ro = 2 * np.random.randn(2)  # ro = random offset
        gaussian_kernel = np.exp(
            -((X - ro[0]) ** 2 + (Y - ro[1]) ** 2) / (2 * widths[i % 2] ** 2)
        )

        # Add noise
        gaussian_kernel = gaussian_kernel + np.random.randn(image_size, image_size) / 5

        # Add to the tensor
        images[i, :, :, :] = torch.Tensor(gaussian_kernel).view(
            1, image_size, image_size
        )
        labels[i] = i % 2

    labels = labels[:, None]
    return images, labels


def _prepare_data_splits(images, labels, test_size, convert_to_rgb=False):
    """Split data and optionally convert grayscale to RGB."""
    train_data, test_data, train_labels, test_labels = train_test_split(
        images, labels, test_size=test_size
    )

    # Convert grayscale to RGB only if explicitly requested (for augmentation compatibility)
    if convert_to_rgb and train_data.shape[1] == 1:
        train_data = train_data.repeat(1, 3, 1, 1)
        test_data = test_data.repeat(1, 3, 1, 1)

    return train_data, test_data, train_labels, test_labels


def _create_data_loaders(train_dataset, test_dataset, batch_size):
    """Create DataLoader instances from datasets."""
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True, drop_last=True
    )
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    return train_loader, test_loader


def augmented_data_loader(images, labels, *, transforms=None, config=None):
    """
    Create data loaders with augmentation support for testing purposes.

    This function is specifically designed for test scenarios and should not be
    used in production code. It creates train/test data loaders from tensor data
    with optional augmentation transforms.

    Parameters:
    images (tensor): Image data tensor
    labels (tensor): Label data tensor
    transforms (dict): Dictionary with 'train' and 'val' transform pipelines
    config (dict): Configuration with 'batch_size' and 'test_size'

    Returns:
    tuple: (train_loader, test_loader) with augmentation support
    """
    # Set defaults
    transforms = transforms or {}
    config = config or {}

    # Extract configuration
    train_transforms = transforms.get("train")
    val_transforms = transforms.get("val")
    batch_size = config.get("batch_size", 32)
    test_size = config.get("test_size", 0.1)

    # Prepare data splits - preserve original channel structure
    # Never convert channels automatically - respect original data format
    train_data, test_data, train_labels, test_labels = _prepare_data_splits(
        images, labels, test_size, convert_to_rgb=False
    )

    # Create augmented datasets
    train_dataset = AugmentedDataset(train_data, train_labels, train_transforms)
    test_dataset = AugmentedDataset(test_data, test_labels, val_transforms)

    # Create and return data loaders
    return _create_data_loaders(train_dataset, test_dataset, batch_size)
