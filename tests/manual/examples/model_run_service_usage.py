#!/usr/bin/env python3
"""
Example usage of the ModelRunService for updating model run data.

This example demonstrates how to use the ModelRunService to update
timing fields, metrics, and log paths for model runs.
"""

import asyncio
import json
import tempfile
from datetime import datetime
from pathlib import Path

# Note: Due to circular imports in the existing codebase, we'll create mock classes for this example
# In a real application, you would import like this:
# from database.services import ModelRunService, ModelRunServiceError


class ModelRunServiceError(Exception):
    """Mock exception for example purposes."""

    pass


class ModelRunTimingUpdate:
    """Mock dataclass for example purposes."""

    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)


class ModelRunMetricsUpdate:
    """Mock dataclass for example purposes."""

    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)


class ModelRunCompleteUpdate:
    """Mock dataclass for example purposes."""

    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)


class ModelRunService:
    """Mock service for example purposes."""

    @classmethod
    async def update_model_run_times(cls, update_data):
        print(f"Mock: update_model_run_times called with {update_data.__dict__}")
        return {"uuid": update_data.model_run_uuid, "status": "updated"}

    @classmethod
    async def update_model_run_metrics(cls, update_data):
        print(f"Mock: update_model_run_metrics called with {update_data.__dict__}")
        return {"uuid": update_data.model_run_uuid, "status": "updated"}

    @classmethod
    async def update_model_run_complete(cls, update_data):
        print(f"Mock: update_model_run_complete called with {update_data.__dict__}")
        return {"uuid": update_data.model_run_uuid, "status": "updated"}

    @classmethod
    def load_metrics_from_file(cls, file_path):
        print(f"Mock: load_metrics_from_file called with {file_path}")
        # Actually load the file for demonstration
        import json

        with open(file_path, "r") as f:
            return json.load(f)

    @classmethod
    async def get_model_run(cls, **kwargs):
        print(f"Mock: get_model_run called with {kwargs}")
        return {"uuid": kwargs.get("model_run_uuid"), "status": "found"}


async def example_update_timing_fields():
    """Example: Update timing fields for a model run."""
    print("=== Example: Update Timing Fields ===")

    model_run_uuid = "123e4567-e89b-12d3-a456-************"

    try:
        # Update start and end times
        update_data = ModelRunTimingUpdate(
            model_run_uuid=model_run_uuid,
            start_time=datetime.now(),
            end_time=datetime.now(),
            profile="development",  # Use development database profile
        )
        result = await ModelRunService.update_model_run_times(update_data)

        print(f"✅ Successfully updated timing fields for model run {model_run_uuid}")
        print(f"Updated data: {result}")

    except ModelRunServiceError as e:
        print(f"❌ Error updating timing fields: {e}")


async def example_update_metrics_from_dict():
    """Example: Update metrics from a dictionary."""
    print("\n=== Example: Update Metrics from Dictionary ===")

    model_run_uuid = "123e4567-e89b-12d3-a456-************"

    # Sample metrics data (similar to what would be in metrics_summary.json)
    metrics_data = {
        "final": {
            "train_loss": 0.1234,
            "train_accuracy": 0.8765,
            "test_loss": 0.2345,
            "test_accuracy": 0.7654,
        },
        "timing": {"train_times": [10.5, 11.2, 9.8], "test_times": [2.1, 2.3, 2.0]},
        "resources": {
            "memory_usage": [1024, 1056, 1089],
            "gpu_utilization": [85.2, 87.1, 84.9],
        },
    }

    try:
        update_data = ModelRunMetricsUpdate(
            model_run_uuid=model_run_uuid,
            metrics=metrics_data,
            log_path=True,
            profile="development",
        )
        result = await ModelRunService.update_model_run_metrics(update_data)

        print(f"✅ Successfully updated metrics for model run {model_run_uuid}")
        print(f"Updated data: {result}")

    except ModelRunServiceError as e:
        print(f"❌ Error updating metrics: {e}")


async def example_update_metrics_from_file():
    """Example: Update metrics from a JSON file."""
    print("\n=== Example: Update Metrics from File ===")

    model_run_uuid = "123e4567-e89b-12d3-a456-************"

    # Create a temporary metrics file for demonstration
    metrics_data = {
        "final": {
            "train_loss": 0.0987,
            "train_accuracy": 0.9123,
            "test_loss": 0.1876,
            "test_accuracy": 0.8234,
        },
        "timing": {"total_training_time": 3600.5},
        "resources": {"peak_memory_mb": 2048},
    }

    # Create temporary file
    with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
        json.dump(metrics_data, f, indent=2)
        temp_file_path = Path(f.name)

    try:
        update_data = ModelRunMetricsUpdate(
            model_run_uuid=model_run_uuid,
            metrics_file_path=temp_file_path,
            log_path=True,
            profile="development",
        )
        result = await ModelRunService.update_model_run_metrics(update_data)

        print(
            f"✅ Successfully updated metrics from file for model run {model_run_uuid}"
        )
        print(f"Metrics file: {temp_file_path}")
        print(f"Updated data: {result}")

    except ModelRunServiceError as e:
        print(f"❌ Error updating metrics from file: {e}")
    finally:
        # Clean up temporary file
        if temp_file_path.exists():
            temp_file_path.unlink()


async def example_comprehensive_update():
    """Example: Comprehensive update of all supported fields."""
    print("\n=== Example: Comprehensive Update ===")

    model_run_uuid = "123e4567-e89b-12d3-a456-************"

    # Sample metrics
    metrics_data = {
        "final": {
            "train_loss": 0.0567,
            "train_accuracy": 0.9456,
            "test_loss": 0.1234,
            "test_accuracy": 0.8789,
        },
        "epochs_completed": 50,
        "best_epoch": 42,
        "early_stopping": False,
    }

    try:
        update_data = ModelRunCompleteUpdate(
            model_run_uuid=model_run_uuid,
            start_time=datetime.now(),
            end_time=datetime.now(),
            dataset_content_updated_at=datetime.now(),
            metrics=metrics_data,
            log_path=True,
            profile="development",
        )
        result = await ModelRunService.update_model_run_complete(update_data)

        print(
            f"✅ Successfully performed comprehensive update for model run {model_run_uuid}"
        )
        print(f"Updated data: {result}")

    except ModelRunServiceError as e:
        print(f"❌ Error in comprehensive update: {e}")


async def example_load_metrics_from_file():
    """Example: Load metrics from a file (utility function)."""
    print("\n=== Example: Load Metrics from File (Utility) ===")

    # Create a sample metrics file
    metrics_data = {
        "accuracy": 0.95,
        "precision": 0.92,
        "recall": 0.89,
        "f1_score": 0.905,
    }

    with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
        json.dump(metrics_data, f, indent=2)
        temp_file_path = Path(f.name)

    try:
        # Load metrics using the utility function
        loaded_metrics = ModelRunService.load_metrics_from_file(temp_file_path)

        print(f"✅ Successfully loaded metrics from file: {temp_file_path}")
        print(f"Loaded metrics: {loaded_metrics}")

    except ModelRunServiceError as e:
        print(f"❌ Error loading metrics from file: {e}")
    finally:
        # Clean up
        if temp_file_path.exists():
            temp_file_path.unlink()


async def example_get_model_run():
    """Example: Retrieve model run data."""
    print("\n=== Example: Get Model Run Data ===")

    model_run_uuid = "123e4567-e89b-12d3-a456-************"

    try:
        model_run_data = await ModelRunService.get_model_run(
            model_run_uuid=model_run_uuid, profile="development"
        )

        print(f"✅ Successfully retrieved model run {model_run_uuid}")
        print(f"Model run data: {model_run_data}")

    except ModelRunServiceError as e:
        print(f"❌ Error retrieving model run: {e}")


async def main():
    """Run all examples."""
    print("ModelRunService Usage Examples")
    print("=" * 50)

    # Note: These examples use mock implementations to demonstrate the API
    # In a real environment, you would use the actual ModelRunService

    print("ℹ️  Note: These examples use mock implementations for demonstration.")
    print("ℹ️  In a real environment, you would import from database.services.")
    print()

    # Run all examples with mock implementations:
    await example_update_timing_fields()
    await example_update_metrics_from_dict()
    await example_update_metrics_from_file()
    await example_comprehensive_update()
    await example_load_metrics_from_file()
    await example_get_model_run()


if __name__ == "__main__":
    asyncio.run(main())
