#!/usr/bin/env python3
"""
Augmentation Module Demonstration

This script demonstrates the capabilities of the new augmentations module,
showing how to create, validate, and use augmentation pipelines for both
training and inference.
"""

import sys
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from augmentations import (  # noqa: E402
    AugmentationPipelineFactory,
    AugmentationSerializer,
    AugmentationUtils,
    AugmentationValidator,
    HorizontalFlip,
    NormalizationMean,
    NormalizationStd,
    ResizeDimensions,
    RotationRange,
    ZoomRange,
)


def main():
    """Demonstrate augmentation module capabilities."""
    print("🎨 Augmentation Module Demonstration")
    print("=" * 50)

    # 1. Create augmentation configurations
    print("\n1. Creating Augmentation Configurations")
    print("-" * 40)

    # Create individual augmentations
    augmentations = [
        ResizeDimensions(resize_dimensions=[224, 224]),
        Horizontal<PERSON>lip(horizontal_flip=True),
        Rota<PERSON><PERSON><PERSON><PERSON>(rotation_range=15.0),
        <PERSON>m<PERSON><PERSON><PERSON>(zoom_range=0.1),
        NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
        NormalizationStd(normalization_std=[0.229, 0.224, 0.225]),
    ]

    print(f"Created {len(augmentations)} augmentations:")
    for i, aug in enumerate(augmentations, 1):
        print(f"  {i}. {aug}")

    # 2. Validate augmentations
    print("\n2. Validating Augmentation Configuration")
    print("-" * 40)

    validation_result = AugmentationValidator.validate_configuration(augmentations)
    print(f"Valid: {validation_result['valid']}")
    print(f"Errors: {len(validation_result['errors'])}")
    print(f"Warnings: {len(validation_result['warnings'])}")
    print(f"Summary: {validation_result['summary']}")

    if validation_result["warnings"]:
        print("Warnings:")
        for warning in validation_result["warnings"]:
            print(f"  ⚠️  {warning}")

    # 3. Create training and inference pipelines
    print("\n3. Creating PyTorch Transform Pipelines")
    print("-" * 40)

    training_pipeline = AugmentationPipelineFactory.create_training_pipeline(
        augmentations
    )
    inference_pipeline = AugmentationPipelineFactory.create_inference_pipeline(
        augmentations
    )

    print(f"Training pipeline transforms: {len(training_pipeline.transforms)}")
    for i, transform in enumerate(training_pipeline.transforms, 1):
        print(f"  {i}. {type(transform).__name__}")

    print(f"\nInference pipeline transforms: {len(inference_pipeline.transforms)}")
    for i, transform in enumerate(inference_pipeline.transforms, 1):
        print(f"  {i}. {type(transform).__name__}")

    # 4. Demonstrate utilities
    print("\n4. Utility Functions")
    print("-" * 40)

    # Filter by type
    geometric = AugmentationUtils.get_geometric_augmentations(augmentations)
    normalization = AugmentationUtils.get_normalization_augmentations(augmentations)
    print(f"Geometric augmentations: {len(geometric)}")
    print(f"Normalization augmentations: {len(normalization)}")

    # Get summary
    summary = AugmentationUtils.get_augmentation_summary(augmentations)
    print(f"Summary: {summary}")

    # 5. Create default configurations
    print("\n5. Default Configurations")
    print("-" * 40)

    default_augs = AugmentationUtils.create_default_augmentations(
        image_size=[224, 224], include_normalization=True
    )
    print(f"Default augmentations: {len(default_augs)}")

    minimal_augs = AugmentationUtils.create_minimal_augmentations(image_size=[224, 224])
    print(f"Minimal augmentations: {len(minimal_augs)}")

    # 6. Serialization demonstration
    print("\n6. Serialization for Inference")
    print("-" * 40)

    # Create temporary directory for demo
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Save complete model artifacts
        AugmentationSerializer.save_model_artifacts(
            augmentations=augmentations,
            training_pipeline=training_pipeline,
            inference_pipeline=inference_pipeline,
            output_dir=temp_path,
        )

        print("Saved model artifacts:")
        for file_path in temp_path.iterdir():
            print(f"  📄 {file_path.name}")

        # Load inference artifacts
        artifacts = AugmentationSerializer.load_inference_artifacts(temp_path)
        print(f"\nLoaded artifacts: {list(artifacts.keys())}")

        # Verify loaded pipeline
        loaded_pipeline = artifacts["inference_pipeline"]
        print(f"Loaded pipeline has {len(loaded_pipeline.transforms)} transforms")

    # 7. Performance analysis
    print("\n7. Performance Analysis")
    print("-" * 40)

    performance = validation_result["performance"]
    print(f"Estimated overhead: {performance['estimated_overhead']}")
    print(f"Memory impact: {performance['memory_impact']}")

    if performance["recommendations"]:
        print("Recommendations:")
        for rec in performance["recommendations"]:
            print(f"  💡 {rec}")

    print("\n✅ Demonstration completed successfully!")
    print("\nKey Benefits:")
    print("  🎯 Consistent preprocessing between training and inference")
    print("  🔧 Easy configuration and validation")
    print("  📦 Serializable pipelines for deployment")
    print("  🚀 Performance-optimized PyTorch transforms")
    print("  🧪 Comprehensive testing and validation")


if __name__ == "__main__":
    main()
