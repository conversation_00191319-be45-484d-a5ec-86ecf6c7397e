#!/usr/bin/env python3
"""
Augmentation Training Integration Demo

This script demonstrates the complete integration of the augmentations module
with the training pipeline, showing how augmentations are applied during training
and how artifacts are saved for inference.
"""

import asyncio
import sys
import tempfile
from pathlib import Path

import torch
from torch import nn, optim

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from augmentations import (  # noqa: E402
    AugmentationPipelineFactory,
    AugmentationSerializer,
    HorizontalFlip,
    NormalizationMean,
    NormalizationStd,
    ResizeDimensions,
    RotationRange,
    ZoomRange,
)
from tests.manual.test_data_utils import (  # noqa: E402
    augmented_data_loader,
    generate_gaussian_blurs,
)
from train.trainer import ModelTrainer, TrainerConfig  # noqa: E402


class DemoCNNModel(nn.Module):
    """Simple CNN model for demonstration."""

    def __init__(self):
        super().__init__()
        self.conv1 = nn.Conv2d(3, 16, 3, padding=1)
        self.conv2 = nn.Conv2d(16, 32, 3, padding=1)
        self.pool = nn.AdaptiveAvgPool2d((4, 4))
        self.fc1 = nn.Linear(32 * 4 * 4, 64)
        self.fc2 = nn.Linear(64, 1)
        self.dropout = nn.Dropout(0.5)

    def forward(self, x):
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = self.pool(x)
        x = x.view(x.size(0), -1)
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        return torch.sigmoid(self.fc2(x)).squeeze(1)  # Remove extra dimension


async def main():
    """Main demonstration function."""
    print("🚀 Augmentation Training Integration Demo")
    print("=" * 50)

    # 1. Define augmentations
    print("\n1. Defining augmentations...")
    augmentations = [
        ResizeDimensions(resize_dimensions=[64, 64]),
        HorizontalFlip(horizontal_flip=True),
        RotationRange(rotation_range=15.0),
        ZoomRange(zoom_range=0.1),
        NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
        NormalizationStd(normalization_std=[0.229, 0.224, 0.225]),
    ]

    print(f"   ✓ Configured {len(augmentations)} augmentations:")
    for i, aug in enumerate(augmentations, 1):
        print(f"     {i}. {type(aug).__name__}: {aug.model_dump()}")

    # 2. Create augmentation pipelines
    print("\n2. Creating augmentation pipelines...")
    train_pipeline = AugmentationPipelineFactory.create_training_pipeline(augmentations)
    inference_pipeline = AugmentationPipelineFactory.create_inference_pipeline(
        augmentations
    )

    print(f"   ✓ Training pipeline: {len(train_pipeline.transforms)} transforms")
    print(f"   ✓ Inference pipeline: {len(inference_pipeline.transforms)} transforms")

    # 3. Create data loaders with augmentations
    print("\n3. Creating augmented data loaders...")

    # Generate mock data
    images, labels = generate_gaussian_blurs(image_size=64, number_per_class=50)

    # Create augmented data loaders
    train_loader, test_loader = augmented_data_loader(
        images,
        labels,
        transforms={"train": train_pipeline, "val": inference_pipeline},
        config={"batch_size": 16, "test_size": 0.2},
    )

    data_loaders = {"train": train_loader, "test": test_loader}
    print(f"   ✓ Created data loaders: {list(data_loaders.keys())}")
    print(
        f"     train: {len(train_loader.dataset)} samples, {len(train_loader)} batches"
    )
    print(f"     test: {len(test_loader.dataset)} samples, {len(test_loader)} batches")

    # 4. Test data loading with augmentations
    print("\n4. Testing augmented data loading...")
    train_loader = data_loaders["train"]
    sample_batch = next(iter(train_loader))
    images, labels = sample_batch

    print(f"   ✓ Sample batch shape: {images.shape}")
    print(f"   ✓ Labels shape: {labels.shape}")
    print(f"   ✓ Image dtype: {images.dtype}")
    print(f"   ✓ Image range: [{images.min():.3f}, {images.max():.3f}]")

    # 5. Create and configure model
    print("\n5. Setting up model and training...")
    model = DemoCNNModel()
    loss_fn = nn.BCELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    print(f"   ✓ Model: {model.__class__.__name__}")
    print(f"   ✓ Loss function: {loss_fn.__class__.__name__}")
    print(f"   ✓ Optimizer: {optimizer.__class__.__name__}")

    # 6. Train with augmentations
    print("\n6. Training with augmentations...")

    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"   ✓ Using temporary directory: {temp_dir}")

        # Add augmentation metadata to data loaders (simulating TrainingJobDispatcher)
        data_loaders["_augmentation_metadata"] = {
            "augmentations": augmentations,
            "train_transforms": train_pipeline,
            "val_transforms": inference_pipeline,
            "augmentation_count": len(augmentations),
        }

        # Create trainer config
        trainer_config = TrainerConfig(
            model_components={
                "model": model,
                "loss_fn": loss_fn,
                "optimizer": optimizer,
            },
            data_loaders=data_loaders,
            training_config={
                "epochs": 2,  # Short training for demo
                "model_id": "demo-model",
                "run_output_dir": temp_dir,
            },
        )

        # Create and run trainer
        trainer = ModelTrainer(trainer_config)

        print("   ✓ Starting training...")
        metrics = trainer.train()

        print("   ✓ Training completed!")
        print(f"     Final train loss: {metrics.storage.train.losses[-1]:.4f}")
        print(f"     Final train accuracy: {metrics.storage.train.accuracies[-1]:.2f}%")
        print(f"     Final val loss: {metrics.storage.test.losses[-1]:.4f}")
        print(f"     Final val accuracy: {metrics.storage.test.accuracies[-1]:.2f}%")

        # 7. Verify saved artifacts
        print("\n7. Verifying saved artifacts...")
        output_dir = Path(temp_dir)

        # Check model artifacts
        model_file = output_dir / "model.pt"
        print(f"   ✓ Model saved: {model_file.exists()}")

        # Check augmentation artifacts
        aug_config = output_dir / "augmentation_config.json"
        inference_transforms = output_dir / "inference_transforms.pkl"
        training_transforms = output_dir / "training_transforms.pkl"
        metadata = output_dir / "augmentation_metadata.json"

        print(f"   ✓ Augmentation config: {aug_config.exists()}")
        print(f"   ✓ Inference transforms: {inference_transforms.exists()}")
        print(f"   ✓ Training transforms: {training_transforms.exists()}")
        print(f"   ✓ Metadata: {metadata.exists()}")

        # 8. Test inference pipeline loading
        print("\n8. Testing inference pipeline loading...")

        if inference_transforms.exists():
            # Load the saved inference pipeline
            loaded_pipeline = AugmentationSerializer.load_pipeline(inference_transforms)
            transform_count = len(loaded_pipeline.transforms)
            print(f"   ✓ Loaded inference pipeline: {transform_count} transforms")

            # Test with a sample image
            sample_image = torch.randn(3, 32, 32)  # Random image
            processed_image = loaded_pipeline(sample_image)
            print(f"   ✓ Processed image shape: {processed_image.shape}")
            print(f"   ✓ Image resized to: {processed_image.shape[-2:]}")

            # Test model inference
            model.eval()
            with torch.no_grad():
                prediction = model(processed_image.unsqueeze(0))
                print(f"   ✓ Model prediction: {prediction.item():.4f}")

        # 9. Show file sizes
        print("\n9. Artifact file sizes...")
        for file_path in [
            model_file,
            aug_config,
            inference_transforms,
            training_transforms,
            metadata,
        ]:
            if file_path.exists():
                size_kb = file_path.stat().st_size / 1024
                print(f"   {file_path.name}: {size_kb:.1f} KB")

    print("\n" + "=" * 50)
    print("✅ Demo completed successfully!")
    print("\nKey takeaways:")
    print("• Augmentations are automatically applied during training")
    print("• Separate pipelines for training (random) and inference (deterministic)")
    print("• All artifacts are saved for deployment and inference")
    print("• Training/inference consistency is guaranteed")
    print("• Integration is seamless with existing training infrastructure")


if __name__ == "__main__":
    asyncio.run(main())
