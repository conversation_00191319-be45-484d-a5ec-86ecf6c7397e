#!/usr/bin/env python3
"""
Manual test script for dataset fetching using production orchestration.

This script tests the complete dataset preparation workflow using the same
DataLoadingService orchestration that powers the training pipeline.

WHAT IT DOES:
- Uses DataLoadingService.create_data_loaders() for complete orchestration
- Fetches dataset metadata and verifies completeness
- Downloads and recovers missing images automatically
- Creates train/test data loaders with proper splits
- Generates label mappings and class information
- Saves comprehensive test reports

USAGE:
    python tests/manual/test_dataset_fetching.py <dataset_uuid> [options]

EXAMPLES:
    # Basic usage with development database
    python tests/manual/test_dataset_fetching.py VALID_DATASET_UUID

    # Using staging database
    python tests/manual/test_dataset_fetching.py VALID_DATASET_UUID --profile staging

    # Custom output directory
    python tests/manual/test_dataset_fetching.py VALID_DATASET_UUID --output-dir /custom/path

PREREQUISITES:
    1. Environment variables in .env.local:
       - COINY_CLASSIFIER_SUPABASE_DEV_URL/KEY (for development)
       - COINY_CLASSIFIER_SUPABASE_STAGING_URL/KEY (for staging)
       - COINY_CLASSIFIER_IMAGES_BASE_DIR (for image storage)

    2. Run from project root directory:
       cd /path/to/coiny-classifier
       python tests/manual/test_dataset_fetching.py <uuid>

OUTPUT STRUCTURE:
    tests/manual/output/datasets/{dataset_uuid}_{timestamp}/
    ├── datasets/                   # Downloaded images (isolated from production)
    │   └── {dataset_uuid}/
    │       └── images/
    │           └── {coin_side_uuid}/
    │               └── {image_uuid}.jpg
    ├── runs/                       # Model runs (isolated from production)
    │   └── {model_run_uuid}/
    │       └── logs/
    │           └── dataset.log     # Service operation logs
    ├── test_results_{uuid}.json    # Complete test results
    ├── test_summary_{uuid}.txt     # Human-readable summary
    ├── manual_fetch_{uuid}.log     # Test execution logs
    └── labels/                     # Generated label mappings
        ├── label_mapping.json
        ├── class_names.json
        └── label_metadata.json

PRODUCTION PARITY:
This script uses the exact same DataLoadingService orchestration as the
training pipeline, ensuring real-world accuracy and automatic maintenance.
"""

import argparse
import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Optional
from uuid import UUID

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

# Import configuration first to avoid circular imports
import os  # noqa: E402

os.environ.setdefault("COINY_CLASSIFIER_SUPABASE_PROFILE", "development")

# Import basic modules first
from config.paths import get_manual_test_output_dir  # noqa: E402

# Import dataset orchestration service
from datasets.data_loading_service import (  # noqa: E402
    DataLoaderConfig,
    DataLoadingService,
    DataProcessingConfig,
    DataSplitConfig,
)


class DatasetFetchingTester:
    """Manual tester for dataset fetching functionality."""

    def __init__(
        self,
        dataset_uuid: str,
        profile: Optional[str] = None,
        output_dir: Optional[Path] = None,
    ):
        """
        Initialize the dataset fetching tester.

        Args:
            dataset_uuid: UUID of the dataset to fetch
            profile: Optional database profile to use
            output_dir: Optional custom output directory
        """
        self.dataset_uuid = dataset_uuid
        self.profile = profile
        self.timestamp = int(time.time())
        self.test_run_uuid = f"test_{self.timestamp}"

        # Create organized test directory structure
        if output_dir:
            self.output_dir = Path(output_dir)
        else:
            base_test_dir = get_manual_test_output_dir("datasets")
            self.output_dir = base_test_dir / f"{dataset_uuid}_{self.timestamp}"

        # Create the test-specific dataset directory for downloads
        self.test_dataset_dir = self.output_dir / "datasets"

        # Create the test-specific runs directory for model runs
        self.test_runs_dir = self.output_dir / "runs"

        # Setup logging
        self.logger = self._setup_logging()

        # Results storage
        self.results = {
            "dataset_uuid": dataset_uuid,
            "profile": profile,
            "test_run_uuid": self.test_run_uuid,
            "start_time": time.time(),
            "steps": {},
            "errors": [],
            "summary": {},
        }

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the test using enhanced dataset logging configuration."""
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Note: DataLoadingService creates its own dataset logger that writes to
        # runs/{model_run_uuid}/logs/dataset.log - no need for a separate one here

        # Also setup a test-specific logger for the output directory
        test_logger = logging.getLogger(f"dataset_fetching_test_{self.test_run_uuid}")
        test_logger.setLevel(logging.INFO)

        # Clear existing handlers to avoid duplicates
        if test_logger.hasHandlers():
            test_logger.handlers.clear()

        # File handler for test output directory
        log_file = (
            self.output_dir / f"manual_fetch_{self.dataset_uuid}_{self.timestamp}.log"
        )
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # Formatter
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        test_logger.addHandler(file_handler)
        test_logger.addHandler(console_handler)

        # Store test logger for use
        # Note: dataset_logger is handled by DataLoadingService

        return test_logger

    def _record_step(
        self, step_name: str, success: bool, data: Dict = None, error: str = None
    ):
        """Record the result of a test step."""
        self.results["steps"][step_name] = {
            "success": success,
            "timestamp": time.time(),
            "data": data or {},
            "error": error,
        }
        if error:
            self.results["errors"].append({"step": step_name, "error": error})

    async def _test_orchestrated_dataset_preparation(self):
        """Test dataset preparation using production orchestration service."""
        self.logger.info("STEP 1: Running orchestrated dataset preparation")

        try:
            # Create a mock model run UUID for the test
            test_model_run_uuid = f"test-{self.dataset_uuid[:8]}"

            # Configure DataLoadingService
            config = DataLoaderConfig(
                model_run_uuid=test_model_run_uuid,
                profile=self.profile,
                processing_config=DataProcessingConfig(
                    batch_size=32,
                    use_mock_data=False,
                    dataset_batch_size=10,  # Use small batch size for testing
                ),
                split_config=DataSplitConfig(test_size=0.2),
            )

            # Use DataLoadingService orchestration - this handles:
            # 1. Dataset metadata fetching
            # 2. Dataset completeness verification and recovery
            # 3. Image downloading
            # 4. Dataset sets loading
            # 5. ImageDataset creation
            # 6. Label mapping generation
            # Set test isolation parameters in config
            config.test_config.base_dir = str(self.test_dataset_dir)
            config.test_config.runs_base_dir = str(self.test_runs_dir)

            data_loaders = await DataLoadingService.create_data_loaders(
                dataset_uuid=self.dataset_uuid,
                augmentations=None,  # No augmentations for testing
                config=config,
            )

            # Store results for reporting
            self.data_loaders = data_loaders
            self.train_dataset = data_loaders["train"].dataset
            self.test_dataset = data_loaders["test"].dataset

            # Extract dataset info from the train dataset
            if hasattr(self.train_dataset, "dataset_sets"):
                # For AugmentedDataset, get the underlying dataset
                underlying_dataset = getattr(
                    self.train_dataset, "dataset", self.train_dataset
                )
                if hasattr(underlying_dataset, "get_dataset_info"):
                    self.dataset_info = underlying_dataset.get_dataset_info()
                if hasattr(underlying_dataset, "get_label_mapping"):
                    self.label_mapping = underlying_dataset.get_label_mapping()
                if hasattr(underlying_dataset, "get_class_names"):
                    self.class_names = underlying_dataset.get_class_names()

            self._record_step("orchestrated_dataset_preparation", True)

        except Exception as e:
            error_msg = f"Orchestrated dataset preparation failed: {str(e)}"
            self.logger.error(error_msg)
            self._record_step(
                "orchestrated_dataset_preparation", False, error=error_msg
            )
            raise

    async def run_complete_test(self) -> Dict:
        """
        Run the complete dataset fetching test.

        Returns:
            Dictionary containing test results
        """
        self.logger.info("Starting dataset fetching test for %s", self.dataset_uuid)
        self.logger.info("Using DataLoadingService orchestration - all steps automated")

        try:
            # Use the production orchestration service instead of manual steps
            await self._test_orchestrated_dataset_preparation()

            # Calculate duration before generating report
            self.results["end_time"] = time.time()
            self.results["duration"] = (
                self.results["end_time"] - self.results["start_time"]
            )

            # Generate final report
            await self._generate_final_report()

        except Exception as e:
            self.logger.error("Test failed with exception: %s", str(e))
            self._record_step("overall_test", False, error=str(e))

            # Calculate duration even if test failed
            self.results["end_time"] = time.time()
            self.results["duration"] = (
                self.results["end_time"] - self.results["start_time"]
            )

        return self.results

    async def _generate_final_report(self):
        """Generate final test report."""
        self.logger.info("STEP 8: Generating final report")

        try:
            # Calculate summary statistics
            total_steps = len(self.results["steps"])
            successful_steps = sum(
                1 for step in self.results["steps"].values() if step["success"]
            )

            # Extract statistics from orchestrated results
            self.results["summary"] = {
                "total_steps": total_steps,
                "successful_steps": successful_steps,
                "failed_steps": total_steps - successful_steps,
                "success_rate": (
                    successful_steps / total_steps if total_steps > 0 else 0
                ),
                "dataset_uuid": self.dataset_uuid,
                "data_loaders_created": hasattr(self, "data_loaders"),
                "train_dataset_length": (
                    len(self.train_dataset) if hasattr(self, "train_dataset") else 0
                ),
                "test_dataset_length": (
                    len(self.test_dataset) if hasattr(self, "test_dataset") else 0
                ),
                "dataset_info": getattr(self, "dataset_info", {}),
                "label_mapping": getattr(self, "label_mapping", {}),
                "class_names": getattr(self, "class_names", []),
                "num_classes": len(getattr(self, "class_names", [])),
            }

            # Save detailed results to JSON
            results_file = self.output_dir / f"test_results_{self.test_run_uuid}.json"
            with results_file.open("w") as f:
                json.dump(self.results, f, indent=2, default=str)

            # Generate summary report
            summary_file = self.output_dir / f"test_summary_{self.test_run_uuid}.txt"
            with summary_file.open("w") as f:
                f.write("DATASET FETCHING TEST SUMMARY\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Dataset UUID: {self.dataset_uuid}\n")
                f.write(f"Profile: {self.profile or 'default'}\n")
                f.write(f"Test Run UUID: {self.test_run_uuid}\n")
                f.write(f"Duration: {self.results['duration']:.2f} seconds\n\n")

                f.write("RESULTS:\n")
                f.write(f"  Total Steps: {total_steps}\n")
                f.write(f"  Successful: {successful_steps}\n")
                f.write(f"  Failed: {self.results['summary']['failed_steps']}\n")
                f.write(
                    f"  Success Rate: {self.results['summary']['success_rate']:.2%}\n\n"
                )

                # Use data from orchestrated results
                summary = self.results["summary"]

                f.write("DATASET INFO:\n")
                f.write(f"  Dataset UUID: {summary.get('dataset_uuid', 'N/A')}\n")
                f.write(
                    f"  Data Loaders Created: {summary.get('data_loaders_created', False)}\n"
                )
                f.write(
                    f"  Train Dataset Length: {summary.get('train_dataset_length', 0)}\n"
                )
                f.write(
                    f"  Test Dataset Length: {summary.get('test_dataset_length', 0)}\n"
                )
                f.write(f"  Number of Classes: {summary.get('num_classes', 0)}\n")
                class_names = summary.get("class_names", [])
                f.write(
                    f"  Class Names: {class_names[:5]}{'...' if len(class_names) > 5 else ''}\n\n"
                )

                f.write("STEP DETAILS:\n")
                for step_name, step_data in self.results["steps"].items():
                    status = "✓" if step_data["success"] else "✗"
                    f.write(f"  {status} {step_name.replace('_', ' ').title()}\n")
                    if not step_data["success"] and step_data.get("error"):
                        f.write(f"    Error: {step_data['error']}\n")

            self.logger.info(
                "TEST COMPLETED: %d/%d steps successful (%.1fs)",
                successful_steps,
                total_steps,
                self.results["duration"],
            )
            self.logger.info("Reports saved to: %s", self.output_dir)

            self._record_step("generate_final_report", True)

        except Exception as e:
            error_msg = f"Failed to generate final report: {str(e)}"
            self.logger.error(error_msg)
            self._record_step("generate_final_report", False, error=error_msg)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Manual test script for dataset fetching functionality",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python tests/manual/test_dataset_fetching.py DATASET_UUID
  python tests/manual/test_dataset_fetching.py DATASET_UUID --profile staging
  python tests/manual/test_dataset_fetching.py DATASET_UUID --output-dir /custom/path

Valid profiles: development, staging, production
        """,
    )

    parser.add_argument("dataset_uuid", help="UUID of the dataset to fetch and test")

    parser.add_argument(
        "--profile",
        choices=["development", "staging", "production"],
        default=None,
        help="Database profile to use (default: development)",
    )

    parser.add_argument(
        "--output-dir",
        type=Path,
        default=None,
        help="Custom output directory for test results (default: tests/manual/output/datasets)",
    )

    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )

    return parser.parse_args()


async def main():
    """Main function to run the dataset fetching test."""
    args = parse_arguments()

    # Validate UUID format
    try:
        UUID(args.dataset_uuid)
    except ValueError:
        print(f"Error: Invalid UUID format: {args.dataset_uuid}")
        sys.exit(1)

    print(f"Dataset Fetching Test: {args.dataset_uuid}")

    # Create and run the tester
    tester = DatasetFetchingTester(
        dataset_uuid=args.dataset_uuid, profile=args.profile, output_dir=args.output_dir
    )

    # Set logging level if verbose
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        results = await tester.run_complete_test()

        # Exit with appropriate code
        failed_steps = results.get("summary", {}).get(
            "failed_steps", len(results.get("errors", []))
        )
        if failed_steps > 0:
            print(f"\nTest completed with {failed_steps} failures.")
            sys.exit(1)
        else:
            print("\nTest completed successfully!")
            sys.exit(0)

    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
        sys.exit(130)
    except Exception as e:
        print(f"\nTest failed with unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
