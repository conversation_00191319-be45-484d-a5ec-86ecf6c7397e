# /tests/manual/models/test_ml_factory_manual.py
"""
Manual test script for MLModelFactory.

This script allows for interactive testing of the MLModelFactory's capabilities,
including model creation, loss function, optimizer, and scheduler instantiation,
as well as model persistence (saving, loading, discarding).

To run this script:
1. Ensure your virtual environment is activated:
   source .env/bin/activate  (or equivalent for your shell)
2. Navigate to the project root directory (coiny-classifier).
3. Execute the script:
   .env/bin/python tests/manual/models/test_ml_factory_manual.py
"""

import sys
import tempfile
import uuid
from pathlib import Path

# --- Path Setup ---
# Ensure the script can find modules in the 'src' directory when run directly.
# This adds the project root to sys.path, allowing `from src...` imports.
try:
    SCRIPT_FILE_PATH = Path(__file__).resolve()
    # Assuming the script is at /coiny-classifier/tests/manual/models/test_ml_factory_manual.py
    # Project root is three levels up from the script's directory.
    PROJECT_ROOT = SCRIPT_FILE_PATH.parent.parent.parent
    if str(PROJECT_ROOT) not in sys.path:
        sys.path.insert(0, str(PROJECT_ROOT))
except NameError:
    # __file__ is not defined, happens when running in certain interactive environments
    # Fallback: Assume current working directory is project root if __file__ is not set.
    PROJECT_ROOT = Path.cwd()
    if str(PROJECT_ROOT) not in sys.path:
        sys.path.insert(0, str(PROJECT_ROOT))

# Verify import after path setup
try:
    import torch  # Ensure torch is importable as well
    from torch import nn

    from src.models.ml_factory import MLModelFactory
except ImportError as e:
    print(
        "Error: Could not import necessary modules. Please ensure you are in the project root"
    )
    print(f"and your virtual environment is activated. Details: {e}")
    print(f"PROJECT_ROOT determined as: {PROJECT_ROOT}")
    print(f"sys.path: {sys.path}")
    sys.exit(1)
# --- End Path Setup ---


def _test_model_creation(factory: MLModelFactory):
    """Tests the model creation functionality of the MLModelFactory."""
    print("\n--- Testing Model Creation ---")
    try:
        # Dynamic CNN
        cnn_params = {
            "name": "CNN",
            "conv_layers": [
                {
                    "out_channels": 3,
                    "kernel_size": 3,
                    "stride": 1,
                    "padding": 1,
                    "activation": "relu",
                    "batch_norm": True,
                },
                {
                    "out_channels": 6,
                    "kernel_size": 3,
                    "stride": 1,
                    "padding": 1,
                    "activation": "relu",
                    "batch_norm": True,
                },
            ],
            "pool_type": "max",
            "pool_kernel_size": 2,
            "pool_stride": 2,
            "fc_layers": [50, 2],
            "fc_activation": "relu",
            "dropout_rate": 0.5,
            "image_size": 91,  # Ensure this matches expected input for fc layer calculation
            "image_channels": 1,
        }
        cnn_model = factory.create_model(
            cnn_params, num_classes=2
        )  # num_classes in params is for final layer, this arg might be redundant or for override
        print(f"Successfully created Dynamic CNN model: {type(cnn_model)}")
        assert isinstance(cnn_model, nn.Sequential)

        # ResNet
        resnet_params = {"name": "ResNet", "variant": "18", "pretrained": False}
        resnet_model = factory.create_model(resnet_params, num_classes=10)
        print(f"Successfully created ResNet model: {type(resnet_model)}")
        assert resnet_model.fc.out_features == 10

        # Test unsupported model
        print("Testing creation of unsupported model...")
        try:
            factory.create_model({"name": "NonExistentNet"})
        except ValueError as e:
            print(f"Correctly caught error for unsupported model: {e}")

    except Exception as e:
        print(f"Error during model creation: {e}")
        raise  # Re-raise to see traceback for debugging


def _test_loss_function_creation(factory: MLModelFactory):
    """Tests the loss function creation functionality of the MLModelFactory."""
    print("\n--- Testing Loss Function Creation ---")
    try:
        ce_loss_params = {"type": "ce"}
        ce_loss_fn = factory.create_loss_function(ce_loss_params)
        print(f"Successfully created CrossEntropyLoss: {type(ce_loss_fn)}")
        assert isinstance(ce_loss_fn, nn.CrossEntropyLoss)

        # Test unsupported loss
        print("Testing creation of unsupported loss function...")
        try:
            factory.create_loss_function({"type": "imaginary_loss"})
        except ValueError as e:
            print(f"Correctly caught error for unsupported loss: {e}")
    except Exception as e:
        print(f"Error during loss function creation: {e}")
        raise


def _test_optimizer_creation(factory: MLModelFactory):
    """Tests the optimizer creation functionality of the MLModelFactory."""
    print("\n--- Testing Optimizer Creation ---")
    try:
        # Need a dummy model for optimizer
        dummy_model = nn.Linear(10, 2)
        adam_params = {"type": "adam", "learning_rate": 0.001}
        adam_optimizer = factory.create_optimizer(adam_params, dummy_model.parameters())
        print(f"Successfully created Adam optimizer: {type(adam_optimizer)}")
        assert isinstance(adam_optimizer, torch.optim.Adam)

        # Test unsupported optimizer
        print("Testing creation of unsupported optimizer...")
        try:
            factory.create_optimizer(
                {"type": "future_optimizer"}, dummy_model.parameters()
            )
        except ValueError as e:
            print(f"Correctly caught error for unsupported optimizer: {e}")
    except Exception as e:
        print(f"Error during optimizer creation: {e}")
        raise


def _test_scheduler_creation(factory: MLModelFactory):
    """Tests the scheduler creation functionality of the MLModelFactory."""
    print("\n--- Testing Scheduler Creation ---")
    try:
        # Need a dummy optimizer for scheduler
        dummy_optimizer = torch.optim.SGD(nn.Linear(5, 1).parameters(), lr=0.1)
        step_scheduler_params = {"type": "step", "step_size": 30}
        step_scheduler = factory.create_scheduler(
            step_scheduler_params, dummy_optimizer
        )
        print(f"Successfully created StepLR scheduler: {type(step_scheduler)}")
        assert isinstance(step_scheduler, torch.optim.lr_scheduler.StepLR)

        # Test scheduler with no type (should return None)
        print("Testing scheduler creation with no type specified...")
        none_scheduler = factory.create_scheduler({}, dummy_optimizer)
        print(f"Scheduler with no type: {none_scheduler}")
        assert none_scheduler is None
    except Exception as e:
        print(f"Error during scheduler creation: {e}")
        raise


def _test_model_persistence(factory: MLModelFactory, temp_persistence_dir: Path):
    """Tests the model persistence functionality of the MLModelFactory."""
    print("\n--- Testing Model Persistence ---")
    try:
        model_to_save = factory.create_model({"name": "CNN"}, num_classes=3)
        optimizer_to_save = factory.create_optimizer(
            {"type": "sgd", "learning_rate": 0.01}, model_to_save.parameters()
        )
        model_run_id = str(uuid.uuid4())
        metadata = {"epoch": 5, "accuracy": 0.95, "test_uuid": model_run_id}

        print(f"Attempting to save model with ID: {model_run_id}")
        factory.persistence.save_checkpoint(
            model_to_save, optimizer_to_save, model_run_id, metadata
        )
        print(f"Model {model_run_id} saved.")
        assert (temp_persistence_dir / model_run_id / "checkpoint.pt").exists()
        assert (temp_persistence_dir / model_run_id / "metadata.json").exists()

        # List models
        print(f"Available models in temp dir: {factory.persistence.list_models()}")

        # Load the model
        print(f"Attempting to load model: {model_run_id}")
        model_to_load = factory.create_model(
            {"name": "CNN"}, num_classes=3
        )  # Fresh instance
        optimizer_to_load = factory.create_optimizer(
            {"type": "sgd", "learning_rate": 0.01}, model_to_load.parameters()
        )

        loaded_model, loaded_optimizer, loaded_metadata = (
            factory.persistence.load_checkpoint(
                model_to_load, optimizer_to_load, model_run_id
            )
        )
        print(f"Model {model_run_id} loaded. Metadata: {loaded_metadata}")
        # Use the loaded variables to avoid unused-variable warning
        print(f"Loaded model type: {type(loaded_model)}")
        print(f"Loaded optimizer type: {type(loaded_optimizer)}")
        assert loaded_metadata["epoch"] == 5
        assert loaded_metadata["test_uuid"] == model_run_id

        # Discard the model
        print(f"Attempting to discard model: {model_run_id}")
        assert factory.persistence.discard_model(model_run_id) is True
        print(f"Model {model_run_id} discarded.")
        assert not (temp_persistence_dir / model_run_id).exists()

        # Try loading a non-existent model
        print("Testing loading of a non-existent model...")
        try:
            factory.persistence.load_checkpoint(
                model_to_load, optimizer_to_load, "non_existent_id"
            )
        except FileNotFoundError as e:
            print(f"Correctly caught error for loading non-existent model: {e}")

    except Exception as e:
        print(f"Error during persistence tests: {e}")
        raise


def run_manual_tests():
    """Runs a series of manual tests for the MLModelFactory."""
    # Create a temporary directory for persistence tests
    with tempfile.TemporaryDirectory() as tmp_dir_name:
        temp_persistence_dir = Path(tmp_dir_name)
        print(f"Using temporary persistence directory: {temp_persistence_dir}")
        print()  # Replaces f-string with newline only

        # 1. Initialize the factory
        factory = MLModelFactory(persistence_base_dir=temp_persistence_dir)
        print("MLModelFactory initialized.")

        _test_model_creation(factory)
        _test_loss_function_creation(factory)
        _test_optimizer_creation(factory)
        _test_scheduler_creation(factory)
        _test_model_persistence(factory, temp_persistence_dir)

        print("\nManual tests completed successfully.")


if __name__ == "__main__":
    print("Starting manual test for MLModelFactory...")
    run_manual_tests()
