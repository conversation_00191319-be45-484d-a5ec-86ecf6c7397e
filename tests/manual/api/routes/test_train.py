#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to directly test the train endpoint and display raw JSON response.
This avoids circular import issues by using only the requests library.
"""

import argparse
import json
import sys
import uuid

import requests


def test_train_endpoint(model_run_uuid=None, profile="development"):
    """
    Test the train endpoint with a given model run UUID and display raw JSON response.

    Args:
        model_run_uuid: UUID of the model run to use for training
        profile: Supabase profile to use
    """
    # Use a test UUID if none is provided
    if not model_run_uuid:
        model_run_uuid = str(uuid.uuid4())
        print(f"No model run UUID provided, using generated UUID: {model_run_uuid}")

    # Endpoint to test
    endpoint = "http://localhost:8000/api/v1/train"

    # Request payload
    payload = {"model_run_uuid": model_run_uuid}

    # Headers
    headers = {"Content-Type": "application/json"}

    # Add profile header if specified
    if profile:
        headers["X-Supabase-Profile"] = profile
        print(f"\nUsing profile: {profile}")

    print(f"\nRequest Headers: {json.dumps(headers, indent=2)}")
    print(f"Request Payload: {json.dumps(payload, indent=2)}")

    try:
        # Make the request
        response = requests.post(endpoint, json=payload, headers=headers, timeout=30)

        # Print response status
        print(f"Response Status: {response.status_code}")

        # Print raw JSON response
        if response.status_code == 200:
            response_json = response.json()
            print(json.dumps(response_json, indent=2))
            return True

        # Error response
        try:
            error_json = response.json()
            print(json.dumps(error_json, indent=2))
        except json.JSONDecodeError:
            print(response.text)
        return False

    except requests.exceptions.ConnectionError:
        print("Connection error: Could not connect to the API server.")
        print("Make sure the FastAPI server is running on http://localhost:8000")
        return False

    except requests.exceptions.Timeout:
        print(
            "Request timed out. The server might be overloaded or the operation takes too long."
        )
        return False

    except requests.exceptions.RequestException as e:
        print(f"Request failed: {str(e)}")
        return False


def main():
    """Main function to parse arguments and run the script."""
    parser = argparse.ArgumentParser(description="Test the train endpoint")
    parser.add_argument(
        "model_run_uuid",
        nargs="?",
        default="75d9e619-bff0-49a9-a4d5-2734c0f5d8e6",
        help="UUID of the model run to use for training",
    )
    parser.add_argument(
        "--profile",
        "-p",
        default="development",
        help="Supabase profile to use (default: development)",
    )

    args = parser.parse_args()
    success = test_train_endpoint(args.model_run_uuid, args.profile)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
