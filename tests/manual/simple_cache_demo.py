#!/usr/bin/env python3
"""
Simple Dataset Cache Invalidation Demo

This script demonstrates the core functionality of the dataset cache invalidation
mechanism using only the DatasetStateCacheService.

Usage:
    python tests/manual/simple_cache_demo.py
"""

import sys
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from uuid import uuid4

# Add src directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from database.services.dataset_state_cache import DatasetStateCacheService  # noqa: E402


def print_separator(title: str):
    """Print a formatted separator with title."""
    print(f"\n{'='*50}")
    print(f" {title}")
    print(f"{'='*50}")


def print_cache_stats():
    """Print current cache statistics."""
    stats = DatasetStateCacheService.get_cache_stats()
    print(
        f"Cache Stats: {stats['total_entries']} total, "
        f"{stats['prepared_entries']} prepared, "
        f"{stats['unprepared_entries']} unprepared"
    )


def demo_basic_operations():
    """Demonstrate basic cache operations."""
    print_separator("Basic Cache Operations")

    dataset_uuid = str(uuid4())
    timestamp = datetime.now()

    print(f"Dataset UUID: {dataset_uuid}")
    print(f"Content Updated At: {timestamp}")

    # 1. Check initial cache state (should be invalid)
    print("\n1. Initial cache check:")
    is_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)
    print(f"   Cache valid: {is_valid}")
    print_cache_stats()

    # 2. Set cache state as prepared
    print("\n2. Mark dataset as prepared:")
    cache_state = DatasetStateCacheService.set_cache_state(
        dataset_uuid=dataset_uuid,
        content_updated_at=timestamp,
        is_prepared=True,
    )
    print(f"   Cache key: {cache_state.cache_key}")
    print(f"   Is prepared: {cache_state.is_prepared}")
    print_cache_stats()

    # 3. Check cache state again (should be valid now)
    print("\n3. Cache check after preparation:")
    is_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)
    print(f"   Cache valid: {is_valid}")

    return dataset_uuid, timestamp


def demo_cache_invalidation_on_content_update():
    """Demonstrate cache invalidation when content changes."""
    print_separator("Cache Invalidation on Content Update")

    dataset_uuid = str(uuid4())
    old_timestamp = datetime.now()
    new_timestamp = old_timestamp + timedelta(hours=1)

    print(f"Dataset UUID: {dataset_uuid}")
    print(f"Old timestamp: {old_timestamp}")
    print(f"New timestamp: {new_timestamp}")

    # Set cache for old timestamp
    print("\n1. Set cache for old timestamp:")
    DatasetStateCacheService.set_cache_state(dataset_uuid, old_timestamp, True)
    print("   Cache set for old timestamp")
    print_cache_stats()

    # Check validity for both timestamps
    print("\n2. Check cache validity:")
    old_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, old_timestamp)
    new_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, new_timestamp)
    print(f"   Old timestamp valid: {old_valid}")
    print(f"   New timestamp valid: {new_valid}")

    # Set cache for new timestamp
    print("\n3. Set cache for new timestamp:")
    DatasetStateCacheService.set_cache_state(dataset_uuid, new_timestamp, True)
    print("   Cache set for new timestamp")
    print_cache_stats()

    # Check validity again
    print("\n4. Final cache validity:")
    old_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, old_timestamp)
    new_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, new_timestamp)
    print(f"   Old timestamp valid: {old_valid}")
    print(f"   New timestamp valid: {new_valid}")


def demo_manual_cache_invalidation():
    """Demonstrate manual cache invalidation."""
    print_separator("Manual Cache Invalidation")

    dataset_uuid = str(uuid4())
    timestamp1 = datetime.now()
    timestamp2 = timestamp1 + timedelta(hours=1)

    print(f"Dataset UUID: {dataset_uuid}")
    print(f"Timestamp 1: {timestamp1}")
    print(f"Timestamp 2: {timestamp2}")

    # Set up multiple cache entries
    print("\n1. Setting up multiple cache entries:")
    DatasetStateCacheService.set_cache_state(dataset_uuid, timestamp1, True)
    DatasetStateCacheService.set_cache_state(dataset_uuid, timestamp2, True)
    print("   Set cache for both timestamps")
    print_cache_stats()

    # Verify both are valid
    print("\n2. Verify cache validity:")
    valid1 = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp1)
    valid2 = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp2)
    print(f"   Timestamp 1 valid: {valid1}")
    print(f"   Timestamp 2 valid: {valid2}")

    # Invalidate all cache for this dataset
    print("\n3. Invalidate all cache for dataset:")
    invalidated_count = DatasetStateCacheService.invalidate_dataset_cache(dataset_uuid)
    print(f"   Invalidated entries: {invalidated_count}")
    print_cache_stats()

    # Verify both are now invalid
    print("\n4. Verify cache invalidation:")
    valid1 = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp1)
    valid2 = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp2)
    print(f"   Timestamp 1 valid: {valid1}")
    print(f"   Timestamp 2 valid: {valid2}")


def demo_cache_key_generation():
    """Demonstrate cache key generation."""
    print_separator("Cache Key Generation")

    dataset_uuid = str(uuid4())
    timestamp = datetime.now()

    print(f"Dataset UUID: {dataset_uuid}")
    print(f"Timestamp: {timestamp}")

    # Generate different types of cache keys
    print("\n1. Cache key variations:")
    key_with_timestamp = DatasetStateCacheService.generate_cache_key(
        dataset_uuid, timestamp
    )
    key_without_timestamp = DatasetStateCacheService.generate_cache_key(
        dataset_uuid, None
    )

    print(f"   With timestamp: {key_with_timestamp}")
    print(f"   Without timestamp: {key_without_timestamp}")

    # Demonstrate key consistency
    print("\n2. Key consistency check:")
    key1 = DatasetStateCacheService.generate_cache_key(dataset_uuid, timestamp)
    key2 = DatasetStateCacheService.generate_cache_key(dataset_uuid, timestamp)
    print(f"   Key 1: {key1}")
    print(f"   Key 2: {key2}")
    print(f"   Keys match: {key1 == key2}")


def demo_real_world_scenario():
    """Demonstrate a real-world usage scenario."""
    print_separator("Real-World Usage Scenario")

    dataset_uuid = str(uuid4())
    initial_timestamp = datetime.now()

    print(f"Dataset UUID: {dataset_uuid}")
    print(f"Initial content timestamp: {initial_timestamp}")

    # Simulate first training run
    print("\n1. First training run:")
    print("   - Check if dataset preparation needed")
    is_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, initial_timestamp)
    print(f"   - Cache valid: {is_valid}")
    print(f"   - Need to fetch dataset_sets: {not is_valid}")

    # Mark preparation complete
    print("   - Fetching dataset_sets and preparing data...")
    DatasetStateCacheService.set_cache_state(dataset_uuid, initial_timestamp, True)
    print("   - Dataset preparation marked as complete")
    print_cache_stats()

    # Simulate second training run (same dataset, no changes)
    print("\n2. Second training run (no dataset changes):")
    is_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, initial_timestamp)
    print(f"   - Cache valid: {is_valid}")
    print(f"   - Can skip dataset_sets fetching: {is_valid}")

    # Simulate dataset content update
    updated_timestamp = initial_timestamp + timedelta(hours=2)
    print(f"\n3. Dataset content updated ({updated_timestamp}):")
    is_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, updated_timestamp)
    print(f"   - Cache valid for new timestamp: {is_valid}")
    print(f"   - Need to fetch dataset_sets again: {not is_valid}")

    # Mark new version as prepared
    print("   - Fetching updated dataset_sets...")
    DatasetStateCacheService.set_cache_state(dataset_uuid, updated_timestamp, True)
    print("   - New dataset version marked as complete")
    print_cache_stats()

    # Verify both versions are cached
    print("\n4. Final state:")
    old_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, initial_timestamp)
    new_valid = DatasetStateCacheService.is_cache_valid(dataset_uuid, updated_timestamp)
    print(f"   - Old version cached: {old_valid}")
    print(f"   - New version cached: {new_valid}")
    print(
        f"   - Total cache entries: {DatasetStateCacheService.get_cache_stats()['total_entries']}"
    )


def main():
    """Run all demonstrations."""
    print("Dataset Cache Invalidation System Demo")
    print("=====================================")
    print("This demo shows how the cache system optimizes dataset preparation")
    print("by avoiding redundant database calls when dataset content hasn't changed.")

    # Clear any existing cache
    DatasetStateCacheService.clear_cache()

    try:
        demo_basic_operations()
        demo_cache_key_generation()
        demo_cache_invalidation_on_content_update()
        demo_manual_cache_invalidation()
        demo_real_world_scenario()

        print_separator("Demo Complete")
        print("Key Benefits:")
        print(
            "• Automatic cache invalidation based on dataset UUID + content timestamp"
        )
        print("• Prevents redundant dataset_sets fetching when content hasn't changed")
        print("• Manual cache invalidation when needed")
        print("• Comprehensive cache statistics and monitoring")
        print("• Thread-safe in-memory cache (can be extended to Redis/external cache)")

    finally:
        # Clean up
        cleared_count = DatasetStateCacheService.clear_cache()
        print(f"\nCleanup: Cleared {cleared_count} cache entries")


if __name__ == "__main__":
    main()
