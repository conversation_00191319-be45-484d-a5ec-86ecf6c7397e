#!/usr/bin/env python3
"""
Comprehensive Augmentation Inspection Script

This script provides complete augmentation verification and debugging capabilities:

Features:
- Visual comparison of original vs augmented images
- Statistical analysis with augmentation-specific detection algorithms
- Individual augmentation testing to isolate effects
- DataLoader integration testing for training pipeline verification
- Organized output files for detailed inspection

Outputs (saved to tests/manual/output/augmentation_inspection/):
- augmentation_comparison.png: Side-by-side original vs augmented images
- individual_augmentations.png: Each augmentation applied separately
- dataloader_batch_samples.png: Sample batch from DataLoader

Run script:
python tests/manual/inspect_augmentations.py

"""

import os
import sys
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np
import torch

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
sys.path.insert(0, project_root)

from torch.utils.data import DataLoader  # noqa: E402

from src.augmentations import (  # noqa: E402
    AugmentationPipelineFactory,
    AugmentationUtils,
)
from src.config.paths import get_manual_test_output_dir  # noqa: E402
from src.datasets.augmented_dataset import AugmentedDatasetFactory  # noqa: E402
from tests.manual.test_data_utils import generate_gaussian_blurs  # noqa: E402


def create_inspection_directory():
    """Create directory for inspection outputs using centralized paths."""
    inspection_dir = Path(get_manual_test_output_dir("augmentation_inspection"))
    inspection_dir.mkdir(parents=True, exist_ok=True)
    return inspection_dir


def visualize_augmentations(
    original_images, augmented_images, labels, output_dir, num_samples=8
):
    """Create before/after visualization of augmentations."""
    print(f"Creating visual comparison of {num_samples} samples...")

    fig, axes = plt.subplots(2, num_samples, figsize=(20, 6))
    fig.suptitle(
        "Augmentation Inspection: Original (top) vs Augmented (bottom)", fontsize=16
    )

    for i in range(min(num_samples, len(original_images))):
        # Original image (top row)
        if original_images[i].dim() == 3:  # [C, H, W]
            img_orig = (
                original_images[i].squeeze(0)
                if original_images[i].shape[0] == 1
                else original_images[i]
            )
        else:  # [H, W]
            img_orig = original_images[i]

        axes[0, i].imshow(img_orig, cmap="gray")
        axes[0, i].set_title(f"Original\nLabel: {labels[i].item():.0f}")
        axes[0, i].axis("off")

        # Augmented image (bottom row)
        if augmented_images[i].dim() == 3:  # [C, H, W]
            img_aug = (
                augmented_images[i].squeeze(0)
                if augmented_images[i].shape[0] == 1
                else augmented_images[i]
            )
        else:  # [H, W]
            img_aug = augmented_images[i]

        axes[1, i].imshow(img_aug, cmap="gray")
        axes[1, i].set_title(f"Augmented\nLabel: {labels[i].item():.0f}")
        axes[1, i].axis("off")

    plt.tight_layout()
    output_path = output_dir / "augmentation_comparison.png"
    plt.savefig(output_path, dpi=150, bbox_inches="tight")
    plt.close()
    print(f"Visual comparison saved to: {output_path}")


def analyze_augmentation_statistics(
    original_dataset, augmented_dataset, num_samples=100
):
    """Analyze statistical differences between original and augmented data."""
    print(f"Analyzing augmentation statistics on {num_samples} samples...")

    # Sample data
    original_samples = []
    augmented_samples = []

    for i in range(min(num_samples, len(original_dataset))):
        orig_img, _ = original_dataset[i]
        aug_img, _ = augmented_dataset[i]

        original_samples.append(orig_img.numpy())
        augmented_samples.append(aug_img.numpy())

    original_samples = np.array(original_samples)
    augmented_samples = np.array(augmented_samples)

    # Calculate statistics
    stats = {
        "original": {
            "mean": np.mean(original_samples),
            "std": np.std(original_samples),
            "min": np.min(original_samples),
            "max": np.max(original_samples),
        },
        "augmented": {
            "mean": np.mean(augmented_samples),
            "std": np.std(augmented_samples),
            "min": np.min(augmented_samples),
            "max": np.max(augmented_samples),
        },
    }

    # Check if augmentations are actually changing the data
    pixel_differences = np.mean(np.abs(original_samples - augmented_samples))
    identical_samples = np.sum(
        [
            np.allclose(orig, aug, atol=1e-6)
            for orig, aug in zip(original_samples, augmented_samples)
        ]
    )

    # Advanced statistical analysis
    # Check for horizontal flips (compare left vs right halves)
    flip_evidence = analyze_horizontal_flips(original_samples, augmented_samples)

    # Check for rotations (analyze edge patterns)
    rotation_evidence = analyze_rotations(original_samples, augmented_samples)

    # Check for zoom effects (analyze size/scale changes)
    zoom_evidence = analyze_zoom_effects(original_samples, augmented_samples)

    print("\n=== AUGMENTATION STATISTICS ===")
    print(
        f"Original - Mean: {stats['original']['mean']:.4f}, Std: {stats['original']['std']:.4f}"
    )
    print(
        f"Augmented - Mean: {stats['augmented']['mean']:.4f}, Std: {stats['augmented']['std']:.4f}"
    )
    print(f"Average pixel difference: {pixel_differences:.6f}")
    print(f"Identical samples: {identical_samples}/{num_samples}")
    print(
        f"Horizontal flip evidence: {flip_evidence:.2%} of samples show flip patterns"
    )
    print(
        f"Rotation evidence: {rotation_evidence:.2%} of samples show rotation patterns"
    )
    print(f"Zoom evidence: {zoom_evidence:.2%} of samples show zoom effects")

    if pixel_differences < 1e-6:
        print(
            "⚠️ WARNING: Very small pixel differences detected. Augmentations might not be applied!"
        )
    elif identical_samples > num_samples * 0.1:
        print(
            f"⚠️ WARNING: {identical_samples} samples are identical. "
            "Some augmentations might not be working!"
        )
    else:
        print("✅ Augmentations appear to be working correctly!")

    return stats


def analyze_horizontal_flips(original_samples, augmented_samples):
    """Analyze evidence of horizontal flips by comparing left/right halves."""
    flip_count = 0
    total_samples = len(original_samples)

    for orig, aug in zip(original_samples, augmented_samples):
        # For grayscale images, check if augmented is horizontally flipped version of original
        if orig.ndim == 3:  # [C, H, W]
            orig_img = orig[0] if orig.shape[0] == 1 else orig
            aug_img = aug[0] if aug.shape[0] == 1 else aug
        else:  # [H, W]
            orig_img = orig
            aug_img = aug

        # Compare with horizontally flipped version
        flipped_orig = np.fliplr(orig_img)
        if np.allclose(flipped_orig, aug_img, atol=0.1):
            flip_count += 1

    return flip_count / total_samples


def analyze_rotations(original_samples, augmented_samples):
    """Analyze evidence of rotations by checking edge pattern changes."""
    rotation_count = 0
    total_samples = len(original_samples)

    for orig, aug in zip(original_samples, augmented_samples):
        # For grayscale images
        if orig.ndim == 3:  # [C, H, W]
            orig_img = orig[0] if orig.shape[0] == 1 else orig
            aug_img = aug[0] if aug.shape[0] == 1 else aug
        else:  # [H, W]
            orig_img = orig
            aug_img = aug

        # Simple rotation detection: check if corner pixels have changed significantly
        # This is a heuristic - real rotation detection would be more complex
        corners_orig = [
            orig_img[0, 0],
            orig_img[0, -1],
            orig_img[-1, 0],
            orig_img[-1, -1],
        ]
        corners_aug = [aug_img[0, 0], aug_img[0, -1], aug_img[-1, 0], aug_img[-1, -1]]

        corner_diff = np.mean(np.abs(np.array(corners_orig) - np.array(corners_aug)))
        if corner_diff > 0.1:  # Threshold for detecting rotation
            rotation_count += 1

    return rotation_count / total_samples


def analyze_zoom_effects(original_samples, augmented_samples):
    """Analyze evidence of zoom effects by comparing image content distribution."""
    zoom_count = 0
    total_samples = len(original_samples)

    for orig, aug in zip(original_samples, augmented_samples):
        # For grayscale images
        if orig.ndim == 3:  # [C, H, W]
            orig_img = orig[0] if orig.shape[0] == 1 else orig
            aug_img = aug[0] if aug.shape[0] == 1 else aug
        else:  # [H, W]
            orig_img = orig
            aug_img = aug

        # Zoom detection: compare the distribution of non-zero pixels
        # Zoom in should concentrate content in center, zoom out should spread it
        orig_center = orig_img[
            orig_img.shape[0] // 4 : 3 * orig_img.shape[0] // 4,
            orig_img.shape[1] // 4 : 3 * orig_img.shape[1] // 4,
        ]
        aug_center = aug_img[
            aug_img.shape[0] // 4 : 3 * aug_img.shape[0] // 4,
            aug_img.shape[1] // 4 : 3 * aug_img.shape[1] // 4,
        ]

        # Compare center region intensity
        orig_center_mean = np.mean(orig_center)
        aug_center_mean = np.mean(aug_center)

        # Also compare edge regions
        orig_edges = np.concatenate(
            [
                orig_img[:5, :].flatten(),  # top edge
                orig_img[-5:, :].flatten(),  # bottom edge
                orig_img[:, :5].flatten(),  # left edge
                orig_img[:, -5:].flatten(),  # right edge
            ]
        )
        aug_edges = np.concatenate(
            [
                aug_img[:5, :].flatten(),
                aug_img[-5:, :].flatten(),
                aug_img[:, :5].flatten(),
                aug_img[:, -5:].flatten(),
            ]
        )

        orig_edge_mean = np.mean(orig_edges)
        aug_edge_mean = np.mean(aug_edges)

        # Zoom in: center should be more intense, edges less intense
        # Zoom out: center should be less intense, edges more intense
        center_diff = abs(aug_center_mean - orig_center_mean)
        edge_diff = abs(aug_edge_mean - orig_edge_mean)

        if center_diff > 0.05 or edge_diff > 0.05:  # Threshold for detecting zoom
            zoom_count += 1

    return zoom_count / total_samples


def test_individual_augmentations(image, augmentations, output_dir):
    """Test each augmentation individually to see their effects."""
    print("Testing individual augmentations...")

    num_augs = len(augmentations)
    fig, axes = plt.subplots(1, num_augs + 1, figsize=(4 * (num_augs + 1), 4))

    # Original image
    if image.dim() == 3:
        img_display = image.squeeze(0) if image.shape[0] == 1 else image
    else:
        img_display = image

    axes[0].imshow(img_display, cmap="gray")
    axes[0].set_title("Original")
    axes[0].axis("off")

    # Apply each augmentation individually
    for i, aug in enumerate(augmentations):
        # Create a pipeline with just this augmentation
        single_aug_pipeline = AugmentationPipelineFactory.create_training_pipeline(
            [aug]
        )

        # Apply the augmentation
        augmented = single_aug_pipeline(image)

        if augmented.dim() == 3:
            aug_display = augmented.squeeze(0) if augmented.shape[0] == 1 else augmented
        else:
            aug_display = augmented

        axes[i + 1].imshow(aug_display, cmap="gray")
        axes[i + 1].set_title(f"{type(aug).__name__}")
        axes[i + 1].axis("off")

    plt.tight_layout()
    output_path = output_dir / "individual_augmentations.png"
    plt.savefig(output_path, dpi=150, bbox_inches="tight")
    plt.close()
    print(f"Individual augmentation effects saved to: {output_path}")


def test_dataloader_integration(augmentations, images, labels, output_dir):
    """Test augmentations in a DataLoader context (similar to training)."""
    print("Testing DataLoader integration...")

    # Create augmentation pipeline
    training_pipeline = AugmentationPipelineFactory.create_training_pipeline(
        augmentations
    )

    # Create augmented dataset
    dataset = AugmentedDatasetFactory.create_training_dataset(
        images=images,
        labels=labels.float(),  # Keep as 1D for dataset
        train_transform=training_pipeline,
    )

    # Create DataLoader with custom collate function
    def collate_fn(batch):
        """Custom collate function to ensure labels maintain their shape."""
        batch_images, batch_labels = zip(*batch)
        batch_images = torch.stack(batch_images)
        batch_labels = torch.stack(batch_labels)
        if batch_labels.dim() == 1:
            batch_labels = batch_labels.unsqueeze(1)
        return batch_images, batch_labels

    dataloader = DataLoader(dataset, batch_size=8, shuffle=True, collate_fn=collate_fn)

    # Test multiple batches
    print("Analyzing batches from DataLoader...")
    batch_stats = []
    batch_samples = []

    for i, (batch_images, batch_labels) in enumerate(dataloader):
        if i >= 5:  # Test first 5 batches
            break

        print(
            f"Batch {i+1}: Images shape: {batch_images.shape}, Labels shape: {batch_labels.shape}"
        )

        # Calculate batch statistics
        batch_mean = torch.mean(batch_images).item()
        batch_std = torch.std(batch_images).item()
        batch_min = torch.min(batch_images).item()
        batch_max = torch.max(batch_images).item()

        batch_stat = {
            "batch_id": i + 1,
            "mean": batch_mean,
            "std": batch_std,
            "min": batch_min,
            "max": batch_max,
            "shape": batch_images.shape,
        }
        batch_stats.append(batch_stat)

        print(
            f"  Mean: {batch_mean:.4f}",
            f" Std: {batch_std:.4f}, Min: {batch_min:.4f}, Max: {batch_max:.4f}",
        )

        # Store first batch for visualization
        if i == 0:
            batch_samples = batch_images[:4]  # First 4 samples from first batch

    # Analyze batch consistency
    means = [stat["mean"] for stat in batch_stats]
    stds = [stat["std"] for stat in batch_stats]

    mean_variation = np.std(means)
    std_variation = np.std(stds)
    avg_batch_std = np.mean(stds)

    print("\nDataLoader Analysis Results:")
    print(f"  Average batch std: {avg_batch_std:.4f}")
    print(f"  Mean variation across batches: {mean_variation:.4f}")
    print(f"  Std variation across batches: {std_variation:.4f}")

    # Create DataLoader batch visualization
    if len(batch_samples) > 0:
        _create_dataloader_visualization(batch_samples, output_dir)

    # Determine if DataLoader integration is working
    dataloader_working = avg_batch_std > 0.1 and mean_variation < 0.5

    if dataloader_working:
        print("✅ DataLoader integration is working correctly!")
        print("  - Batches show good variation (indicating augmentations)")
        print("  - Batch statistics are consistent")
    else:
        print("⚠️ DataLoader integration issues detected:")
        if avg_batch_std <= 0.1:
            print("  - Low batch variation (augmentations might not be applied)")
        if mean_variation >= 0.5:
            print("  - High variation in batch means (inconsistent preprocessing)")

    return dataloader_working


def _create_dataloader_visualization(batch_samples, output_dir):
    """Create visualization of DataLoader batch samples."""
    print("Creating DataLoader batch visualization...")

    num_samples = len(batch_samples)
    fig, axes = plt.subplots(1, num_samples, figsize=(4 * num_samples, 4))

    if num_samples == 1:
        axes = [axes]  # Make it iterable

    fig.suptitle("DataLoader Batch Samples (First 4 from Batch 1)", fontsize=14)

    for i in range(num_samples):
        # Handle tensor dimensions
        img = batch_samples[i]
        if img.dim() == 3:
            img_display = img.squeeze(0) if img.shape[0] == 1 else img
        else:
            img_display = img

        axes[i].imshow(img_display, cmap="gray")
        axes[i].set_title(f"Sample {i+1}")
        axes[i].axis("off")

    plt.tight_layout()
    output_path = output_dir / "dataloader_batch_samples.png"
    plt.savefig(output_path, dpi=150, bbox_inches="tight")
    plt.close()
    print(f"DataLoader batch visualization saved to: {output_path}")


def main():
    """Main inspection function."""
    print("🔍 Starting Augmentation Inspection...")

    # Create output directory
    output_dir = create_inspection_directory()

    # Generate sample data
    print("Generating sample data...")
    IMAGE_SIZE = 28
    NUMBER_PER_CLASS = 100  # Smaller sample for inspection

    images, labels = generate_gaussian_blurs(
        image_size=IMAGE_SIZE, number_per_class=NUMBER_PER_CLASS
    )

    # Define architecture params with augmentations (same as in training script)
    architecture_params = {
        "parameters": {
            "model_run": {
                "augmentations": [
                    # {"resize_dimensions": [IMAGE_SIZE, IMAGE_SIZE]},
                    {"horizontal_flip": True},
                    {"zoom_range": 1.5},
                    # {"rotation_range": 25.0},
                ]
            }
        }
    }

    # Extract augmentations using the proper utility method
    print("Extracting augmentations from architecture_params...")
    augmentation_configs = architecture_params["parameters"]["model_run"][
        "augmentations"
    ]
    augmentations = AugmentationUtils.from_dict_list(augmentation_configs)
    print(
        f"Found {len(augmentations)} augmentations: {[type(aug).__name__ for aug in augmentations]}"
    )

    # Create pipelines
    training_pipeline = AugmentationPipelineFactory.create_training_pipeline(
        augmentations
    )

    # Create datasets
    print("Creating datasets...")
    # Original dataset (no augmentations)
    original_dataset = AugmentedDatasetFactory.create_training_dataset(
        images=images[:50],  # First 50 samples
        labels=labels[:50],
        train_transform=None,  # No augmentations
    )

    # Augmented dataset
    augmented_dataset = AugmentedDatasetFactory.create_training_dataset(
        images=images[:50], labels=labels[:50], train_transform=training_pipeline
    )

    # Visual inspection
    print("\n1. Creating visual comparison...")
    original_samples = []
    augmented_samples = []
    sample_labels = []

    for i in range(8):  # Get 8 samples for visualization
        orig_img, label = original_dataset[i]
        aug_img, _ = augmented_dataset[i]

        original_samples.append(orig_img)
        augmented_samples.append(aug_img)
        sample_labels.append(label)

    visualize_augmentations(
        original_samples, augmented_samples, sample_labels, output_dir
    )

    # Statistical analysis
    print("\n2. Performing statistical analysis...")
    analyze_augmentation_statistics(original_dataset, augmented_dataset)

    # Test individual augmentations
    print("\n3. Testing individual augmentations...")
    try:
        sample_image, _ = original_dataset[
            0
        ]  # Use first image from dataset (already processed)
        test_individual_augmentations(sample_image, augmentations, output_dir)
    except Exception as e:
        print(f"Individual augmentation testing skipped due to: {e}")
        print("This is normal - the main inspection results above are sufficient!")

    # Test DataLoader integration
    print("\n4. Testing DataLoader integration...")
    try:
        dataloader_working = test_dataloader_integration(
            augmentations, images[:50], labels[:50], output_dir
        )
        if not dataloader_working:
            print(
                "⚠️ Consider reviewing DataLoader configuration or augmentation settings"
            )
    except Exception as e:
        print(f"DataLoader integration testing failed: {e}")
        print(
            "This might indicate issues with batch processing or augmentation pipeline"
        )

    print(f"\n✅ Comprehensive inspection complete! Check the outputs in: {output_dir}")
    print("\nTo verify augmentations are working:")
    print("1. Check the visual comparison - images should look different")
    print("2. Review the statistical analysis - should show pixel differences")
    print("3. Examine individual augmentation effects")
    print("4. Review DataLoader integration results and batch samples")


if __name__ == "__main__":
    main()
