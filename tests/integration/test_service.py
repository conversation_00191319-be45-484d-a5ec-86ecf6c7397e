"""
Integration tests for database services using the generic model factory.
"""

import asyncio
import unittest
from unittest.mock import patch
from uuid import uuid4

from src.database.models.dataset import Dataset
from src.database.services.dataset_service import DatasetError, DatasetService
from src.database.services.model_metadata_service import (
    ModelMetadataError,
    ModelMetadataService,
)
from src.database.services.training_data_service import TrainingDataService

# Import centralized test data
from tests.fixtures.data import (
    get_sample_dataset_data,
    get_sample_model_data,
    get_sample_model_run_data,
    get_sample_model_version_data,
)


# pylint: disable=too-many-instance-attributes
class ServiceIntegrationTests(unittest.TestCase):
    """Integration tests for database services."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock data for testing
        self.dataset_uuid = str(uuid4())
        self.model_uuid = str(uuid4())
        self.model_run_uuid = str(uuid4())
        self.model_version_uuid = str(uuid4())

        # Sample dataset data using the centralized function
        self.dataset_data = get_sample_dataset_data(
            custom_values={
                "uuid": self.dataset_uuid,
                "name": "Test Dataset",
                "images_count": 100,  # Model expects images_count
                "description": "A test dataset",
                "created_at": "2023-01-01T00:00:00Z",
                "content_updated_at": "2023-01-02T00:00:00Z",
            }
        )

        # Sample model data using the centralized function
        self.model_data = get_sample_model_data(
            custom_values={
                "uuid": self.model_uuid,
                "name": "Test Model",
                "description": "A test model",
            }
        )

        # Sample model version data using the centralized function
        self.model_version_data = get_sample_model_version_data(
            custom_values={
                "uuid": self.model_version_uuid,
                "model_uuid": self.model_uuid,
                "version_number": 1,
                "parameters": {"learning_rate": 0.001, "batch_size": 32},
            }
        )

        # Sample model run data using the centralized function
        self.model_run_data = get_sample_model_run_data(
            custom_values={
                "uuid": self.model_run_uuid,
                "model_version_uuid": self.model_version_uuid,
                "dataset_uuid": self.dataset_uuid,
                "schedule_time": "2023-01-02T00:00:00Z",
                "parameters": {"epochs": 10},
                "created_at": "2023-01-01T00:00:00Z",
            }
        )

        # Sample dataset set data
        self.dataset_set_data = {
            "uuid": str(uuid4()),
            "dataset_uuid": self.dataset_uuid,
            "set_type": "training",
            "created_at": "2023-01-01T00:00:00Z",
        }

    @patch("src.database.services.dataset_service.fetch_data")
    def test_dataset_service(self, mock_fetch_data):
        """Test DatasetService with the generic model factory."""
        # Mock the fetch_data function to return our test data
        mock_fetch_data.return_value = [self.dataset_data]

        # Test get_dataset - note this is not an async method
        # The service returns a dict, so we need to convert it to a model
        dataset_dict = DatasetService.get_dataset(self.dataset_uuid)

        # Create a model from the dict
        dataset = Dataset(**dataset_dict)

        # Verify that we got a proper dataset object
        self.assertEqual(str(dataset.uuid), str(self.dataset_uuid))
        self.assertEqual(dataset.name, "Test Dataset")
        self.assertEqual(dataset.images_count, 100)

        # Test with invalid data
        invalid_data = {
            "uuid": str(uuid4()),
            "name": "Invalid Dataset",
            # Missing required 'images_count' field
        }
        mock_fetch_data.return_value = [invalid_data]

        # Should raise DatasetError
        with self.assertRaises(DatasetError):
            DatasetService.get_dataset(str(uuid4()))

    @patch("src.database.services.model_metadata_service.fetch_data")
    def test_model_metadata_service(self, mock_fetch_data):
        """Test ModelMetadataService with the generic model factory."""
        # Mock the fetch_data function to return our test data
        mock_fetch_data.return_value = [self.model_data]

        # Test get_model
        model = asyncio.run(ModelMetadataService.get_model(self.model_uuid))

        # Verify that we got a proper model object
        self.assertEqual(model.uuid, self.model_uuid)
        self.assertEqual(model.name, "Test Model")
        self.assertEqual(model.architecture, "ResNet50")

        # Test with invalid data
        invalid_data = {
            "uuid": str(uuid4()),
            "name": "Invalid Model",
            # Missing required 'architecture' field
        }
        mock_fetch_data.return_value = [invalid_data]

        # Should raise ModelMetadataError
        with self.assertRaises(ModelMetadataError):
            asyncio.run(ModelMetadataService.get_model(str(uuid4())))

    @patch("src.database.services.model_metadata_service.fetch_data")
    @patch("src.database.services.dataset_service.fetch_data")
    def test_training_data_service(self, mock_dataset_fetch, mock_model_fetch):
        """Test TrainingDataService with the generic model factory."""

        # Mock the fetch_data functions
        # For model metadata
        # pylint: disable=unused-argument
        def mock_model_metadata_fetch(table, params, profile=None):
            if table == "model_runs":
                return [
                    {
                        **self.model_run_data,
                        "model_versions": [self.model_version_data],
                        "models": [self.model_data],
                    }
                ]
            return []

        mock_model_fetch.side_effect = mock_model_metadata_fetch

        # For dataset
        mock_dataset_fetch.return_value = [self.dataset_data]

        # Test get_training_data
        with patch(
            "src.database.services.training_data_service.ModelMetadataService.get_model_metadata"
        ) as mock_get_metadata:
            # Mock the get_model_metadata method to return our test data
            # pylint: disable=unused-argument
            async def mock_get_metadata_async(*args, **kwargs):
                # Structure the data to match what the method expects
                # model_versions is inside model_run and models is inside model_version
                model_run = dict(self.model_run_data)
                model_version = dict(self.model_version_data)
                model = dict(self.model_data)

                # Create the nested structure
                model_version["models"] = [model]
                model_run["model_versions"] = [model_version]

                return {
                    "model": model,
                    "model_version": model_version,
                    "model_run": model_run,
                }

            mock_get_metadata.side_effect = mock_get_metadata_async

            # We need to patch the TrainingDataService to fix the await issue
            with patch(
                "src.database.services.training_data_service.DatasetService.get_dataset"
            ) as mock_get_dataset:
                # Need async coroutine for awaited method
                # pylint: disable=unused-argument
                async def mock_get_dataset_async(*args, **kwargs):
                    return self.dataset_data

                mock_get_dataset.side_effect = mock_get_dataset_async

                # Get training data
                training_data = asyncio.run(
                    TrainingDataService.get_training_data(self.model_run_uuid)
                )

            # Verify that we got a proper training data dictionary
            self.assertEqual(training_data["model"]["uuid"], self.model_uuid)
            self.assertEqual(training_data["dataset"]["uuid"], self.dataset_uuid)
            self.assertEqual(training_data["model_run"]["uuid"], self.model_run_uuid)

            # Verify that the training parameters are merged correctly
            self.assertEqual(
                training_data["training_parameters"]["learning_rate"], 0.001
            )
            self.assertEqual(training_data["training_parameters"]["batch_size"], 32)
            self.assertEqual(training_data["training_parameters"]["epochs"], 10)


if __name__ == "__main__":
    unittest.main()
