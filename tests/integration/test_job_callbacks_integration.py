"""
Integration tests for job callbacks.

These tests focus on the integration between job callbacks and the job dispatcher system,
ensuring that callbacks are properly triggered during job lifecycle events.
"""

import asyncio
import logging
from datetime import datetime
from unittest.mock import MagicMock, patch

import psutil
import pytest

from src.database.services.model_run_service import (
    ModelRunCompleteUpdate,
    ModelRunMetricsUpdate,
    ModelRunTimingUpdate,
)

# Import fixtures to make them available for pytest discovery
pytest_plugins = ["tests.fixtures.job_callbacks"]

logger = logging.getLogger(__name__)


# --- Tests for TestJobCallbacksIntegration ---


class TestJobCallbacksIntegration:
    """Test that callbacks are triggered correctly during job lifecycle."""

    @pytest.mark.asyncio
    async def test_dispatcher_lifecycle_callbacks(  # pylint: disable=redefined-outer-name
        self, dispatcher, database_callback_mock, resource_callback_mock
    ):
        """Test that callbacks are triggered for dispatcher start and stop."""
        await dispatcher.start()
        database_callback_mock.on_dispatcher_start.assert_called_once()
        resource_callback_mock.on_dispatcher_start.assert_called_once()

        await dispatcher.stop()
        database_callback_mock.on_dispatcher_stop.assert_called_once()
        resource_callback_mock.on_dispatcher_stop.assert_called_once()

    @pytest.mark.asyncio
    async def test_job_lifecycle_callbacks(  # pylint: disable=redefined-outer-name
        self, dispatcher, job, database_callback_mock, resource_callback_mock
    ):
        """Test that callbacks are triggered during job lifecycle events."""
        await dispatcher.start()

        await dispatcher.schedule_job(job)
        database_callback_mock.on_job_scheduled.assert_called_with(job)
        resource_callback_mock.on_job_scheduled.assert_called_with(job)

        # pylint: disable=protected-access
        dispatcher._notify_callbacks("on_job_start", job, None)
        database_callback_mock.on_job_start.assert_called_with(job, None)
        resource_callback_mock.on_job_start.assert_called_with(job, None)

        result = {"acc": 0.9}
        # pylint: disable=protected-access
        dispatcher._notify_callbacks("on_job_complete", job, result)
        database_callback_mock.on_job_complete.assert_called_with(job, result)
        resource_callback_mock.on_job_complete.assert_called_with(job, result)

    @pytest.mark.asyncio
    async def test_job_failure_callbacks(  # pylint: disable=redefined-outer-name
        self, dispatcher, job, database_callback_mock, resource_callback_mock
    ):
        """Test that callbacks are triggered when a job fails."""
        await dispatcher.start()
        await dispatcher.schedule_job(job)

        error = ValueError("Job failed")
        # pylint: disable=protected-access
        dispatcher._notify_callbacks("on_job_failed", job, error, None)
        database_callback_mock.on_job_failed.assert_called_with(job, error, None)
        resource_callback_mock.on_job_failed.assert_called_with(job, error, None)

    @pytest.mark.asyncio
    async def test_job_cancellation_callbacks(  # pylint: disable=redefined-outer-name
        self, dispatcher, job, database_callback_mock, resource_callback_mock
    ):
        """Test that callbacks are triggered when a job is cancelled."""
        await dispatcher.start()
        await dispatcher.schedule_job(job)

        await dispatcher.cancel_job(job.job_id)
        database_callback_mock.on_job_cancelled.assert_called_with(job)
        resource_callback_mock.on_job_cancelled.assert_called_with(job)


# --- Tests for TestDatabaseCallbackIntegration ---


class TestDatabaseCallbackIntegration:
    """Integration tests for the DatabaseUpdateCallback."""

    async def test_database_update_on_job_start(  # pylint: disable=redefined-outer-name
        self, database_update_callback, db_training_job, mock_model_run_service
    ):
        """Test that the database is updated when a job starts."""
        mock_service, mock_constructor = mock_model_run_service
        database_update_callback.on_job_start(db_training_job)
        await asyncio.sleep(0)  # Allow the created task to be scheduled

        mock_constructor.assert_called_once()

        # Check call to update_model_run_times
        args_timing, _ = mock_service.update_model_run_times.call_args
        assert mock_service.update_model_run_times.call_count == 1
        assert isinstance(args_timing[0], ModelRunTimingUpdate)
        assert args_timing[0].model_run_uuid == db_training_job.data.model_run_uuid
        assert isinstance(args_timing[0].start_time, datetime)

        # Check call to update_model_run_metrics
        args_metrics, _ = mock_service.update_model_run_metrics.call_args
        assert mock_service.update_model_run_metrics.call_count == 1
        assert isinstance(args_metrics[0], ModelRunMetricsUpdate)
        assert args_metrics[0].model_run_uuid == db_training_job.data.model_run_uuid
        assert args_metrics[0].metrics == {"job_id": db_training_job.job_id}

    async def test_database_update_on_job_complete(  # pylint: disable=redefined-outer-name
        self, database_update_callback, db_training_job, mock_model_run_service
    ):
        """Test that the database is updated when a job completes."""
        mock_service, _ = mock_model_run_service
        result = {"accuracy": 0.95}
        database_update_callback.on_job_complete(db_training_job, logs=result)
        await asyncio.sleep(0)  # Allow the created task to be scheduled

        # Check call to update_model_run_complete
        args_complete, _ = mock_service.update_model_run_complete.call_args
        assert mock_service.update_model_run_complete.call_count == 1
        assert isinstance(args_complete[0], ModelRunCompleteUpdate)
        assert args_complete[0].model_run_uuid == db_training_job.data.model_run_uuid
        assert isinstance(args_complete[0].end_time, datetime)
        assert args_complete[0].metrics == result

    async def test_database_update_on_job_failed(  # pylint: disable=redefined-outer-name
        self, database_update_callback, db_training_job, mock_model_run_service
    ):
        """Test that the database is updated when a job fails."""
        mock_service, _ = mock_model_run_service
        error = RuntimeError("Training failed")
        database_update_callback.on_job_failed(db_training_job, error=error)
        await asyncio.sleep(0)  # Allow the created task to be scheduled

        # Check call to update_model_run_complete
        args_complete, _ = mock_service.update_model_run_complete.call_args
        assert mock_service.update_model_run_complete.call_count == 1
        assert isinstance(args_complete[0], ModelRunCompleteUpdate)
        assert args_complete[0].model_run_uuid == db_training_job.data.model_run_uuid
        assert isinstance(args_complete[0].end_time, datetime)
        assert args_complete[0].metrics == {
            "error": str(error),
            "error_type": type(error).__name__,
        }


# --- Tests for TestResourceMonitorCallbackIntegration ---


@pytest.mark.integration
class TestResourceMonitorCallbackIntegration:
    """Integration tests for the ResourceMonitorCallback."""

    # pylint: disable=too-few-public-methods
    # This class currently serves as a placeholder for these future tests.
    # If tests are not added, consider removing or fleshing out this class.

    @pytest.mark.asyncio
    async def test_resource_monitoring_lifecycle(  # pylint: disable=redefined-outer-name
        self, resource_monitor_callback_with_mocks, job
    ):
        """Test the complete lifecycle of resource monitoring."""
        callback_fixture = resource_monitor_callback_with_mocks
        current_job = job

        callback_fixture.on_job_start(current_job)
        # pylint: disable=protected-access
        assert len(callback_fixture._monitoring_tasks) == 1
        assert current_job.job_id in callback_fixture._monitoring_tasks

        await asyncio.sleep(0.3)

        # Check metrics in the Metrics instance
        # pylint: disable=protected-access
        job_metrics = callback_fixture._job_metrics.get(current_job.job_id)
        assert job_metrics is not None
        assert len(job_metrics.storage.resources["cpu_percent"]) >= 2
        assert len(job_metrics.storage.resources["memory_percent"]) >= 2
        assert len(job_metrics.storage.resources["gpu_memory_used_gb"]) >= 2

        # Verify metrics values
        assert job_metrics.storage.resources["cpu_percent"][0] == 50.0
        assert job_metrics.storage.resources["memory_percent"][0] == 60.0
        assert (
            job_metrics.storage.resources["gpu_memory_used_gb"][0] == 1.0
        )  # 1GB from fixture

        callback_fixture.on_job_complete(current_job)
        # pylint: disable=protected-access
        assert len(callback_fixture._monitoring_tasks) == 0

    @pytest.mark.asyncio
    async def test_resource_monitoring_on_job_cancelled(  # pylint: disable=redefined-outer-name
        self, resource_monitor_callback_with_mocks, job
    ):
        """Test that resource monitoring stops when a job is cancelled."""
        callback_fixture = resource_monitor_callback_with_mocks
        current_job = job

        callback_fixture.on_job_start(current_job)

        # pylint: disable=protected-access
        assert current_job.job_id in callback_fixture._monitoring_tasks
        task = callback_fixture._monitoring_tasks[current_job.job_id]
        assert not task.done()

        callback_fixture.on_job_cancelled(current_job)
        await asyncio.sleep(0.1)

        # pylint: disable=protected-access
        assert current_job.job_id not in callback_fixture._monitoring_tasks
        assert task.cancelled()

    @pytest.mark.asyncio
    async def test_monitoring_handles_psutil_error(  # pylint: disable=redefined-outer-name
        self, resource_monitor_callback_with_mocks, job
    ):
        """Test that monitoring continues gracefully if a psutil call fails."""
        callback_fixture = resource_monitor_callback_with_mocks
        current_job = job

        mock_cpu_percent = MagicMock(
            side_effect=[50.0, psutil.Error("Test Error"), 60.0, 60.0, 60.0]
        )

        with patch(
            "src.jobs.callbacks.resource_monitor.psutil.cpu_percent", mock_cpu_percent
        ), patch(
            "src.jobs.callbacks.resource_monitor.logger.error"
        ) as mock_logger_error:
            callback_fixture.on_job_start(current_job)
            await asyncio.sleep(0.35)
            callback_fixture.on_job_complete(current_job)

            mock_logger_error.assert_called()
            last_call_args = mock_logger_error.call_args_list[-1].args
            assert last_call_args[0].startswith(
                "Error collecting resource metrics for job %s"
            )
            assert last_call_args[1] == current_job.job_id
            assert isinstance(last_call_args[2], psutil.Error)

            # Verify metrics were collected despite the error
            # pylint: disable=protected-access
            assert current_job.job_id not in callback_fixture._monitoring_tasks

    @pytest.mark.asyncio
    async def test_concurrent_job_monitoring(  # pylint: disable=redefined-outer-name
        self, resource_monitor_callback_with_mocks, job_factory
    ):
        """Test that the callback can monitor multiple jobs concurrently."""
        callback_fixture = resource_monitor_callback_with_mocks
        job_creator = job_factory
        job1 = job_creator(job_id="job-1")
        job2 = job_creator(job_id="job-2")

        callback_fixture.on_job_start(job1)
        callback_fixture.on_job_start(job2)

        # pylint: disable=protected-access
        assert len(callback_fixture._monitoring_tasks) == 2
        assert "job-1" in callback_fixture._monitoring_tasks
        assert "job-2" in callback_fixture._monitoring_tasks

        await asyncio.sleep(0.15)
        callback_fixture.on_job_complete(job1)

        # pylint: disable=protected-access
        assert len(callback_fixture._monitoring_tasks) == 1
        assert "job-2" in callback_fixture._monitoring_tasks
        assert "job-1" not in callback_fixture._monitoring_tasks

        await asyncio.sleep(0.15)
        callback_fixture.on_job_complete(job2)

        # pylint: disable=protected-access
        assert not callback_fixture._monitoring_tasks

    @pytest.mark.asyncio
    async def test_data_aggregation_accuracy(  # pylint: disable=redefined-outer-name
        self, resource_monitor_callback_with_mocks, job
    ):
        """Test that summary metrics are aggregated accurately."""
        callback_fixture = resource_monitor_callback_with_mocks
        current_job = job

        cpu_values = [15.0, 85.5, 45.0]
        mem_values = [40.0, 60.0, 95.8]
        gpu_mem_values = [1.2, 3.5, 2.1]

        mock_cpu_side_effect = cpu_values + [cpu_values[-1]]
        mock_mem_side_effect = [MagicMock(percent=p) for p in mem_values] + [
            MagicMock(percent=mem_values[-1])
        ]
        mock_gpu_allocated_side_effect = [g * (1024**3) for g in gpu_mem_values] + [
            gpu_mem_values[-1] * (1024**3)
        ]

        with patch(
            "src.jobs.callbacks.resource_monitor.psutil.cpu_percent",
            side_effect=mock_cpu_side_effect,
        ), patch(
            "src.jobs.callbacks.resource_monitor.psutil.virtual_memory",
            side_effect=mock_mem_side_effect,
        ), patch(
            "src.jobs.callbacks.resource_monitor.torch.cuda.is_available",
            return_value=True,
        ), patch(
            "src.jobs.callbacks.resource_monitor.torch.cuda.memory_allocated",
            side_effect=mock_gpu_allocated_side_effect,
        ), patch(
            "src.jobs.callbacks.resource_monitor.torch.cuda.memory_reserved",
            return_value=4 * (1024**3),
        ):
            callback_fixture.on_job_start(current_job)
            await asyncio.sleep(0.35)

            # Check metrics in the Metrics instance before cleanup
            # pylint: disable=protected-access
            job_metrics = callback_fixture._job_metrics.get(current_job.job_id)
            assert job_metrics is not None

            # Verify aggregation accuracy using MetricsDict methods
            assert job_metrics.storage.resources.get_max("cpu_percent") == max(
                cpu_values
            )
            assert job_metrics.storage.resources.get_max("memory_percent") == max(
                mem_values
            )
            assert job_metrics.storage.resources.get_max(
                "gpu_memory_used_gb"
            ) == pytest.approx(max(gpu_mem_values))

            # Verify we collected the expected number of metrics
            metrics_count = len(job_metrics.storage.resources["cpu_percent"])
            assert 3 <= metrics_count <= 4

            callback_fixture.on_job_complete(current_job)

    @pytest.mark.asyncio
    async def test_monitoring_in_no_gpu_environment(  # pylint: disable=redefined-outer-name
        self, resource_monitor_callback_with_mocks, job
    ):
        """Test that monitoring works correctly when no GPU is available."""
        callback_fixture = resource_monitor_callback_with_mocks
        current_job = job

        with patch(
            "src.jobs.callbacks.resource_monitor.torch.cuda.is_available",
            return_value=False,
        ):
            callback_fixture.on_job_start(current_job)
            await asyncio.sleep(0.15)

            # Check metrics in the Metrics instance before cleanup
            # pylint: disable=protected-access
            job_metrics = callback_fixture._job_metrics.get(current_job.job_id)
            assert job_metrics is not None
            assert len(job_metrics.storage.resources["cpu_percent"]) >= 1

            # Verify GPU metrics are not collected when CUDA is not available
            assert len(job_metrics.storage.resources.get("gpu_memory_used_gb", [])) == 0
            assert len(job_metrics.storage.resources.get("gpu_memory_percent", [])) == 0

            callback_fixture.on_job_complete(current_job)
