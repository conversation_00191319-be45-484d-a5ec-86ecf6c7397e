"""
Integration tests for augmentation module with training pipeline.

This module tests the complete integration of the augmentations module
with the training pipeline, including data loading, training, and artifact saving.
"""

import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import torch
from torch import nn, optim
from torch.utils.data import DataLoader

from augmentations import (
    AugmentationPipelineFactory,
    HorizontalFlip,
    ResizeDimensions,
    RotationRange,
)
from datasets import AugmentedDataset
from src.config.paths import get_run_paths
from src.jobs.training_data import TrainingJob, TrainingJobData
from src.jobs.training_dispatcher import DispatcherConfig, TrainingJobDispatcher
from src.train.trainer import ModelTrainer, TrainerConfig
from tests.manual.test_data_utils import augmented_data_loader, generate_gaussian_blurs


class SimpleTestModel(nn.Module):
    """Simple model for testing augmentation integration."""

    def __init__(self):
        super().__init__()
        self.conv1 = nn.Conv2d(1, 16, 3, padding=1)  # 1-channel input for grayscale
        self.conv2 = nn.Conv2d(16, 32, 3, padding=1)
        self.pool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(32, 1)

    def forward(self, x):
        """Forward pass of the model."""

        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = self.pool(x)
        x = x.view(x.size(0), -1)
        return torch.sigmoid(self.fc(x))


@pytest.fixture(name="sample_augmentations")
def fixture_sample_augmentations():
    """Sample augmentations for testing."""
    return [
        ResizeDimensions(resize_dimensions=[64, 64]),
        HorizontalFlip(horizontal_flip=True),
        RotationRange(rotation_range=10.0),
        # Note: Normalization removed to avoid channel mismatch with grayscale data
    ]


@pytest.fixture(name="sample_training_data")
def fixture_sample_training_data(sample_augmentations):
    """Sample training data with augmentations."""
    return {
        "model": {"uuid": "test-model", "name": "Test Model"},
        "model_version": {"uuid": "test-version", "version": "1.0.0"},
        "dataset": {
            "uuid": "test-dataset",
            "name": "Test Dataset",
            "images_count": 100,
        },
        "model_run": {
            "uuid": "test-run",
            "augmentations": [aug.model_dump() for aug in sample_augmentations],
        },
        "training_parameters": {"batch_size": 16, "epochs": 2},
        "dataset_metadata": {"images_count": 100},
    }


class TestAugmentationTrainingIntegration:
    """Test augmentation integration with training pipeline."""

    def test_augmentation_pipeline_creation(self, sample_augmentations):
        """Test that augmentation pipelines are created correctly."""
        # Create training pipeline
        train_pipeline = AugmentationPipelineFactory.create_training_pipeline(
            sample_augmentations
        )

        # Create inference pipeline
        inference_pipeline = AugmentationPipelineFactory.create_inference_pipeline(
            sample_augmentations
        )

        # Verify pipelines are created
        assert train_pipeline is not None
        assert inference_pipeline is not None

        # Training pipeline should have more transforms (includes random augmentations)
        assert len(train_pipeline.transforms) >= len(inference_pipeline.transforms)

        # Both should include ValuePreservingToTensor (no normalization in this test)
        transform_types = [type(t).__name__ for t in train_pipeline.transforms]
        assert "ValuePreservingToTensor" in transform_types

    def test_augmented_dataset_creation(self, sample_augmentations):
        """Test that AugmentedDataset works with augmentation pipelines."""
        # Create mock data
        images = torch.randn(10, 1, 32, 32)  # 1-channel grayscale
        labels = torch.randint(0, 2, (10,))

        # Create augmentation pipeline
        train_pipeline = AugmentationPipelineFactory.create_training_pipeline(
            sample_augmentations
        )

        # Create augmented dataset
        dataset = AugmentedDataset(images, labels, train_pipeline)

        # Test dataset properties
        assert len(dataset) == 10
        assert dataset.transform is not None

        # Test getting a sample
        sample_image, sample_label = dataset[0]
        assert isinstance(sample_image, torch.Tensor)
        assert isinstance(sample_label, torch.Tensor)

        # Image should be resized to 64x64 as specified in augmentations
        assert sample_image.shape[-2:] == (64, 64)

    def test_augmented_data_loader_with_augmentations(self, sample_augmentations):
        """Test that augmented_data_loader works with augmentation pipelines."""

        # Generate mock data
        images, labels = generate_gaussian_blurs(image_size=32, number_per_class=20)

        # Create augmentation pipelines
        train_pipeline = AugmentationPipelineFactory.create_training_pipeline(
            sample_augmentations
        )
        val_pipeline = AugmentationPipelineFactory.create_inference_pipeline(
            sample_augmentations
        )

        # Create augmented data loaders
        train_loader, test_loader = augmented_data_loader(
            images,
            labels,
            transforms={"train": train_pipeline, "val": val_pipeline},
            config={"batch_size": 8, "test_size": 0.2},
        )

        # Verify data loaders are created
        assert isinstance(train_loader, DataLoader)
        assert isinstance(test_loader, DataLoader)

        # Test getting a batch
        train_batch = next(iter(train_loader))
        batch_images, _ = train_batch

        # Images should be resized to 64x64 and normalized
        assert batch_images.shape[-2:] == (64, 64)
        assert batch_images.dtype == torch.float32

    @pytest.mark.asyncio
    async def test_training_dispatcher_augmentation_integration(
        self, sample_training_data
    ):
        """Test TrainingJobDispatcher with augmentation support."""
        # Generate test data
        images, labels = generate_gaussian_blurs(image_size=32, number_per_class=20)
        data_loaders = augmented_data_loader(
            images,
            labels,
            transforms={"train": None, "val": None},
            config={"batch_size": 8, "test_size": 0.2},
        )

        # Setup comprehensive mocking
        with self._setup_service_mocks(data_loaders, sample_training_data):
            # Create dispatcher and job
            dispatcher, job = self._create_dispatcher_and_job()

            try:
                # Test augmented data loader preparation
                # pylint: disable-next=protected-access
                result = await dispatcher._prepare_augmented_data_loaders(
                    sample_training_data, job
                )

                # Verify augmentation metadata is stored
                self._verify_augmentation_metadata(result)
            finally:
                # Ensure dispatcher is properly stopped
                await dispatcher.stop()

    def _setup_service_mocks(self, data_loaders, sample_training_data):
        """Setup all service mocks for the integration test."""
        train_loader, test_loader = data_loaders

        return patch.multiple(
            "src.jobs.training_dispatcher",
            TrainingDataService=MagicMock(
                get_training_data=AsyncMock(return_value=sample_training_data)
            ),
            DataLoadingService=MagicMock(
                create_data_loaders=AsyncMock(
                    return_value={"train": train_loader, "test": test_loader}
                )
            ),
            DatasetService=MagicMock(
                download_dataset_images=AsyncMock(return_value=(0, []))
            ),
            DatasetStateCacheService=MagicMock(
                is_cache_valid=MagicMock(return_value=True)
            ),
            ModelRunService=MagicMock(update_model_run_times=AsyncMock()),
        )

    def _create_dispatcher_and_job(self):
        """Create dispatcher and job for the integration test."""
        dispatcher_config = DispatcherConfig(auto_start=False)
        dispatcher = TrainingJobDispatcher(config=dispatcher_config)

        job_data = TrainingJobData(
            model_run_uuid="123e4567-e89b-12d3-a456-************",
            profile="test",
            model_components={},
            data_loaders={},
            training_config={},
        )
        job = TrainingJob(job_id="test-job", data=job_data)

        return dispatcher, job

    def _verify_augmentation_metadata(self, data_loaders):
        """Verify that augmentation metadata is properly stored."""
        assert "_augmentation_metadata" in data_loaders
        metadata = data_loaders["_augmentation_metadata"]

        assert "augmentations" in metadata
        assert "train_transforms" in metadata
        assert "val_transforms" in metadata
        assert metadata["augmentation_count"] == 3

    def test_model_trainer_augmentation_artifacts(self, sample_augmentations):
        """Test that ModelTrainer saves augmentation artifacts."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create mock data loaders with augmentation metadata
            mock_train_loader = MagicMock(spec=DataLoader)
            mock_test_loader = MagicMock(spec=DataLoader)

            # Create augmentation pipelines
            train_transforms = AugmentationPipelineFactory.create_training_pipeline(
                sample_augmentations
            )
            val_transforms = AugmentationPipelineFactory.create_inference_pipeline(
                sample_augmentations
            )

            data_loaders = {
                "train": mock_train_loader,
                "test": mock_test_loader,
                "_augmentation_metadata": {
                    "augmentations": sample_augmentations,
                    "train_transforms": train_transforms,
                    "val_transforms": val_transforms,
                    "augmentation_count": len(sample_augmentations),
                },
            }

            # Create trainer config
            model = SimpleTestModel()
            trainer_config = TrainerConfig(
                model_components={
                    "model": model,
                    "loss_fn": nn.BCELoss(),
                    "optimizer": optim.Adam(model.parameters()),
                },
                data_loaders=data_loaders,
                training_config={
                    "epochs": 1,
                    "model_run_uuid": "test-augmentation-artifacts-uuid",
                    "run_output_dir": temp_dir,
                },
            )

            # Create trainer
            trainer = ModelTrainer(trainer_config)

            # Test saving augmentation artifacts using the integrated artifact manager
            # pylint: disable=protected-access
            trainer.artifact_manager._save_augmentation_artifacts()

            # Verify augmentation artifacts are saved using centralized paths
            run_paths = get_run_paths("test-augmentation-artifacts-uuid")
            augmentations_dir = Path(run_paths.augmentations)

            # Check for the new artifact structure (per-split configs)
            assert (augmentations_dir / "train_augmentation_config.json").exists()
            assert (augmentations_dir / "test_augmentation_config.json").exists()
            assert (augmentations_dir / "augmentation_summary.json").exists()

    def test_simple_data_loader_validation(self):
        """Test that augmented_data_loader works with no augmentations."""

        # Generate mock data
        images, labels = generate_gaussian_blurs(image_size=32, number_per_class=20)

        # Create simple data loaders
        train_loader, test_loader = augmented_data_loader(
            images,
            labels,
            transforms={"train": None, "val": None},  # No transforms for simplicity
            config={"batch_size": 8, "test_size": 0.2},
        )

        # Basic validation
        assert isinstance(train_loader, DataLoader)
        assert isinstance(test_loader, DataLoader)
        assert len(train_loader.dataset) > 0
        assert len(test_loader.dataset) > 0
        assert train_loader.batch_size == 8
        assert test_loader.batch_size == 8

    def test_augmented_dataset_error_handling(self):
        """Test AugmentedDataset error handling."""
        # Test with mismatched image and label lengths
        images = torch.randn(10, 1, 32, 32)  # 1-channel grayscale
        labels = torch.randint(0, 2, (5,))  # Wrong length

        with pytest.raises(
            ValueError, match="Images and labels must have the same length"
        ):
            AugmentedDataset(images, labels)

        # Test with invalid label shape
        labels = torch.randint(0, 2, (10, 2, 3))  # Invalid shape

        with pytest.raises(ValueError, match="Labels must be 1D or 2D"):
            AugmentedDataset(images, labels)
