"""
Integration test fixtures and configuration.

Note: Common fixtures have been moved to tests/fixtures/ directory.
Import them from there instead of defining them here.
"""

import pytest
from fastapi.testclient import TestClient

from api.main import app


@pytest.fixture
def integration_client():
    """Create a TestClient for integration tests."""
    return TestClient(app)


# The mock_env_vars fixture is now provided globally in tests/conftest.py
# The mock_database_response fixture is now provided in tests/fixtures/mocks.py
