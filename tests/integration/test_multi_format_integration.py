"""
Integration test for multi-format image support.

This test demonstrates the complete end-to-end workflow of using multiple image formats
in the dataset system, from dataset creation to data loading with format detection and validation.
"""

from pathlib import Path
from uuid import uuid4

from PIL import Image

from datasets.data_loading_service import (
    BackgroundClassConfig,
    DataLoaderConfig,
    DataLoadingService,
    FormatConfig,
    ImageDatasetParams,
)
from datasets.image_dataset import ImageDataset, ImageDatasetConfig
from tests.fixtures.data import create_dataset_sets_for_background_tests


class TestMultiFormatIntegration:
    """Integration tests for multi-format image support."""

    def create_test_images_multi_format(self, dataset_sets, dataset_uuid, base_dir):
        """Create test images in multiple formats for testing."""
        images_dir = Path(base_dir) / dataset_uuid / "images"

        # Create directories and files for each dataset set
        for i, dataset_set in enumerate(dataset_sets):
            if dataset_set.coin_side_uuid:
                coin_side_dir = images_dir / str(dataset_set.coin_side_uuid)
            else:
                coin_side_dir = images_dir / "background"

            coin_side_dir.mkdir(parents=True, exist_ok=True)

            # Create different format files for different images
            formats = [
                "jpg",
                "png",
                "webp",
                "jpg",
            ]  # Use jpg instead of heic for saving
            format_ext = formats[i % len(formats)]

            image_file = coin_side_dir / f"{dataset_set.image_uuid}.{format_ext}"

            # Create a simple test image file
            # For integration tests, we'll create actual image files
            test_image = Image.new("RGB", (100, 100), color=(255, 0, 0))
            if format_ext == "webp":
                test_image.save(image_file, "WEBP")
            else:
                test_image.save(image_file)

        return images_dir

    def test_multi_format_dataset_creation_and_loading(self, tmp_path):
        """Test creating and loading a dataset with multiple image formats."""
        dataset_uuid = str(uuid4())

        # Create dataset sets with different image URLs (different extensions)
        image_urls = [
            "http://example.com/coin1.jpg",
            "http://example.com/coin2.png",
            "http://example.com/coin3.webp",
            "http://example.com/background1.heic",
        ]

        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=3,
            background_count=1,
            image_urls=image_urls,
        )

        # Create actual test images in multiple formats
        images_dir = self.create_test_images_multi_format(
            dataset_sets, dataset_uuid, tmp_path
        )

        # Test 1: Create ImageDataset with multi-format support
        config = ImageDatasetConfig(
            supported_extensions=["jpg", "png", "webp", "heic", "hevc"],
        )
        dataset = ImageDataset(dataset_sets, images_dir, config)

        # Verify dataset properties
        assert len(dataset) == 4  # All images included
        # Note: The number of classes depends on how many unique coin_side_uuids are created
        # by the test fixture, plus background class if enabled
        num_classes = dataset.get_num_classes()
        assert num_classes >= 2  # At least some coin sides + background

        # Verify all images can be loaded
        for image, label in dataset:
            assert image.shape == (3, 224, 224)  # Default image size
            assert isinstance(label.item(), int)

        # Test format validation
        validation_results = dataset.validate_dataset_formats()
        assert validation_results["total_images"] == 4
        assert len(validation_results["format_distribution"]) > 0
        assert len(validation_results["corrupted_files"]) == 0

    def test_format_detection_priority(self, tmp_path):
        """Test format detection: URL-based first, then fallback to supported extensions."""
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=1,
            background_count=0,
            image_urls=["http://example.com/test.jpg"],
        )

        # Create test directory
        images_dir = Path(tmp_path) / dataset_uuid / "images"
        coin_side_dir = images_dir / str(dataset_sets[0].coin_side_uuid)
        coin_side_dir.mkdir(parents=True)

        # Create multiple format files for the same image
        image_uuid = str(dataset_sets[0].image_uuid)
        jpg_file = coin_side_dir / f"{image_uuid}.jpg"
        png_file = coin_side_dir / f"{image_uuid}.png"
        webp_file = coin_side_dir / f"{image_uuid}.webp"

        # Create actual image files
        test_image = Image.new("RGB", (50, 50), color=(0, 255, 0))
        test_image.save(jpg_file, "JPEG")
        test_image.save(png_file, "PNG")
        test_image.save(webp_file, "WEBP")

        # Test 1: URL-based detection should find JPG first (URL has .jpg extension)
        config = ImageDatasetConfig(
            supported_extensions=["png", "jpg", "webp"],
        )
        dataset = ImageDataset(dataset_sets, images_dir, config)

        # Should find JPG file (matches URL extension)
        assert dataset.image_paths[0] == jpg_file

        # Test 2: When URL-based file doesn't exist, fallback to supported extensions
        # Remove the JPG file so URL-based detection fails
        jpg_file.unlink()

        config = ImageDatasetConfig(
            supported_extensions=["png", "webp"],  # JPG not in supported list
        )
        dataset = ImageDataset(dataset_sets, images_dir, config)

        # Should find PNG file (first in supported extensions list)
        assert dataset.image_paths[0] == png_file

    def test_format_fallback_behavior(self, tmp_path):
        """Test format fallback when preferred format is not available."""
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=1,
            background_count=0,
            image_urls=["http://example.com/test.webp"],
        )

        # Create test directory
        images_dir = Path(tmp_path) / dataset_uuid / "images"
        coin_side_dir = images_dir / str(dataset_sets[0].coin_side_uuid)
        coin_side_dir.mkdir(parents=True)

        # Create only WebP file (not the preferred JPG)
        image_uuid = str(dataset_sets[0].image_uuid)
        webp_file = coin_side_dir / f"{image_uuid}.webp"
        test_image = Image.new("RGB", (50, 50), color=(0, 0, 255))
        test_image.save(webp_file, "WEBP")

        # Test with all formats supported
        config = ImageDatasetConfig(
            supported_extensions=["jpg", "png", "webp"],
        )
        dataset = ImageDataset(dataset_sets, images_dir, config)

        # Should find WebP file as fallback
        assert dataset.image_paths[0] == webp_file

        # Verify the image can be loaded
        image, label = dataset[0]
        assert image.shape == (3, 224, 224)
        assert isinstance(label.item(), int)  # Verify label is a valid integer

    def test_data_loading_service_integration_with_multi_format(self, tmp_path):
        """Test DataLoadingService integration with multi-format support."""
        dataset_uuid = str(uuid4())

        # Create dataset sets with mixed formats
        image_urls = [
            "http://example.com/labeled1.jpg",
            "http://example.com/labeled2.png",
            "http://example.com/bg1.webp",
        ]

        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=2,
            background_count=1,
            image_urls=image_urls,
        )

        # Create actual test images
        images_dir = self.create_test_images_multi_format(
            dataset_sets, dataset_uuid, tmp_path
        )
        assert images_dir.exists()  # Verify images directory was created

        # Test DataLoadingService._create_image_dataset with multi-format config
        image_config = ImageDatasetConfig(
            supported_extensions=["jpg", "png", "webp", "heic"],
            quality_threshold=90,
            max_file_size_mb=5,
        )

        config = DataLoaderConfig(
            model_run_uuid="test-uuid",
            format_config=FormatConfig(
                image_config=image_config,
                format_validation=True,
            ),
            background_config=BackgroundClassConfig(
                include_background_class=True, background_class_name="no_object"
            ),
        )

        params = ImageDatasetParams(
            dataset_sets=dataset_sets,
            dataset_uuid=dataset_uuid,
            content_updated_at=None,
            set_name="train",
            config=config,
            base_dir=str(tmp_path),
        )

        # pylint: disable=protected-access
        dataset = DataLoadingService._create_image_dataset(params)

        # Verify dataset was created successfully
        assert len(dataset) == 3
        # Note: The number of classes depends on how many unique coin_side_uuids are created
        num_classes = dataset.get_num_classes()
        assert num_classes >= 2  # At least some coin sides + background

        # Verify multi-format configuration was applied
        assert dataset.config.quality_threshold == 90
        assert dataset.config.max_file_size_mb == 5

        # Verify all images can be loaded
        for image, label in dataset:
            assert image.shape == (3, 224, 224)
            assert isinstance(label.item(), int)  # Verify label is a valid integer

    def test_performance_monitoring_integration(self, tmp_path):
        """Test performance monitoring with multi-format images."""
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=2,
            background_count=0,
            image_urls=["http://example.com/test1.jpg", "http://example.com/test2.png"],
        )

        # Create test images
        images_dir = self.create_test_images_multi_format(
            dataset_sets, dataset_uuid, tmp_path
        )

        # Create dataset with performance monitoring
        config = ImageDatasetConfig()
        dataset = ImageDataset(dataset_sets, images_dir, config)

        # Load some images to generate performance data
        for _ in dataset:
            pass  # Just iterate to trigger performance tracking

        # Check that performance metrics were collected (only if images were loaded)
        if len(dataset) > 0:
            assert hasattr(dataset, "performance_metrics")

            # Get performance report
            report = dataset.get_format_performance_report()
            assert isinstance(report, dict)

            # Should have data for the formats we created
        else:
            # If no images were loaded, performance metrics won't exist
            report = dataset.get_format_performance_report()
            assert report == {"message": "No performance data available"}
        format_keys = list(report.keys())
        assert len(format_keys) > 0

        # Each format should have performance statistics
        for format_name in format_keys:
            format_stats = report[format_name]
            assert "avg_loading_time" in format_stats
            assert "total_files" in format_stats
            assert "success_rate" in format_stats
            assert format_stats["success_rate"] > 0  # Should have successful loads
