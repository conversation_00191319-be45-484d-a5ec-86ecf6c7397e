"""
Integration tests for the complete data flow from database to training.

This module tests the end-to-end data flow from dataset_sets in the database
to training data loaders, ensuring proper handling of different set types
and image loading functionality.

All tests automatically use virtual directories to prevent overwriting real data.
The dataset directories are isolated using the test fixtures in conftest.py.
"""

import shutil
import uuid
from unittest.mock import MagicMock, patch

import pytest
from PIL import Image

from config.paths import get_dataset_paths
from database.models.dataset_set import SetType
from datasets.data_loading_service import (
    DataLoaderConfig,
    DataLoadingError,
    DataLoadingService,
    DataProcessingConfig,
    DataSplitConfig,
)
from datasets.image_dataset import ImageDataset


class TestDataFlowIntegration:
    """Test the complete data flow integration."""

    @pytest.fixture
    def cleanup_test_datasets(self):
        """
        Fixture to ensure test dataset directories are cleaned up after individual tests.

        This fixture can be used by tests that create specific dataset directories
        and want to ensure they're cleaned up even if the test fails.
        """
        dataset_uuids_to_cleanup = []

        class CleanupHelper:
            """Helper class to add dataset UUIDs to be cleaned up."""

            def add_dataset_uuid(self, dataset_uuid: str):
                """Add a dataset UUID to be cleaned up after the test."""
                dataset_uuids_to_cleanup.append(dataset_uuid)

        helper = CleanupHelper()
        yield helper

        # Cleanup after test completes
        for dataset_uuid in dataset_uuids_to_cleanup:
            dataset_paths = get_dataset_paths(dataset_uuid)
            dataset_dir = dataset_paths.base
            if dataset_dir.exists():
                shutil.rmtree(dataset_dir, ignore_errors=True)

    def create_test_images(self, dataset_sets, dataset_uuid):
        """Create test images on disk for the dataset sets using virtual directories."""
        # Use the centralized path system which is now automatically isolated
        dataset_paths = get_dataset_paths(dataset_uuid)
        images_dir = dataset_paths.images

        for dataset_set in dataset_sets:
            # Create directory structure using centralized paths
            coin_side_dir = dataset_paths.coin_side_dir(dataset_set.coin_side_uuid)
            coin_side_dir.mkdir(parents=True, exist_ok=True)

            # Create a simple test image
            image_path = dataset_paths.image_path(
                dataset_set.coin_side_uuid, dataset_set.image_uuid, "jpg"
            )
            image = Image.new("RGB", (64, 64), color="red")
            image.save(image_path)

        return images_dir

    def test_image_dataset_creation(
        self, sample_dataset_sets_integration, cleanup_test_datasets
    ):
        """Test ImageDataset creation with real files."""
        # Use a unique dataset UUID for this test
        dataset_uuid = str(uuid.uuid4())
        cleanup_test_datasets.add_dataset_uuid(dataset_uuid)

        # Create test images using virtual directories
        images_dir = self.create_test_images(
            sample_dataset_sets_integration, dataset_uuid
        )

        # Create ImageDataset
        dataset = ImageDataset(sample_dataset_sets_integration, images_dir)

        # Verify dataset properties - expect 0 images since all have image_url=None
        # This tests the real-world scenario where image URLs are missing
        assert (
            len(dataset) == 0
        ), f"Expected 0 images (all should be skipped due to missing image_url), got {len(dataset)}"

        # Since no images are loaded, we can't test image loading
        # But we can verify the dataset handles missing URLs gracefully
        assert dataset.get_dataset_info()["num_samples"] == 0

    def test_image_dataset_set_type_filtering(
        self, sample_dataset_sets_integration, cleanup_test_datasets
    ):
        """Test that ImageDataset works with filtered dataset sets by type."""
        # Use a unique dataset UUID for this test
        dataset_uuid = str(uuid.uuid4())
        cleanup_test_datasets.add_dataset_uuid(dataset_uuid)

        # Create test images using virtual directories
        images_dir = self.create_test_images(
            sample_dataset_sets_integration, dataset_uuid
        )

        # Filter for training sets only
        train_sets = [
            ds for ds in sample_dataset_sets_integration if ds.set_type == SetType.TRAIN
        ]

        # Create ImageDataset with training sets only
        dataset = ImageDataset(train_sets, images_dir)

        # Verify dataset handles missing image URLs correctly
        # Since all dataset sets have image_url=None, they should be skipped
        assert (
            len(dataset) == 0
        ), f"Expected 0 images (all should be skipped due to missing image_url), got {len(dataset)}"

        # Since no images are loaded, we can't test image loading
        # But we can verify the dataset handles missing URLs gracefully
        assert dataset.get_dataset_info()["num_samples"] == 0

    async def test_data_loading_service_mock_data_error(self):
        """Test DataLoadingService properly rejects mock data requests."""
        dataset_uuid = str(uuid.uuid4())

        # Test that requesting mock data raises an appropriate error
        config = DataLoaderConfig(
            model_run_uuid="test-mock-data-uuid",
            processing_config=DataProcessingConfig(
                use_mock_data=True,
                batch_size=4,
            ),
        )

        with pytest.raises(
            DataLoadingError, match="Mock data is not supported in production"
        ):
            await DataLoadingService.create_data_loaders(
                dataset_uuid=dataset_uuid,
                config=config,
            )

    @patch("datasets.data_loading_service.DatasetService.get_dataset")
    @patch("datasets.data_loading_service.DatasetService.get_dataset_sets")
    async def test_data_loading_service_with_validation(
        self,
        mock_get_dataset_sets,
        mock_get_dataset,
        sample_dataset_sets_integration,
        cleanup_test_datasets,
    ):
        """Test DataLoadingService with validation sets."""
        dataset_uuid = str(uuid.uuid4())
        cleanup_test_datasets.add_dataset_uuid(dataset_uuid)

        # Create test images using virtual directories
        self.create_test_images(sample_dataset_sets_integration, dataset_uuid)

        # Mock dataset service methods
        mock_get_dataset.return_value = {
            "uuid": dataset_uuid,
            "content_updated_at": None,
        }

        # Mock get_dataset_sets to return our sample data in batches
        def mock_dataset_sets_generator(
            *_args, **_kwargs
        ):  # pylint: disable=unused-argument
            yield sample_dataset_sets_integration

        mock_get_dataset_sets.side_effect = mock_dataset_sets_generator

        # Create data loaders with validation
        # (no need to mock paths - they're automatically virtual)
        config = DataLoaderConfig(
            model_run_uuid="test-data-flow-uuid",
            processing_config=DataProcessingConfig(
                use_mock_data=False,
                batch_size=2,
            ),
            split_config=DataSplitConfig(include_validation=True),
        )
        # Since all dataset sets have image_url=None, they will be skipped
        # and the datasets will be empty, causing DataLoader creation to fail
        with pytest.raises(DataLoadingError, match="Failed to create data loaders"):
            await DataLoadingService.create_data_loaders(
                dataset_uuid=dataset_uuid,
                config=config,
            )

    def test_dataset_path_management(self, cleanup_test_datasets):
        """Test dataset path management functions with virtual directories."""
        self._test_path_generation(cleanup_test_datasets)
        self._test_directory_creation(cleanup_test_datasets)

    def _test_path_generation(self, cleanup_test_datasets):
        """Test path generation functionality."""
        dataset_uuid = str(uuid.uuid4())
        cleanup_test_datasets.add_dataset_uuid(dataset_uuid)

        coin_side_uuid = str(uuid.uuid4())
        image_uuid = str(uuid.uuid4())
        content_updated_at = "2025-01-01 12:00:00+00"

        # Test path generation
        dataset_paths = get_dataset_paths(dataset_uuid, content_updated_at)
        dataset_dir = dataset_paths.base
        images_dir = dataset_paths.images
        coin_side_dir = dataset_paths.coin_side_dir(coin_side_uuid)
        image_path = dataset_paths.image_path(coin_side_uuid, image_uuid)

        # Verify path structure
        assert dataset_dir.name.startswith(dataset_uuid)
        assert images_dir == dataset_dir / "images"
        assert coin_side_dir == images_dir / coin_side_uuid
        assert image_path == coin_side_dir / f"{image_uuid}.jpg"

    def _test_directory_creation(self, cleanup_test_datasets):
        """Test directory creation functionality."""
        dataset_uuid = str(uuid.uuid4())
        cleanup_test_datasets.add_dataset_uuid(dataset_uuid)

        content_updated_at = "2025-01-01 12:00:00+00"

        # Test directory creation
        dataset_paths = get_dataset_paths(dataset_uuid, content_updated_at)
        directories = dataset_paths.ensure_directories()
        assert "dataset" in directories
        assert "images" in directories
        assert directories["dataset"].exists()
        assert directories["images"].exists()

    async def test_data_loading_service_validation_methods(self):
        """Test DataLoadingService validation and info methods."""
        # Create mock data loaders
        mock_train_loader = MagicMock()
        mock_train_loader.dataset = [1, 2, 3]  # 3 samples
        mock_train_loader.batch_size = 2
        mock_train_loader.sampler = None

        mock_test_loader = MagicMock()
        mock_test_loader.dataset = [1, 2]  # 2 samples
        mock_test_loader.batch_size = 2
        mock_test_loader.sampler = None

        data_loaders = {
            "train": mock_train_loader,
            "test": mock_test_loader,
        }

        # Test data loader info extraction (inline)
        info = {}
        for name, loader in data_loaders.items():
            info[name] = {
                "num_samples": len(loader.dataset),
                "batch_size": loader.batch_size,
                "num_batches": len(loader),
                "shuffle": loader.sampler is not None,
            }
        assert "train" in info
        assert "test" in info
        assert info["train"]["num_samples"] == 3
        assert info["test"]["num_samples"] == 2

        # Test data loader validation (inline)
        required_keys = {"train", "test"}
        is_valid = required_keys.issubset(data_loaders.keys())
        if is_valid:
            for name, loader in data_loaders.items():
                if len(loader.dataset) == 0:
                    is_valid = False
                    break
        assert is_valid

        # Test validation with missing loader
        incomplete_loaders = {"train": mock_train_loader}
        is_valid = required_keys.issubset(incomplete_loaders.keys())
        assert not is_valid
