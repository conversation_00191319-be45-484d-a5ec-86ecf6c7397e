"""
Integration test for background class functionality.

This test demonstrates the complete end-to-end workflow of using background classes
in the dataset system, from dataset creation to data loading.
"""

from uuid import uuid4

from datasets.data_loading_service import (
    BackgroundClassConfig,
    DataLoaderConfig,
    DataLoadingService,
    ImageDatasetParams,
)
from datasets.image_dataset import ImageDataset, ImageDatasetConfig
from tests.fixtures.data import create_dataset_sets_for_background_tests


class TestBackgroundClassIntegration:
    """Integration tests for background class functionality."""

    def test_end_to_end_background_class_workflow(self, tmp_path):
        """Test the complete workflow from dataset sets to data loaders with background class."""

        # Create test dataset sets with mixed labeled and background images using fixture
        dataset_uuid = str(uuid4())
        image_urls = [
            "http://example.com/labeled1.jpg",
            "http://example.com/labeled2.jpg",
            "http://example.com/bg1.jpg",
            "http://example.com/bg2.jpg",
        ]

        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=2,
            background_count=2,
            image_urls=image_urls,
        )

        # Get the coin side UUIDs for verification
        coin_side_1 = dataset_sets[0].coin_side_uuid
        coin_side_2 = dataset_sets[1].coin_side_uuid

        # Test 1: Create ImageDataset directly with background class enabled
        config = ImageDatasetConfig(include_background_class=True)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Verify dataset properties
        assert len(dataset) == 4  # All images included
        assert dataset.get_num_classes() == 3  # 2 coin sides + 1 background

        label_mapping = dataset.get_label_mapping()
        assert len(label_mapping) == 3
        assert coin_side_1 in label_mapping
        assert coin_side_2 in label_mapping
        assert "background" in label_mapping

        # Background should have the highest label value
        background_label = label_mapping["background"]
        assert background_label == 2

        # Verify class names
        class_names = dataset.get_class_names()
        assert len(class_names) == 3
        assert class_names[-1] == "background"  # Background should be last

        # Test 2: Verify image path construction (using fixture naming convention)
        expected_paths = [
            tmp_path / coin_side_1 / "jpg_image.jpg",  # First labeled image
            tmp_path / coin_side_2 / "jpg_image.jpg",  # Second labeled image
            tmp_path / "background" / "bg_image_1.jpg",  # First background image
            tmp_path / "background" / "bg_image_2.jpg",  # Second background image
        ]

        for expected_path in expected_paths:
            assert expected_path in dataset.image_paths

        # Test 3: Verify dataset info includes background counts
        info = dataset.get_dataset_info()
        assert info["num_coin_sides"] == 3  # Including background
        assert "background" in info["coin_side_counts"]
        assert info["coin_side_counts"]["background"] == 2

    def test_background_class_disabled_integration(self, tmp_path):
        """Test that background images are properly filtered when disabled."""

        dataset_uuid = str(uuid4())
        image_urls = ["http://example.com/labeled1.jpg", "http://example.com/bg1.jpg"]

        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=1,
            background_count=1,
            image_urls=image_urls,
        )

        # Get the coin side UUID for verification
        coin_side_1 = dataset_sets[0].coin_side_uuid

        # Create dataset with background class disabled
        config = ImageDatasetConfig(include_background_class=False)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Verify only labeled images are included
        assert len(dataset) == 1
        assert dataset.get_num_classes() == 1

        label_mapping = dataset.get_label_mapping()
        assert len(label_mapping) == 1
        assert coin_side_1 in label_mapping
        assert "background" not in label_mapping

    def test_data_loading_service_integration(self, tmp_path):
        """Test DataLoadingService integration with background class."""

        dataset_uuid = str(uuid4())
        image_urls = ["http://example.com/labeled1.jpg", "http://example.com/bg1.jpg"]

        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=1,
            background_count=1,
            image_urls=image_urls,
        )

        # Test DataLoadingService._create_image_dataset with background class
        background_config = BackgroundClassConfig(
            include_background_class=True, background_class_name="no_object"
        )
        config = DataLoaderConfig(
            model_run_uuid="test-uuid",
            background_config=background_config,
        )

        params = ImageDatasetParams(
            dataset_sets=dataset_sets,
            dataset_uuid=dataset_uuid,
            content_updated_at=None,
            set_name="train",
            config=config,
            base_dir=str(tmp_path),
        )
        # pylint: disable=protected-access
        dataset = DataLoadingService._create_image_dataset(params)

        # Verify custom background class name is used
        assert dataset.config.background_class_name == "no_object"

        label_mapping = dataset.get_label_mapping()
        assert "no_object" in label_mapping
        assert "background" not in label_mapping

    def test_custom_background_class_name_integration(self, tmp_path):
        """Test integration with custom background class name."""

        dataset_uuid = str(uuid4())
        image_urls = ["http://example.com/bg1.jpg"]

        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=0,
            background_count=1,
            image_urls=image_urls,
        )

        # Test with custom background class name
        config = ImageDatasetConfig(
            include_background_class=True, background_class_name="empty_image"
        )
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Verify custom name is used throughout
        label_mapping = dataset.get_label_mapping()
        assert "empty_image" in label_mapping
        assert "background" not in label_mapping

        class_names = dataset.get_class_names()
        assert "empty_image" in class_names

        # Verify image path uses custom name (fixture generates bg_image_1 for first background)
        expected_path = tmp_path / "empty_image" / "bg_image_1.jpg"
        assert expected_path in dataset.image_paths

        # Verify dataset info uses custom name
        info = dataset.get_dataset_info()
        assert "empty_image" in info["coin_side_counts"]

    def test_mixed_dataset_realistic_scenario(self, tmp_path):
        """Test a realistic scenario with mixed labeled and background images."""

        # Simulate a realistic dataset with multiple coin sides and background images
        coin_sides = [str(uuid4()) for _ in range(3)]
        dataset_uuid = str(uuid4())

        # Create image URLs for the fixture
        image_urls = []
        for coin_side in coin_sides:
            for i in range(2):
                image_urls.append(f"http://example.com/coin_{coin_side}_{i}.jpg")
        for i in range(3):
            image_urls.append(f"http://example.com/bg_{i}.jpg")

        # Use fixture to create dataset sets (6 labeled + 3 background)
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=6,
            background_count=3,
            image_urls=image_urls,
        )

        # Override coin_side_uuid values to create 3 distinct coin sides (2 images each)
        for i, coin_side in enumerate(coin_sides):
            dataset_sets[i * 2].coin_side_uuid = (
                coin_side  # First image for this coin side
            )
            dataset_sets[i * 2 + 1].coin_side_uuid = (
                coin_side  # Second image for this coin side
            )

        # Create dataset with background class enabled
        config = ImageDatasetConfig(include_background_class=True)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Verify dataset properties
        assert len(dataset) == 9  # 6 labeled + 3 background
        assert dataset.get_num_classes() == 4  # 3 coin sides + 1 background

        # Verify label distribution
        info = dataset.get_dataset_info()
        assert info["coin_side_counts"]["background"] == 3
        for coin_side in coin_sides:
            assert info["coin_side_counts"][coin_side] == 2

        # Verify all coin sides and background are in class names
        class_names = dataset.get_class_names()
        assert len(class_names) == 4
        for coin_side in coin_sides:
            assert coin_side in class_names
        assert "background" in class_names
        assert class_names[-1] == "background"  # Background should be last
