"""
Integration tests for DataLoadingService with cache optimization.
"""

import asyncio
from datetime import datetime
from unittest.mock import patch
from uuid import uuid4

import pytest

from database.services.dataset_state_cache import DatasetStateCacheService
from datasets.data_loading_service import (
    DataLoaderConfig,
    DataLoadingError,
    DataLoadingService,
    DataProcessingConfig,
    DataSplitConfig,
)


class TestDataLoadingServiceCacheIntegration:
    """Integration test cases for DataLoadingService cache functionality."""

    def setup_method(self):
        """Clear cache before each test."""
        DatasetStateCacheService.clear_cache()

    def teardown_method(self):
        """Clear cache after each test."""
        DatasetStateCacheService.clear_cache()

    @patch(
        "datasets.data_loading_service.DataLoadingService.verify_dataset_completeness"
    )
    @patch("datasets.data_loading_service.DataLoadingService.create_data_loaders")
    def test_create_data_loaders_with_cache_optimization_cache_miss(
        self, mock_create_data_loaders, mock_verify_dataset
    ):
        """Test data loader creation with cache optimization when cache is missed."""
        # Setup test data
        dataset_uuid = str(uuid4())
        timestamp = datetime.now()

        training_data = {
            "dataset": {
                "uuid": dataset_uuid,
                "content_updated_at": timestamp,
            },
            "cache_state": {
                "cache_key": f"{dataset_uuid}_{timestamp.isoformat()}",
                "is_cache_hit": False,
                "dataset_preparation_skipped": False,
            },
        }

        # Mock the dataset verification
        mock_verify_dataset.return_value = {
            "expected_count": 100,
            "available_count": 100,
            "completeness": 1.0,
        }

        # Mock the create_data_loaders method
        mock_data_loaders = {"train": "mock_train_loader", "test": "mock_test_loader"}
        mock_create_data_loaders.return_value = mock_data_loaders

        config = DataLoaderConfig(
            model_run_uuid="test-model-run-uuid",
            processing_config=DataProcessingConfig(
                batch_size=32,
                use_mock_data=False,
            ),
            split_config=DataSplitConfig(test_size=0.2),
        )

        # Call the method
        result = asyncio.run(
            DataLoadingService.create_data_loaders_with_cache_optimization(
                training_data, config=config
            )
        )

        # Verify the result
        assert result == mock_data_loaders

        # Verify that create_data_loaders was called
        mock_create_data_loaders.assert_called_once()

        # Verify that dataset preparation was marked as complete
        assert DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)

    @patch(
        "datasets.data_loading_service.DataLoadingService.verify_dataset_completeness"
    )
    @patch("datasets.data_loading_service.DataLoadingService.create_data_loaders")
    def test_create_data_loaders_with_cache_optimization_cache_hit(
        self, mock_create_data_loaders, mock_verify_dataset
    ):
        """Test data loader creation with cache optimization when cache is hit."""
        # Setup test data
        dataset_uuid = str(uuid4())
        timestamp = datetime.now()

        # Pre-populate cache
        DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid,
            content_updated_at=timestamp,
            is_prepared=True,
        )

        training_data = {
            "dataset": {
                "uuid": dataset_uuid,
                "content_updated_at": timestamp,
            },
            "cache_state": {
                "cache_key": f"{dataset_uuid}_{timestamp.isoformat()}",
                "is_cache_hit": True,
                "dataset_preparation_skipped": True,
            },
        }

        # Mock the dataset verification
        mock_verify_dataset.return_value = {
            "expected_count": 100,
            "available_count": 100,
            "completeness": 1.0,
        }

        # Mock the create_data_loaders method
        mock_data_loaders = {"train": "mock_train_loader", "test": "mock_test_loader"}
        mock_create_data_loaders.return_value = mock_data_loaders

        config = DataLoaderConfig(
            model_run_uuid="test-model-run-uuid",
            processing_config=DataProcessingConfig(
                batch_size=32,
                use_mock_data=False,
            ),
            split_config=DataSplitConfig(test_size=0.2),
        )

        # Call the method
        result = asyncio.run(
            DataLoadingService.create_data_loaders_with_cache_optimization(
                training_data, config=config
            )
        )

        # Verify the result
        assert result == mock_data_loaders

        # Verify that create_data_loaders was still called
        # (In the current implementation, we still create data loaders even on cache hit)
        mock_create_data_loaders.assert_called_once()

    def test_create_data_loaders_with_cache_optimization_missing_dataset_uuid(self):
        """Test error handling when dataset UUID is missing."""
        training_data = {
            "dataset": {},  # Missing uuid
            "cache_state": {
                "is_cache_hit": False,
                "dataset_preparation_skipped": False,
            },
        }

        config = DataLoaderConfig(
            model_run_uuid="test-model-run-uuid",
            processing_config=DataProcessingConfig(
                batch_size=32,
                use_mock_data=False,
            ),
            split_config=DataSplitConfig(test_size=0.2),
        )

        with pytest.raises(DataLoadingError, match="Dataset UUID not found"):
            asyncio.run(
                DataLoadingService.create_data_loaders_with_cache_optimization(
                    training_data, config=config
                )
            )

    def test_create_data_loaders_with_cache_optimization_missing_dataset(self):
        """Test error handling when dataset information is missing."""
        training_data = {
            # Missing dataset key
            "cache_state": {
                "is_cache_hit": False,
                "dataset_preparation_skipped": False,
            },
        }

        config = DataLoaderConfig(
            model_run_uuid="test-model-run-uuid",
            processing_config=DataProcessingConfig(
                batch_size=32,
                use_mock_data=False,
            ),
            split_config=DataSplitConfig(test_size=0.2),
        )

        with pytest.raises(DataLoadingError, match="Dataset UUID not found"):
            asyncio.run(
                DataLoadingService.create_data_loaders_with_cache_optimization(
                    training_data, config=config
                )
            )

    @patch(
        "datasets.data_loading_service.DataLoadingService.verify_dataset_completeness"
    )
    @patch("datasets.data_loading_service.DataLoadingService.create_data_loaders")
    def test_create_data_loaders_with_cache_optimization_no_cache_state(
        self, mock_create_data_loaders, mock_verify_dataset
    ):
        """Test data loader creation when no cache state is provided."""
        # Setup test data
        dataset_uuid = str(uuid4())
        timestamp = datetime.now()

        training_data = {
            "dataset": {
                "uuid": dataset_uuid,
                "content_updated_at": timestamp,
            },
            # No cache_state provided
        }

        # Mock the dataset verification
        mock_verify_dataset.return_value = {
            "expected_count": 100,
            "available_count": 100,
            "completeness": 1.0,
        }

        # Mock the create_data_loaders method
        mock_data_loaders = {"train": "mock_train_loader", "test": "mock_test_loader"}
        mock_create_data_loaders.return_value = mock_data_loaders

        config = DataLoaderConfig(
            model_run_uuid="test-model-run-uuid",
            processing_config=DataProcessingConfig(
                batch_size=32,
                use_mock_data=False,
            ),
            split_config=DataSplitConfig(test_size=0.2),
        )

        # Call the method
        result = asyncio.run(
            DataLoadingService.create_data_loaders_with_cache_optimization(
                training_data, config=config
            )
        )

        # Verify the result
        assert result == mock_data_loaders

        # Verify that create_data_loaders was called
        mock_create_data_loaders.assert_called_once()

        # Verify that dataset preparation was marked as complete
        assert DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)

    @patch(
        "datasets.data_loading_service.DataLoadingService.verify_dataset_completeness"
    )
    @patch("datasets.data_loading_service.DataLoadingService.create_data_loaders")
    def test_create_data_loaders_with_cache_optimization_with_augmentations(
        self, mock_create_data_loaders, mock_verify_dataset
    ):
        """Test data loader creation with cache optimization and augmentations."""
        # Setup test data
        dataset_uuid = str(uuid4())
        timestamp = datetime.now()

        training_data = {
            "dataset": {
                "uuid": dataset_uuid,
                "content_updated_at": timestamp,
            },
            "cache_state": {
                "cache_key": f"{dataset_uuid}_{timestamp.isoformat()}",
                "is_cache_hit": False,
                "dataset_preparation_skipped": False,
            },
        }

        augmentations = ["rotation", "flip"]

        # Mock the dataset verification
        mock_verify_dataset.return_value = {
            "expected_count": 100,
            "available_count": 100,
            "completeness": 1.0,
        }

        # Mock the create_data_loaders method
        mock_data_loaders = {"train": "mock_train_loader", "test": "mock_test_loader"}
        mock_create_data_loaders.return_value = mock_data_loaders

        config = DataLoaderConfig(
            model_run_uuid="test-model-run-uuid",
            processing_config=DataProcessingConfig(
                batch_size=32,
                use_mock_data=False,
            ),
            split_config=DataSplitConfig(test_size=0.2),
        )

        # Call the method with augmentations
        result = asyncio.run(
            DataLoadingService.create_data_loaders_with_cache_optimization(
                training_data, augmentations=augmentations, config=config
            )
        )

        # Verify the result
        assert result == mock_data_loaders

        # Verify that create_data_loaders was called with augmentations
        # Note: config will be a DataLoaderConfig instance, not None
        call_args = mock_create_data_loaders.call_args
        assert call_args.kwargs["dataset_uuid"] == dataset_uuid
        assert call_args.kwargs["augmentations"] == augmentations
        assert call_args.kwargs["config"] is not None

    def test_cache_state_persistence_across_calls(self):
        """Test that cache state persists across multiple calls."""
        dataset_uuid = str(uuid4())
        timestamp = datetime.now()

        # First call should be a cache miss
        cache_state_1 = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)
        assert not cache_state_1

        # Mark as prepared
        DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid,
            content_updated_at=timestamp,
            is_prepared=True,
        )

        # Second call should be a cache hit
        cache_state_2 = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)
        assert cache_state_2

        # Third call should still be a cache hit
        cache_state_3 = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)
        assert cache_state_3

    def test_cache_invalidation_integration(self):
        """Test cache invalidation in an integration scenario."""
        dataset_uuid = str(uuid4())
        old_timestamp = datetime.now()

        # Set cache for old timestamp
        DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid,
            content_updated_at=old_timestamp,
            is_prepared=True,
        )

        # Verify cache is valid for old timestamp
        assert DatasetStateCacheService.is_cache_valid(dataset_uuid, old_timestamp)

        # Simulate dataset content update (new timestamp)
        new_timestamp = datetime.now()

        # Cache should be invalid for new timestamp
        assert not DatasetStateCacheService.is_cache_valid(dataset_uuid, new_timestamp)

        # Old cache should still be valid
        assert DatasetStateCacheService.is_cache_valid(dataset_uuid, old_timestamp)

        # Invalidate all cache for this dataset
        invalidated_count = DatasetStateCacheService.invalidate_dataset_cache(
            dataset_uuid
        )
        assert invalidated_count == 1

        # Now both should be invalid
        assert not DatasetStateCacheService.is_cache_valid(dataset_uuid, old_timestamp)
        assert not DatasetStateCacheService.is_cache_valid(dataset_uuid, new_timestamp)
