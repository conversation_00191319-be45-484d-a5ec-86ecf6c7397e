"""
Shared utilities for trainer integration tests.

This module contains common fixtures, helper functions, and test patterns
to reduce code duplication across trainer integration test files.
"""

from dataclasses import dataclass
from pathlib import Path
from unittest.mock import MagicMock, patch
from uuid import uuid4

import torch
from torch import nn, optim
from torch.utils.data import DataLoader, TensorDataset

from src.train.trainer_config import DatabaseConfig


def _create_mock_tensor_output(args, output_size=1):
    """
    Create a mock tensor output with appropriate shape.

    This utility function reduces code duplication between conftest.py
    and trainer_test_utils.py for mock tensor creation.
    """
    if args:
        input_tensor = args[0]
        if hasattr(input_tensor, "shape"):
            batch_size = input_tensor.shape[0] if len(input_tensor.shape) > 0 else 1
            mock_output = MagicMock()
            mock_output.shape = (batch_size, output_size)
            mock_output.detach.return_value = mock_output
            mock_output.cpu.return_value = mock_output
            mock_output.numpy.return_value = [[0.5] * output_size] * batch_size
            return mock_output

    mock_output = MagicMock()
    mock_output.shape = (1, output_size)
    return mock_output


@dataclass
class ModelComponentsConfig:
    """Configuration for creating test model components."""

    input_size: int = 10
    hidden_size: int = 5
    output_size: int = 1
    binary_classification: bool = True
    optimizer_type: str = "adam"
    learning_rate: float = 0.001


def create_simple_model(input_size=10, hidden_size=5, output_size=1, use_mock=True):
    """Create a simple test model."""

    if use_mock:
        # Use lightweight mock model for faster tests
        mock_model = MagicMock()
        mock_model.training = True

        # Mock parameters method - return iterator for compatibility
        mock_param = MagicMock()
        mock_param.data = MagicMock()
        mock_param.grad = None
        mock_param.device = MagicMock()

        # Dynamic device type that can be updated
        def get_device_type():
            if torch.backends.mps.is_available():
                return "mps"
            if torch.cuda.is_available():
                return "cuda"
            return "cpu"

        mock_param.device.type = get_device_type()
        mock_model.parameters.return_value = iter([mock_param])  # Return iterator
        mock_model.named_parameters.return_value = iter([("mock_param", mock_param)])

        # Mock state dict - return serializable tensors
        mock_state_dict = {
            "mock_param": torch.randn(1, 1)
        }  # Real tensor for serialization
        mock_model.state_dict.return_value = mock_state_dict
        mock_model.load_state_dict = MagicMock()

        # Mock training methods
        mock_model.eval.return_value = mock_model
        mock_model.train.return_value = mock_model
        mock_model.to.return_value = mock_model

        # Mock forward pass
        def mock_forward(*args, **kwargs):  # pylint: disable=unused-argument
            return _create_mock_tensor_output(args, output_size)

        mock_model.side_effect = mock_forward
        mock_model.__call__ = mock_forward

        return mock_model

    # Original real model implementation (for specific tests that need it)
    class SimpleTestModel(nn.Module):
        """A simple model for integration testing."""

        def __init__(self, input_size, hidden_size, output_size):
            super().__init__()
            self.linear1 = nn.Linear(input_size, hidden_size)
            self.relu = nn.ReLU()
            self.linear2 = nn.Linear(hidden_size, output_size)

        def forward(self, x):
            """Forward pass of the model."""
            x = self.linear1(x)
            x = self.relu(x)
            x = self.linear2(x)
            return x

    return SimpleTestModel(input_size, hidden_size, output_size)


def create_test_data_loaders(
    train_samples=16,  # Reduced from 64 for faster tests
    test_samples=8,  # Reduced from 32 for faster tests
    input_size=10,
    batch_size=4,  # Reduced from 8 for faster tests
    binary_classification=True,
):
    """Create test data loaders for integration testing."""
    torch.manual_seed(42)

    x_train = torch.randn(train_samples, input_size)
    x_test = torch.randn(test_samples, input_size)

    if binary_classification:
        y_train = torch.randint(0, 2, (train_samples, 1)).float()
        y_test = torch.randint(0, 2, (test_samples, 1)).float()
    else:
        y_train = torch.randn(train_samples, 1)
        y_test = torch.randn(test_samples, 1)

    train_dataset = TensorDataset(x_train, y_train)
    test_dataset = TensorDataset(x_test, y_test)

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    return {"train": train_loader, "test": test_loader}


def create_test_model_components(config: ModelComponentsConfig = None, use_mock=True):
    """Create test model components for integration testing."""
    if config is None:
        config = ModelComponentsConfig()

    torch.manual_seed(42)

    model = create_simple_model(
        config.input_size, config.hidden_size, config.output_size, use_mock=use_mock
    )

    if use_mock:
        # Use lightweight mock loss and optimizer for faster tests

        # Mock loss function
        mock_loss = MagicMock()

        def mock_loss_call(predictions, targets):  # pylint: disable=unused-argument
            mock_loss_value = MagicMock()
            mock_loss_value.item.return_value = 0.5
            mock_loss_value.backward = MagicMock()
            return mock_loss_value

        mock_loss.side_effect = mock_loss_call
        mock_loss.__call__ = mock_loss_call

        # Mock optimizer
        mock_optimizer = MagicMock()
        mock_optimizer.param_groups = [{"lr": config.learning_rate}]
        mock_optimizer.step = MagicMock()
        mock_optimizer.zero_grad = MagicMock()

        return {"model": model, "loss_fn": mock_loss, "optimizer": mock_optimizer}

    # Original real components (for specific tests that need them)
    if config.binary_classification:
        loss_fn = nn.BCEWithLogitsLoss()
    else:
        loss_fn = nn.MSELoss()

    if config.optimizer_type.lower() == "adam":
        optimizer = optim.Adam(model.parameters(), lr=config.learning_rate)
    elif config.optimizer_type.lower() == "sgd":
        optimizer = optim.SGD(model.parameters(), lr=config.learning_rate)
    else:
        raise ValueError(f"Unsupported optimizer type: {config.optimizer_type}")

    return {"model": model, "loss_fn": loss_fn, "optimizer": optimizer}


def create_test_training_config(
    temp_dir,
    model_id="test_model",
    epochs=1,  # Reduced from 2 for faster tests
    gradient_clip_max_norm=None,
    learning_rate_schedule=None,
):
    """Create test training configuration."""
    config = {
        "model_id": model_id,
        "model_run_uuid": f"test-{model_id}-uuid",
        "run_output_dir": temp_dir,
        "epochs": epochs,
    }

    if gradient_clip_max_norm is not None:
        config["gradient_clip_max_norm"] = gradient_clip_max_norm

    if learning_rate_schedule is not None:
        config["learning_rate_schedule"] = learning_rate_schedule

    return config


def create_mock_database_callback_setup():
    """Create mock setup for database callback testing."""
    mock_callback_instance = MagicMock()
    model_run_uuid = str(uuid4())
    database_config = DatabaseConfig(
        model_run_uuid=model_run_uuid, profile="development"
    )

    return mock_callback_instance, model_run_uuid, database_config


def mock_training_methods_for_callback_testing(trainer):
    """Create common mock setup for callback testing."""
    return (
        patch.object(
            trainer,
            "_validate_epoch",
            return_value={"validation_loss": 0.6, "validation_accuracy": 0.75},
        ),
        patch.object(
            trainer,
            "_train_epoch",
            return_value={"train_loss": 0.5, "train_accuracy": 0.8},
        ),
        patch.object(trainer.get_metrics_callback(), "_collect_resource_metrics"),
    )


def mock_training_and_run(trainer):
    """
    Mock training methods and run training with standard return values.

    This utility function reduces code duplication across integration tests
    by providing a standard way to mock training methods and execute training.

    Args:
        trainer: The ModelTrainer instance to mock and run

    Returns:
        The metrics returned from trainer.train()
    """
    metrics_callback = trainer.get_metrics_callback()
    with patch.object(trainer, "_train_epoch") as mock_train, patch.object(
        trainer, "_validate_epoch"
    ) as mock_validate, patch.object(metrics_callback, "_collect_resource_metrics"):
        mock_train.return_value = {"train_loss": 0.5, "train_accuracy": 0.8}
        mock_validate.return_value = {
            "validation_loss": 0.6,
            "validation_accuracy": 0.75,
            "precision": 0.7,
            "recall": 0.8,
            "f1_score": 0.75,
        }
        return trainer.train()


def assert_device_compatibility(device1, device2):
    """Assert that two devices are compatible (handle index differences)."""
    # Compare device types, ignoring index differences (e.g., mps:0 vs mps)
    assert device1.type == device2.type


def create_validation_losses_for_early_stopping(improving=True):
    """Create validation loss sequences for early stopping tests."""
    if improving:
        return [0.8, 0.6, 0.4, 0.2, 0.1]  # Continuous improvement

    return [0.5, 0.6, 0.7, 0.8, 0.9]  # No improvement after first epoch


def create_side_effects_for_validation(losses):
    """Create side effects for validation epoch mocking."""
    return [{"validation_loss": loss, "validation_accuracy": 0.5} for loss in losses]


def verify_callback_integration(
    mock_callback_class, mock_callback_instance, model_run_uuid, profile="development"
):
    """Verify that callback was properly integrated."""
    mock_callback_class.assert_called_once_with(
        model_run_uuid=model_run_uuid,
        profile=profile,
        verbose=True,
    )
    return mock_callback_instance


def verify_training_metrics_structure(metrics, expected_epochs):
    """Verify that training metrics have the expected structure."""
    assert len(metrics.storage.train.losses) == expected_epochs
    assert len(metrics.storage.test.losses) == expected_epochs
    assert len(metrics.storage.train.accuracies) == expected_epochs
    assert len(metrics.storage.test.accuracies) == expected_epochs

    # Verify timing metrics
    assert "start_time" in metrics.storage.timing
    assert "end_time" in metrics.storage.timing
    assert "total_training_time" in metrics.storage.timing
    assert len(metrics.storage.timing["epoch_times"]) == expected_epochs


def verify_database_callback_import_error_handling(mock_logger):
    """Verify that database callback import errors are handled correctly."""
    mock_logger.warning.assert_called_once()
    warning_call = mock_logger.warning.call_args[0][0]
    assert "Failed to import ModelRunCallback" in warning_call


def create_mock_logger_setup():
    """Create mock logger setup for testing."""
    mock_logger = MagicMock()
    return (
        patch("src.train.trainer.setup_training_logger", return_value=mock_logger),
        mock_logger,
    )


# Common test patterns
class CommonTestPatterns:
    """Common test patterns to reduce duplication."""

    @staticmethod
    def verify_basic_trainer_initialization(trainer, _, data_loaders):
        """Verify basic trainer initialization."""
        assert trainer.components.model is not None
        assert trainer.components.loss_fn is not None
        assert trainer.components.optimizer is not None
        assert trainer.data_loaders == data_loaders
        assert trainer.device is not None
        assert trainer.logger is not None

    @staticmethod
    def verify_metrics_collection(metrics, expected_epochs):
        """Verify metrics were collected correctly."""
        verify_training_metrics_structure(metrics, expected_epochs)
        assert len(metrics.batch_metrics) > 0

    @staticmethod
    def verify_artifacts_saved(output_dir):
        """Verify that training artifacts were saved."""

        output_path = Path(output_dir)
        assert (output_path / "model.pt").exists()
        assert (output_path / "metrics_history.json").exists()
        assert (output_path / "metrics_summary.json").exists()
