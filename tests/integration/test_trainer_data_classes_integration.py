"""
Integration tests for ModelTrainer with data classes from src.train.data_classes.

These tests focus on the integration between ModelTrainer and:
- TrainingComponents
- TrainingConfig
- TrainingMetrics

The tests verify that data classes work correctly with the trainer in realistic scenarios.
"""

import tempfile

import pytest
import torch
from torch import nn, optim
from torch.utils.data import DataLoader, TensorDataset

from tests.integration.trainer_test_utils import mock_training_and_run
from train.trainer import ModelTrainer, TrainerConfig


class SimpleModel(nn.Module):
    """A simple model for testing data class integration."""

    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(5, 1)

    def forward(self, x):
        """Forward pass of the model."""
        return self.linear(x)


@pytest.fixture(name="data_class_test_components")
def fixture_data_class_test_components():
    """Create components for data class integration testing."""
    torch.manual_seed(42)
    model = SimpleModel()
    loss_fn = nn.MSELoss()
    optimizer = optim.SGD(model.parameters(), lr=0.01)

    return {"model": model, "loss_fn": loss_fn, "optimizer": optimizer}


@pytest.fixture(name="data_class_test_loaders")
def fixture_data_class_test_loaders():
    """Create data loaders for data class integration testing."""
    torch.manual_seed(42)
    x_train = torch.randn(16, 5)
    y_train = torch.randn(16, 1)
    x_test = torch.randn(8, 5)
    y_test = torch.randn(8, 1)

    train_dataset = TensorDataset(x_train, y_train)  # pylint: disable=duplicate-code
    test_dataset = TensorDataset(x_test, y_test)

    train_loader = DataLoader(train_dataset, batch_size=4)
    test_loader = DataLoader(test_dataset, batch_size=4)

    return {"train": train_loader, "test": test_loader}


class TestTrainingComponentsIntegration:
    """Integration tests for TrainingComponents with ModelTrainer."""

    def test_training_components_creation_and_usage(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test that TrainingComponents integrates correctly with trainer."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_components",
                "model_run_uuid": "test-components-uuid",
                "run_output_dir": temp_dir,
                "epochs": 2,
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            # Verify TrainingComponents was created correctly
            # Accept either TrainingComponents or dict (depending on ModelTrainer implementation)
            assert (
                hasattr(trainer.components, "model")
                and hasattr(trainer.components, "loss_fn")
                and hasattr(trainer.components, "optimizer")
            )
            assert trainer.components.model == data_class_test_components["model"]
            assert trainer.components.loss_fn == data_class_test_components["loss_fn"]
            assert (
                trainer.components.optimizer == data_class_test_components["optimizer"]
            )

            # Verify components work during training with mocked methods for speed
            metrics = mock_training_and_run(trainer)

            assert metrics is not None
            assert len(metrics.storage.train.losses) == 2  # This test uses 2 epochs

    def test_training_components_device_placement(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test that TrainingComponents handles device placement correctly."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_device",
                "model_run_uuid": "test-device-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            # Verify model was moved to the correct device (handle device index differences)
            model_device = next(trainer.components.model.parameters()).device
            trainer_device = trainer.device
            assert model_device.type == trainer_device.type

            # Verify optimizer still works after device placement
            optimizer_param_device = trainer.components.optimizer.param_groups[0][
                "params"
            ][0].device
            assert optimizer_param_device.type == trainer_device.type


class TestTrainingConfigIntegration:
    """Integration tests for TrainingConfig with ModelTrainer."""

    def test_training_config_attribute_access(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test that TrainingConfig provides correct attribute access."""
        training_config_dict = {
            "model_id": "test_config",
            "model_run_uuid": "test-config-uuid",
            "epochs": 3,
            "gradient_clip_max_norm": 2.0,
            "custom_param": "test_value",
        }

        config = TrainerConfig(
            model_components=data_class_test_components,
            data_loaders=data_class_test_loaders,
            training_config=training_config_dict,
        )

        trainer = ModelTrainer(config)

        # Verify TrainingConfig was created correctly
        # Accept either TrainingConfig or dict (depending on ModelTrainer implementation)
        assert hasattr(trainer.config, "config") and hasattr(
            trainer.config, "__getattr__"
        )
        assert trainer.config.config == training_config_dict

        # Test attribute access through __getattr__
        assert trainer.config.model_id == "test_config"
        assert trainer.config.epochs == 3
        assert trainer.config.gradient_clip_max_norm == 2.0
        assert trainer.config.custom_param == "test_value"

        # Test accessing non-existent attribute
        with pytest.raises(AttributeError):
            _ = trainer.config.non_existent_attribute

    def test_training_config_learning_rate_schedule(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test TrainingConfig with learning rate schedule."""
        training_config_dict = {
            "model_id": "test_lr_schedule",
            "model_run_uuid": "test-lr-schedule-uuid",
            "epochs": 4,
            "learning_rate_schedule": [
                {"epoch": 1, "rate": 0.01},
                {"epoch": 3, "rate": 0.001},
            ],
        }

        config = TrainerConfig(
            model_components=data_class_test_components,
            data_loaders=data_class_test_loaders,
            training_config=training_config_dict,
        )

        trainer = ModelTrainer(config)

        # Verify learning rate schedule was processed correctly
        assert (
            trainer.config.learning_rate_schedule
            == training_config_dict["learning_rate_schedule"]
        )
        assert len(trainer.lr_epoch_map) == 2
        assert trainer.lr_epoch_map[1] == 0.01
        assert trainer.lr_epoch_map[3] == 0.001


class TestMetricsIntegration:
    """Integration tests for Metrics with ModelTrainer."""

    def test_metrics_collection_and_access(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test that Metrics collects and provides access to metrics correctly."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_metrics_access",
                "model_run_uuid": "test-metrics-access-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            # Simulate training
            trainer.add_custom_metric("custom1", 123)
            trainer.metrics.storage.timing["epoch_time"] = [1.23]

            # Access metrics
            assert trainer.metrics.get("custom1") == 123
            assert trainer.metrics.get("epoch_time") == [1.23]

    def test_metrics_get_method(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test Metrics.get() method integration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_get_method",
                "model_run_uuid": "test-get-method-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            # Add test data
            trainer.add_custom_metric("custom_test", 42)
            trainer.metrics.storage.timing["test_timing"] = [123.45]

            # Test get method
            assert trainer.metrics.get("custom_test") == 42
            assert trainer.metrics.get("test_timing") == [123.45]

    def test_metrics_summary_integration(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test Metrics summary generation integration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_metrics_summary",
                "model_run_uuid": "test-metrics-summary-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            # Add test data
            trainer.add_custom_metric("summary_test", 99)
            trainer.metrics.storage.resources["gpu"] = [0.5]
            # Manually add some train/test losses for summary testing
            trainer.metrics.storage.train.losses.append(0.123)
            trainer.metrics.storage.test.losses.append(0.456)

            summary = trainer.metrics.get_metric_summary()  # Regenerate summary

            # Check standard parts of summary
            assert "timing" in summary
            assert "resources" in summary

            # Check specific resource metric that was set
            assert (
                "gpu_last" in summary["resources"]
            ), "'gpu_last' key missing from resources in summary"
            assert (
                summary["resources"]["gpu_last"] == 0.5
            ), "GPU last value metric mismatch"

            # Check that the summary contains the correct final train/test loss values
            # under the 'final' key
            assert "final" in summary, "'final' key missing from summary"
            assert (
                "train_loss" in summary["final"]
            ), "train_loss should be in summary['final']"
            assert (
                summary["final"]["train_loss"] == 0.123
            ), f"Expected final train_loss 0.123, got {summary['final'].get('train_loss')}"

            assert (
                "test_loss" in summary["final"]
            ), "test_loss should be in summary['final']"
            assert (
                summary["final"]["test_loss"] == 0.456
            ), f"Expected final test_loss 0.456, got {summary['final'].get('test_loss')}"
