"""
Tests for the model factory and base service implementation.
"""

import unittest
from typing import Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, Field

from database.services.base_service import BaseService
from database.utils.model_factory import ModelFactory, ModelValidationError


# Define test models
class SampleModel(BaseModel):
    """Sample model for unit tests."""

    uuid: str | UUID = Field(..., description="Unique identifier")
    name: str = Field(..., description="Name of the test model")
    value: int = Field(..., description="Value of the test model")
    optional_field: Optional[str] = Field(None, description="Optional field")


class SampleService(BaseService[SampleModel]):
    """Sample service for unit tests."""

    model_class = SampleModel
    required_fields = ["uuid", "name", "value"]


class ModelFactoryTests(unittest.TestCase):
    """Test cases for the ModelFactory class."""

    def setUp(self):
        """Set up test fixtures."""
        self.valid_data = {
            "uuid": str(uuid4()),
            "name": "Test Model",
            "value": 42,
            "optional_field": "Optional Value",
        }

        self.invalid_data = {
            "uuid": str(uuid4()),
            "name": "Test Model",
            # Missing required 'value' field
        }

    def test_validate_data_success(self):
        """Test successful data validation."""
        # Should not raise an exception
        ModelFactory.validate_data(self.valid_data, ["uuid", "name", "value"])

    def test_validate_data_missing_field(self):
        """Test validation with missing required field."""
        with self.assertRaises(ModelValidationError):
            ModelFactory.validate_data(self.invalid_data, ["uuid", "name", "value"])

    def test_validate_data_with_custom_validator(self):
        """Test validation with custom field validator."""

        # Define a validator that requires value to be > 50
        # The validator needs to raise an exception when validation fails
        def value_validator(v):
            if v <= 50:
                raise ValueError("Value must be greater than 50")
            return v

        validators = {"value": value_validator}

        # Create a copy of the data to avoid modifying the original
        test_data = self.valid_data.copy()

        # Should raise an exception because value is 42
        with self.assertRaises(ModelValidationError):
            ModelFactory.validate_data(
                test_data, ["uuid", "name", "value"], field_validators=validators
            )

        # Fix the data and try again
        test_data["value"] = 51
        # Should not raise an exception now
        ModelFactory.validate_data(
            test_data, ["uuid", "name", "value"], field_validators=validators
        )

    def test_create_model_success(self):
        """Test successful model creation."""
        model = ModelFactory.create_model(SampleModel, self.valid_data)
        self.assertIsInstance(model, SampleModel)
        self.assertEqual(model.name, "Test Model")
        self.assertEqual(model.value, 42)  # Original value
        self.assertEqual(model.optional_field, "Optional Value")

    def test_create_model_invalid_data(self):
        """Test model creation with invalid data."""
        with self.assertRaises(ModelValidationError):
            ModelFactory.create_model(SampleModel, self.invalid_data)

    def test_create_model_partial(self):
        """Test partial model creation."""

        # For this test, we need a model with an optional value field
        class PartialTestModel(BaseModel):
            """Test model for partial creation."""

            uuid: str | UUID = Field(..., description="Unique identifier")
            name: str = Field(..., description="Name of the test model")
            value: Optional[int] = Field(None, description="Optional value field")

        partial_data = {
            "uuid": str(uuid4()),
            "name": "Partial Model",
            # Missing 'value' field, but it's optional in this model
        }

        # Should not raise an exception
        model = ModelFactory.create_model(PartialTestModel, partial_data)
        self.assertIsInstance(model, PartialTestModel)
        self.assertEqual(model.name, "Partial Model")
        self.assertIsNone(model.value)  # Value should be None

    def test_create_models_batch(self):
        """Test batch model creation."""
        batch_data = [
            self.valid_data,
            {"uuid": str(uuid4()), "name": "Another Model", "value": 100},
        ]

        models = ModelFactory.create_models_from_list(SampleModel, batch_data)
        self.assertEqual(len(models), 2)
        self.assertIsInstance(models[0], SampleModel)
        self.assertIsInstance(models[1], SampleModel)
        self.assertEqual(models[1].name, "Another Model")

    def test_create_models_batch_with_errors(self):
        """Test batch model creation with some invalid data."""
        batch_data = [
            self.valid_data,
            self.invalid_data,  # This one should fail
            {"uuid": str(uuid4()), "name": "Another Model", "value": 100},
        ]

        # Should raise an exception by default
        with self.assertRaises(ModelValidationError):
            ModelFactory.create_models_from_list(SampleModel, batch_data)

        # With skip_invalid=True, should return only valid models
        models = ModelFactory.create_models_from_list(
            SampleModel, batch_data, skip_invalid=True
        )
        self.assertEqual(len(models), 2)  # Only 2 out of 3 are valid


class BaseServiceTests(unittest.TestCase):
    """Test cases for the BaseService class."""

    def setUp(self):
        """Set up test fixtures."""
        self.valid_data = {
            "uuid": str(uuid4()),
            "name": "Test Model",
            "value": 42,
            "optional_field": "Optional Value",
        }

    def test_validate_data(self):
        """Test the validate_data method."""
        # Should not raise an exception
        SampleService.validate_data(self.valid_data)

        # Test with invalid data
        invalid_data = {
            "uuid": str(uuid4()),
            "name": "Test Model",
            # Missing required 'value' field
        }

        with self.assertRaises(ModelValidationError):
            SampleService.validate_data(invalid_data)

    def test_create_model(self):
        """Test the create_model method."""
        model = SampleService.create_model(self.valid_data)
        self.assertIsInstance(model, SampleModel)
        self.assertEqual(model.name, "Test Model")
        self.assertEqual(model.value, 42)

    def test_create_model_with_different_class(self):
        """Test creating a model with a different class than the service's default."""

        class AnotherModel(BaseModel):
            """Another test model."""

            uuid: str
            name: str

        model = SampleService.create_model(
            {"uuid": "123", "name": "Another"}, model_class=AnotherModel
        )
        self.assertIsInstance(model, AnotherModel)
        self.assertEqual(model.name, "Another")


if __name__ == "__main__":
    unittest.main()
