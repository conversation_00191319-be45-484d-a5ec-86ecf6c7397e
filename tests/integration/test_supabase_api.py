"""
Integration tests for Supabase functionality.
"""

from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import Test<PERSON>lient

from api.main import app
from tests.fixtures.data import get_sample_dataset_list


@pytest.mark.asyncio
async def test_debug_endpoint_development():
    """Test the debug endpoint with development profile."""
    client = TestClient(app)
    response = client.get(
        "/api/v1/datasets/debug", headers={"X-Supabase-Profile": "development"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["profile"] == "development"


@pytest.mark.asyncio
async def test_debug_endpoint_staging():
    """Test the debug endpoint with staging profile."""
    client = TestClient(app)
    response = client.get(
        "/api/v1/datasets/debug", headers={"X-Supabase-Profile": "staging"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["profile"] == "staging"


@pytest.mark.asyncio
async def test_debug_endpoint_production():
    """Test the debug endpoint with production profile."""
    client = TestClient(app)
    response = client.get(
        "/api/v1/datasets/debug", headers={"X-Supabase-Profile": "production"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["profile"] == "production"


@pytest.mark.asyncio
async def test_fetch_data_integration():
    """Test the integration of fetch_data with the API."""

    # Create a mock for fetch_data
    mock_fetch_data = MagicMock()
    mock_fetch_data.return_value = get_sample_dataset_list()

    # Mock the fetch_data function at the correct import path
    with patch("api.routes.datasets.fetch_data", mock_fetch_data):
        client = TestClient(app)
        response = client.get("/api/v1/datasets")

        # Verify the response
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0

        # Verify the data structure
        assert data[0]["uuid"] is not None
        assert data[0]["name"] is not None

        # Verify the mock was called
        mock_fetch_data.assert_called_once_with("datasets", profile="development")
