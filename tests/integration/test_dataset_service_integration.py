"""
Integration tests for the DatasetService class.

These tests use fewer mocks and test the integration between components.
"""

from unittest.mock import patch

import pytest

from database.models.dataset_set import SetType
from database.services.dataset_service import DatasetService
from tests.fixtures.data import (
    TEST_DATASET_UUID,
    get_sample_dataset_data,
    get_sample_dataset_sets,
)

# Mark this module to use the auto_mock system with the database category
pytest.mark.auto_mock("database")


class TestDatasetServiceIntegration:
    """Integration tests for DatasetService that focus on component interactions."""

    @pytest.mark.parametrize(
        "dataset_name,images_count",
        [
            ("Test Dataset 1", 100),
            ("Test Dataset 2", 200),
        ],
    )
    def test_get_dataset_integration(self, dataset_name, images_count):
        """
        Test dataset retrieval with integration between fetch_data and DatasetService.

        This test mocks only the database layer and tests the actual service logic.
        """
        # Create sample dataset data
        dataset_uuid = TEST_DATASET_UUID
        sample_data = get_sample_dataset_data(
            custom_values={
                "uuid": dataset_uuid,
                "name": dataset_name,
                "images_count": images_count,
                "created_at": "2023-01-01T00:00:00Z",
                "content_updated_at": "2023-01-01T00:00:00Z",
            }
        )

        # Mock fetch_data directly instead of the Supabase client
        with patch("database.services.dataset_service.fetch_data") as mock_fetch_data:
            # Configure the mock to return our sample data
            mock_fetch_data.return_value = [sample_data]

            # Call the service method
            result = DatasetService.get_dataset(dataset_uuid)

            # Verify the result contains the expected data
            assert result["uuid"] == dataset_uuid
            assert result["name"] == dataset_name
            assert result["images_count"] == images_count

    def test_get_dataset_sets_integration(self):
        """
        Test dataset sets retrieval with integration between fetch_data and DatasetService.

        This test mocks only the database layer and tests the actual service logic.
        """
        # Create sample dataset sets
        dataset_sets = get_sample_dataset_sets(
            count=5,
            custom_values_list=[
                {
                    "dataset_uuid": TEST_DATASET_UUID,
                    "created_at": "2023-01-01T00:00:00Z",
                }
                for _ in range(5)
            ],
        )

        # Mock fetch_data directly instead of the Supabase client
        with patch("database.services.dataset_service.fetch_data") as mock_fetch_data:
            # First call returns count
            # Second call returns dataset sets
            mock_fetch_data.side_effect = [
                [{"count": len(dataset_sets)}],  # First call returns count
                dataset_sets,  # Second call returns data
            ]

            # Call the service method
            dataset_sets_generator = DatasetService.get_dataset_sets(
                TEST_DATASET_UUID, batch_size=10
            )

            # Consume the generator
            all_batches = list(dataset_sets_generator)
            all_sets = [item for batch in all_batches for item in batch]

            # Verify the results
            assert len(all_sets) == len(dataset_sets)
            assert all(str(ds.dataset_uuid) == TEST_DATASET_UUID for ds in all_sets)

    @pytest.mark.parametrize(
        "set_type,expected_count",
        [
            (SetType.TRAIN, 2),
            (SetType.VALIDATION, 1),
            (SetType.TEST, 1),
        ],
    )
    def test_get_dataset_sets_with_type_filter_integration(
        self, set_type, expected_count
    ):
        """
        Test dataset sets retrieval with type filter using integration testing.

        This test verifies that filtering by set_type works correctly.
        """
        # Create sample dataset sets with different types
        # Create base dataset sets
        base_dataset_sets = [
            # Training sets
            {
                "uuid": "set-1",
                "dataset_uuid": TEST_DATASET_UUID,
                "set_type": SetType.TRAIN,
                "created_at": "2023-01-01T00:00:00Z",
                "image_uuid": "img-1",
                "coin_side_uuid": "side-1",
                "images_reviews": {"image_url": "http://example.com/image1.jpg"},
            },
            {
                "uuid": "set-2",
                "dataset_uuid": TEST_DATASET_UUID,
                "set_type": SetType.TRAIN,
                "created_at": "2023-01-02T00:00:00Z",
                "image_uuid": "img-2",
                "coin_side_uuid": "side-2",
                "images_reviews": {"image_url": "http://example.com/image2.jpg"},
            },
            # Validation set
            {
                "uuid": "set-3",
                "dataset_uuid": TEST_DATASET_UUID,
                "set_type": SetType.VALIDATION,
                "created_at": "2023-01-03T00:00:00Z",
                "image_uuid": "img-3",
                "coin_side_uuid": "side-3",
                "images_reviews": {"image_url": "http://example.com/image3.jpg"},
            },
            # Test set
            {
                "uuid": "set-4",
                "dataset_uuid": TEST_DATASET_UUID,
                "set_type": SetType.TEST,
                "created_at": "2023-01-04T00:00:00Z",
                "image_uuid": "img-4",
                "coin_side_uuid": "side-4",
                "images_reviews": {"image_url": "http://example.com/image4.jpg"},
            },
        ]

        # Convert to proper dataset sets
        dataset_sets = base_dataset_sets

        # Filter the sets based on the test parameter
        filtered_sets = [s for s in dataset_sets if s["set_type"] == set_type]

        # Mock fetch_data directly
        with patch("database.services.dataset_service.fetch_data") as mock_fetch_data:
            # Configure mock to return count and then data
            mock_fetch_data.side_effect = [
                [{"count": len(filtered_sets)}],  # First call returns count
                filtered_sets,  # Second call returns filtered data
            ]

            # Call the service method with the filter
            dataset_sets_generator = DatasetService.get_dataset_sets(
                TEST_DATASET_UUID, set_type=set_type, batch_size=10
            )

            # Consume the generator
            all_batches = list(dataset_sets_generator)
            all_sets = [item for batch in all_batches for item in batch]

            # Verify the results
            assert len(all_sets) == expected_count
            assert all(ds.set_type == set_type for ds in all_sets)
