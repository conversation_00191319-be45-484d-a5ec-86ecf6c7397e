"""
API-related test fixtures and configuration.

This module provides fixtures for FastAPI application testing,
environment variables, and Supabase profile management.
"""

import sys
from importlib import reload
from unittest.mock import MagicMock, patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from api.main import app

# Import this at the top level to avoid pylint import-outside-toplevel warnings
try:
    from api.config import settings as api_settings
except ImportError:
    # Mock if not available
    api_settings = MagicMock()


@pytest.fixture
def api_test_app():
    """Create a test FastAPI app for API tests."""
    test_app = FastAPI()

    @test_app.get("/test")
    def test_endpoint():
        return {"status": "ok"}

    return test_app


@pytest.fixture
def api_test_client():
    """Create a TestClient for the main app."""
    return TestClient(app)


@pytest.fixture(name="test_client")
def fixture_test_client() -> TestClient:
    """
    Create a test client for the FastAPI application with mocked dependencies.

    Returns:
        TestClient: A test client for the FastAPI application.
    """
    # Create a test client with the app
    client = TestClient(app)
    return client


@pytest.fixture
def mock_supabase_profile_middleware():
    """Mock the SupabaseProfileMiddleware."""
    with patch("api.middleware.supabase_profile.SupabaseProfileMiddleware") as mock:
        yield mock


@pytest.fixture
def set_supabase_profile():
    """Fixture to temporarily set the Supabase profile.

    This fixture provides a context manager that temporarily changes the
    Supabase profile and restores it after the test completes.

    Returns:
        A function that takes a profile name and yields within a context
        where that profile is active.
    """

    def _set_profile(profile):
        # Store the original profile to restore it later
        original_profile = api_settings.SUPABASE_PROFILE
        # Update to the new profile
        api_settings.model_copy(update={"SUPABASE_PROFILE": profile})
        yield
        # Restore the original profile
        api_settings.model_copy(update={"SUPABASE_PROFILE": original_profile})

    return _set_profile


@pytest.fixture(autouse=True)
def mock_env_vars():
    """Mock environment variables for all tests.

    This fixture is automatically applied to all tests and provides consistent
    environment variables for Supabase credentials across all test modules.

    The values are standardized for testing:
    - Development: https://dev.supabase.co with key 'dev-key'
    - Staging: https://staging.supabase.co with key 'staging-key'
    - Production: https://prod.supabase.co with key 'prod-key'

    Yields:
        None: This fixture uses a context manager to temporarily modify
        environment variables during test execution.
    """
    env_vars = {
        "COINY_CLASSIFIER_SUPABASE_DEV_URL": "https://dev.supabase.co",
        "COINY_CLASSIFIER_SUPABASE_DEV_ANON_KEY": "dev-key",
        "COINY_CLASSIFIER_SUPABASE_STAGING_URL": "https://staging.supabase.co",
        "COINY_CLASSIFIER_SUPABASE_STAGING_ANON_KEY": "staging-key",
        "COINY_CLASSIFIER_SUPABASE_URL": "https://prod.supabase.co",
        "COINY_CLASSIFIER_SUPABASE_ANON_KEY": "prod-key",
    }

    with patch.dict("os.environ", env_vars, clear=True):
        # Only reload the config if it's not a mock
        if "api" in sys.modules and hasattr(sys.modules["api"], "config"):
            config = sys.modules["api"].config
            # Check if it's not a mock
            if not hasattr(config, "__class__") or "Mock" not in str(config.__class__):
                try:
                    # Use the already imported reload function
                    reload(config)
                except (ImportError, TypeError):
                    # If we can't reload, just continue
                    pass
        yield
