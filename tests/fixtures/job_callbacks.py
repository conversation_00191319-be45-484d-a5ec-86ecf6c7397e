"""
Fixtures for job callback testing.

This module contains fixtures for testing job callbacks and their integration
with the job dispatcher system.
"""

import logging
import time
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest

from src.common.callbacks.job import JobCallback
from src.database.services.model_run_service import ModelRunService
from src.jobs.base import Job, JobStatus
from src.jobs.callbacks.database import DatabaseUpdateCallback
from src.jobs.callbacks.resource_monitor import (
    ResourceMonitorCallback,
    ResourceMonitorConfig,
)
from src.jobs.training_data import TrainingJob, TrainingJobData
from src.jobs.training_dispatcher import TrainingJobDispatcher

logger = logging.getLogger(__name__)


# --- Mock Classes ---


def create_mock_job_dispatcher(callbacks=None):
    """
    Create a mock JobDispatcher using autospec for better alignment with real implementation.

    This approach uses the real TrainingJobDispatcher as a spec to ensure the mock
    has the same interface as the real implementation, preventing misalignment issues.
    """
    # Create a mock with autospec based on TrainingJobDispatcher
    mock_dispatcher = MagicMock(spec=TrainingJobDispatcher)

    # Initialize the mock with the same attributes as the real dispatcher
    mock_dispatcher.jobs = {}
    mock_dispatcher._running = False  # pylint: disable=protected-access
    mock_dispatcher.callback_handler = MagicMock()

    # Set up the callback handler to actually call callbacks for testing
    if callbacks:
        mock_dispatcher.callback_handler.call_method = MagicMock(
            side_effect=lambda method_name, *args, **kwargs: [
                getattr(callback, method_name)(*args, **kwargs)
                for callback in callbacks
                if hasattr(callback, method_name)
            ]
        )

    # Configure the mock methods to behave like a real dispatcher
    async def mock_start():
        mock_dispatcher._running = True  # pylint: disable=protected-access
        if hasattr(mock_dispatcher.callback_handler, "call_method"):
            mock_dispatcher.callback_handler.call_method("on_dispatcher_start")

    async def mock_stop():
        mock_dispatcher._running = False  # pylint: disable=protected-access
        if hasattr(mock_dispatcher.callback_handler, "call_method"):
            mock_dispatcher.callback_handler.call_method("on_dispatcher_stop")

    async def mock_schedule_job(job):
        mock_dispatcher.jobs[job.job_id] = job
        if hasattr(mock_dispatcher.callback_handler, "call_method"):
            mock_dispatcher.callback_handler.call_method("on_job_scheduled", job)
        return job

    async def mock_cancel_job(job_id):
        if job_id in mock_dispatcher.jobs:
            job = mock_dispatcher.jobs[job_id]
            job.status = JobStatus.CANCELLED
            del mock_dispatcher.jobs[job_id]
            if hasattr(mock_dispatcher.callback_handler, "call_method"):
                mock_dispatcher.callback_handler.call_method("on_job_cancelled", job)
            return True
        return False

    async def mock_get_job_status(job_id):
        if job_id in mock_dispatcher.jobs:
            return mock_dispatcher.jobs[job_id].status
        return None

    def mock_notify_callbacks(method_name, *args, **kwargs):
        if hasattr(mock_dispatcher.callback_handler, "call_method"):
            mock_dispatcher.callback_handler.call_method(method_name, *args, **kwargs)

    # Assign the mock methods
    mock_dispatcher.start = mock_start
    mock_dispatcher.stop = mock_stop
    mock_dispatcher.schedule_job = mock_schedule_job
    mock_dispatcher.cancel_job = mock_cancel_job
    mock_dispatcher.get_job_status = mock_get_job_status
    # pylint: disable-next=protected-access
    mock_dispatcher._notify_callbacks = mock_notify_callbacks

    return mock_dispatcher


class CustomTestCallback(JobCallback):
    """Custom JobCallback implementation for testing."""

    def __init__(self):
        """Initialize the callback and its counters."""
        super().__init__()
        self.last_job = None
        self.last_error = None
        self.reset_counters()

    def reset_counters(self):
        """Reset all counters to initial state."""
        self.start_count = 0
        self.stop_count = 0
        self.scheduled_count = 0
        self.start_job_count = 0
        self.complete_count = 0
        self.failed_count = 0
        self.cancelled_count = 0

    def on_dispatcher_start(self, logs=None):  # pylint: disable=unused-argument
        """Called when dispatcher starts."""
        self.start_count += 1

    def on_dispatcher_stop(self, logs=None):  # pylint: disable=unused-argument
        """Called when dispatcher stops."""
        self.stop_count += 1

    def on_job_scheduled(self, job, logs=None):  # pylint: disable=unused-argument
        """Called when a job is scheduled."""
        self.scheduled_count += 1
        self.last_job = job

    def on_job_start(self, job, logs=None):  # pylint: disable=unused-argument
        """Called when a job starts."""
        self.start_job_count += 1
        self.last_job = job

    def on_job_complete(self, job, logs=None):  # pylint: disable=unused-argument
        """Called when a job completes."""
        self.complete_count += 1
        self.last_job = job

    def on_job_failed(self, job, error, logs=None):  # pylint: disable=unused-argument
        """Called when a job fails."""
        self.failed_count += 1
        self.last_job = job
        self.last_error = error

    def on_job_cancelled(self, job, logs=None):  # pylint: disable=unused-argument
        """Called when a job is cancelled."""
        self.cancelled_count += 1
        self.last_job = job


# --- Basic Job Fixtures ---


@pytest.fixture(name="mock_job_dispatcher_factory")
def fixture_mock_job_dispatcher_factory():
    """Fixture for creating mock JobDispatcher instances."""
    return create_mock_job_dispatcher


@pytest.fixture(name="job")
def fixture_job():
    """Fixture for a standard Job instance."""
    # Patch asyncio.get_event_loop().time() to avoid the "There is no current event loop" error
    with patch("asyncio.get_event_loop") as mock_get_loop:
        mock_loop = MagicMock()
        mock_loop.time.return_value = time.time()
        mock_get_loop.return_value = mock_loop
        return Job(job_id=str(uuid4()), data={"test": "data"})


@pytest.fixture(name="job_factory")
def fixture_job_factory():
    """Factory fixture to create multiple job instances."""

    def _factory(job_id: str):
        # Patch asyncio.get_event_loop().time() to avoid the "There is no current event loop" error
        with patch("asyncio.get_event_loop") as mock_get_loop:
            mock_loop = MagicMock()
            mock_loop.time.return_value = time.time()
            mock_get_loop.return_value = mock_loop
            return Job(job_id=job_id, data={"test": "data"})

    return _factory


@pytest.fixture(name="training_job")
def fixture_training_job():
    """Fixture for a TrainingJob instance."""
    # Patch asyncio.get_event_loop().time() to avoid the "There is no current event loop" error
    with patch("asyncio.get_event_loop") as mock_get_loop:
        mock_loop = MagicMock()
        mock_loop.time.return_value = time.time()
        mock_get_loop.return_value = mock_loop

        model_run_uuid = str(uuid4())
        training_job_data = TrainingJobData(
            model_run_uuid=model_run_uuid,
            training_config={
                "model_name": "test_model",
                "hyperparameters": {"lr": 0.01},
                "dataset_id": "test_dataset",
            },
        )
        return TrainingJob(job_id=str(uuid4()), data=training_job_data)


@pytest.fixture(name="db_training_job")
def fixture_db_training_job():
    """Fixture for a TrainingJob instance specific to database tests."""
    # Patch asyncio.get_event_loop().time() to avoid the "There is no current event loop" error
    with patch("asyncio.get_event_loop") as mock_get_loop:
        mock_loop = MagicMock()
        mock_loop.time.return_value = time.time()
        mock_get_loop.return_value = mock_loop

        model_run_uuid = str(uuid4())
        job_id = str(uuid4())
        job_data = TrainingJobData(
            model_run_uuid=model_run_uuid,
            model_components={"name": "db_test_model"},
            training_config={
                "hyperparameters": {"lr": 0.01},
                "dataset_id": "db_test_dataset",
            },
        )
        return TrainingJob(job_id=job_id, data=job_data)


# --- Callback Mock Fixtures ---


@pytest.fixture(name="database_callback_mock")
def fixture_database_callback_mock():
    """Fixture for a mocked DatabaseUpdateCallback."""
    return MagicMock(spec=DatabaseUpdateCallback)


@pytest.fixture(name="resource_callback_mock")
def fixture_resource_callback_mock():
    """Fixture for a mocked ResourceMonitorCallback."""
    return MagicMock(spec=ResourceMonitorCallback)


@pytest.fixture(name="custom_test_callback")
def fixture_custom_test_callback():
    """Fixture for a CustomTestCallback instance."""
    return CustomTestCallback()


# --- Dispatcher Fixtures ---


@pytest.fixture(name="dispatcher")
def fixture_dispatcher(
    mock_job_dispatcher_factory, database_callback_mock, resource_callback_mock
):
    """Fixture for a mock JobDispatcher instance with mock callbacks."""
    return mock_job_dispatcher_factory(
        callbacks=[database_callback_mock, resource_callback_mock]
    )


# --- Database Service Fixtures ---


@pytest.fixture(name="mock_model_run_service")
def fixture_mock_model_run_service():
    """Fixture for a mocked ModelRunService."""
    with patch("src.jobs.callbacks.database.ModelRunService") as mock_constructor:
        mock_instance = MagicMock(spec=ModelRunService)
        mock_constructor.return_value = mock_instance
        yield mock_instance, mock_constructor


@pytest.fixture(name="database_update_callback")
def fixture_database_update_callback(mock_model_run_service):
    """Fixture for a DatabaseUpdateCallback instance."""
    # The mock_model_run_service fixture already patches the service
    _ = mock_model_run_service  # Ensure fixture is used
    with patch("src.jobs.callbacks.database.maybe_await", lambda x: x):
        yield DatabaseUpdateCallback()


# --- Resource Monitor Fixtures ---


@pytest.fixture(name="resource_monitor_callback_with_mocks")
def fixture_resource_monitor_callback_with_mocks():
    """Fixture for ResourceMonitorCallback with patched dependencies."""
    config = ResourceMonitorConfig(monitoring_interval=0.1)
    callback = ResourceMonitorCallback(config)
    with patch(
        "src.jobs.callbacks.resource_monitor.psutil.cpu_percent", return_value=50.0
    ), patch(
        "src.jobs.callbacks.resource_monitor.psutil.virtual_memory",
        return_value=MagicMock(percent=60.0),
    ), patch(
        "src.jobs.callbacks.resource_monitor.torch.cuda.is_available",
        return_value=True,
    ), patch(
        "src.jobs.callbacks.resource_monitor.torch.cuda.device_count", return_value=1
    ), patch(
        "src.jobs.callbacks.resource_monitor.torch.cuda.memory_allocated",
        return_value=1 * 1024**3,  # 1 GB
    ), patch(
        "src.jobs.callbacks.resource_monitor.torch.cuda.memory_reserved",
        return_value=4 * 1024**3,  # 4 GB
    ):
        yield callback

    # Teardown: The _stop_monitoring method (called by on_job_complete/failed)
    # handles task cancellation. No explicit cancellation needed here.
