# Test Fixtures

This directory contains centralized fixtures for tests. These fixtures are automatically imported and available to all test modules.

## Purpose

The purpose of centralizing fixtures is to:

1. **Reduce duplication**: Common fixtures are defined once and reused across multiple test modules.
2. **Ensure consistency**: All tests use the same data structures and mock objects.
3. **Simplify maintenance**: Changes to fixtures only need to be made in one place.
4. **Improve readability**: Test modules are cleaner and more focused on the actual tests.

## Available Fixtures

### API Fixtures (`api.py`)

API fixtures provide FastAPI testing utilities and environment configuration:

- `api_test_app`: A test FastAPI app for API tests.
- `api_test_client`: A TestClient for the main app.
- `test_client`: A test client for the FastAPI application with mocked dependencies.
- `mock_supabase_profile_middleware`: Mock the SupabaseProfileMiddleware.
- `set_supabase_profile`: Fixture to temporarily set the Supabase profile.
- `mock_env_vars`: Mock environment variables for all tests (autouse).

### Data Fixtures (`data.py`)

Data fixtures provide common data structures used across different test modules:

- `sample_dataset_data`: A dictionary representing a single dataset.
- `sample_dataset_list`: A list of dictionaries representing multiple datasets.
- `sample_image_batch`: A tensor representing a batch of images for model testing.
- `sample_labels`: A tensor representing labels for model testing.

### Model Fixtures (`model.py`)

Model fixtures provide PyTorch models and related testing utilities:

- `sample_image_batch`: A tensor representing a batch of images for model testing.
- `sample_labels`: A tensor representing labels for model testing.
- `mock_model_weights`: A dictionary representing model weights for testing.
- `mock_model_factory`: Session-scoped fixture providing lightweight mock models for fast testing.

### Mock Fixtures (`mocks.py`)

Mock fixtures provide common mock objects used across different test modules:

- `mock_supabase_client`: A mock Supabase client that returns an empty list.
- `mock_supabase_create_client`: A mock for the create_client function.
- `mock_supabase_client_with_dataset`: A mock Supabase client that returns a single dataset.
- `mock_supabase_client_with_datasets`: A mock Supabase client that returns multiple datasets.
- `mock_database_response`: A mock for the fetch_data function that returns a single dataset.
- `mock_database_responses`: A mock for the fetch_data function that returns multiple datasets.

### Async Fixtures (`async_utils.py`)

Async fixtures provide async versions of mock objects for testing async code:

- `async_mock_supabase_client`: An async mock Supabase client that returns an empty list.
- `async_mock_supabase_client_with_dataset`: An async mock Supabase client that returns a single dataset.
- `async_mock_supabase_client_with_datasets`: An async mock Supabase client that returns multiple datasets.
- `async_mock_database_response`: An async mock for the fetch_data function that returns a single dataset.
- `async_mock_database_responses`: An async mock for the fetch_data function that returns multiple datasets.

### Path Fixtures (`paths.py`)

Path fixtures provide directory isolation and cleanup for tests:

- `test_runs_dir`: Session-scoped temporary directory for test runs.
- `test_datasets_dir`: Session-scoped temporary directory for test datasets.
- `isolate_runs_directory`: Automatically isolate all tests to use a temporary runs directory (autouse).
- `isolate_datasets_directory`: Automatically isolate all tests to use a temporary datasets directory (autouse).
- `cleanup_test_runs`: Fixture to ensure test run directories are cleaned up after individual tests.

### Performance Fixtures (`performance.py`)

Performance fixtures provide optimization for test execution:

- `warm_up_heavy_libraries`: Session-scoped fixture to pre-load heavy libraries and keep them warm (autouse).

### Database Fixtures (`database.py`)

Database fixtures provide database testing utilities and mocks.

### Trainer Fixtures (`trainer.py`)

Trainer fixtures provide training-related testing utilities and mocks.

## Usage

These fixtures are automatically imported and available to all test modules. You can use them directly in your tests:

### Synchronous Tests

```python
def test_something(sample_dataset_data):
    # Use sample_dataset_data in your test
    assert sample_dataset_data["name"] == "Test Dataset"

def test_with_mock_client(mock_supabase_client):
    # Use mock_supabase_client in your test
    result = mock_supabase_client.table("datasets").select("*").execute()
    assert result.data == []
```

### Asynchronous Tests

```python
import pytest

@pytest.mark.asyncio
async def test_async_function(async_mock_database_response):
    # Use async_mock_database_response in your test
    from unittest.mock import patch

    with patch("database.supabase_client.fetch_data", async_mock_database_response):
        # Call your async function
        result = await your_async_function()
        assert result is not None

@pytest.mark.asyncio
async def test_with_async_client(async_mock_supabase_client):
    # Use async_mock_supabase_client in your test
    result = await async_mock_supabase_client.table("datasets").select("*").execute()
    assert result.data == []
```

## Adding New Fixtures

When adding new fixtures:

1. Determine if the fixture is specific to a component or can be used across multiple components.
2. If it's specific to a component, add it to the component's `conftest.py` file.
3. If it can be used across multiple components, add it to the appropriate file in this directory.
4. Add comprehensive documentation to explain the purpose and usage of the fixture.
5. Follow the naming conventions established in this directory.
