"""
Performance optimization fixtures for tests.

This module provides fixtures for optimizing test performance by
pre-loading heavy libraries and managing resources.
"""

import time

import pytest


@pytest.fixture(scope="session", autouse=True)
def warm_up_heavy_libraries():
    """
    Session-scoped fixture to pre-load heavy libraries and keep them warm.

    This fixture runs once at the start of the test session and pre-imports
    heavy libraries like PyTorch, PIL, numpy, etc. to reduce import overhead
    in individual tests.
    """
    start_time = time.time()
    heavy_imports = []

    # Import and initialize PyTorch
    torch = _import_and_init_torch(heavy_imports)

    # Import other heavy libraries
    _import_heavy_library("numpy", heavy_imports)
    _import_heavy_library("PIL.Image", heavy_imports, "PIL")
    _import_heavy_library("torchvision", heavy_imports)
    _import_heavy_library("psutil", heavy_imports)
    _import_heavy_library("asyncio", heavy_imports)

    end_time = time.time()
    warmup_time = end_time - start_time

    print(
        f"\n🔥 Heavy libraries warmed up in {warmup_time:.3f}s: {', '.join(heavy_imports)}"
    )

    yield

    # Cleanup after all tests
    print("\n🧹 Cleaning up heavy library resources...")
    _cleanup_torch_cache(torch)


def _import_and_init_torch(heavy_imports):
    """Import and initialize PyTorch with common objects."""
    try:
        # pylint: disable=import-outside-toplevel
        import torch
        from torch import nn

        heavy_imports.extend(["torch", "torch.nn"])

        # Pre-create common objects to keep them in memory
        _ = torch.randn(1, 1)  # Initialize CUDA/MPS if available
        _ = nn.BCEWithLogitsLoss()  # Initialize common loss functions
        _ = nn.CrossEntropyLoss()
        _ = nn.MSELoss()

        return torch
    except ImportError:
        return None


def _import_heavy_library(module_name, heavy_imports, import_name=None):
    """Import a heavy library and add to imports list."""
    try:
        __import__(module_name)
        heavy_imports.append(import_name or module_name)
    except ImportError:
        pass


def _cleanup_torch_cache(torch):
    """Clean up PyTorch cache if available."""
    if torch is not None:
        try:
            if hasattr(torch.cuda, "empty_cache"):
                torch.cuda.empty_cache()
            if hasattr(torch.backends, "mps") and hasattr(
                torch.backends.mps, "empty_cache"
            ):
                torch.backends.mps.empty_cache()
        except Exception:
            pass
