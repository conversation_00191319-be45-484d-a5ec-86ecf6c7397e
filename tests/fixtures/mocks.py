"""
Centralized mock system for tests.

This module provides a flexible and configurable mocking system for external dependencies
used across different test modules to avoid code duplication and apply DRY principles.
"""

from unittest.mock import MagicMock, patch

import pytest

# Dictionary of all mockable modules grouped by category
MOCK_MODULES = {
    "api": [
        "api",
        "api.config",
        "api.config.settings",
        "api.utils",
    ],
    "database": [
        "database",
        "database.utils",
        "database.utils.file_utils",
    ],
    "ml": [
        "numpy",
        "torch",
        "PIL",
        "PIL.Image",
        "models",
    ],
}


def create_mock_modules(categories=None):
    """
    Create a context manager that mocks specified module categories.

    Args:
        categories (list, optional): List of categories to mock. If None, all categories are mocked.

    Returns:
        A context manager that mocks the specified modules.
    """
    if categories is None:
        categories = list(MOCK_MODULES.keys())

    modules_to_mock = {}
    for category in categories:
        if category in MOCK_MODULES:
            for module in MOCK_MODULES[category]:
                modules_to_mock[module] = MagicMock()

    return patch.dict("sys.modules", modules_to_mock)


@pytest.fixture
def mock_modules(request):
    """
    Fixture to mock modules based on markers.

    Usage:
        @pytest.mark.mock_api
        @pytest.mark.mock_database
        def test_something(mock_modules):
            # Both API and database modules are mocked
            ...

    If no specific markers are provided, all modules are mocked by default.
    """
    categories = []

    if request.node.get_closest_marker("mock_api"):
        categories.append("api")

    if request.node.get_closest_marker("mock_database"):
        categories.append("database")

    if request.node.get_closest_marker("mock_ml"):
        categories.append("ml")

    if request.node.get_closest_marker("mock_all"):
        categories = list(MOCK_MODULES.keys())

    if not categories:
        # Default to mock all if no specific markers
        categories = list(MOCK_MODULES.keys())

    with create_mock_modules(categories):
        yield


@pytest.fixture(autouse=True)
def auto_mock_modules(request):
    """
    Automatically mock modules for tests with the auto_mock marker.

    This fixture is automatically applied to all tests but only takes effect
    if the test or its parent has the auto_mock marker.
    """
    marker = request.node.get_closest_marker("auto_mock")
    if marker:
        categories = marker.kwargs.get("categories", list(MOCK_MODULES.keys()))
        with create_mock_modules(categories):
            yield
    else:
        yield
