"""
Model-specific fixtures for testing.

This module provides fixtures specifically for testing ML models,
including mock models for performance testing.
"""

import pytest
import torch


@pytest.fixture
def sample_image_batch():
    """
    Create a sample batch of images for testing.

    Returns:
        torch.Tensor: A tensor of shape (32, 1, 91, 91) representing a batch
                     of 32 grayscale images of size 91x91 pixels.
    """
    # Create a batch of 32 random images with shape (1, 91, 91)
    batch_size = 32
    image_size = 91
    torch.manual_seed(42)  # For reproducibility
    return torch.randn(batch_size, 1, image_size, image_size)


@pytest.fixture
def sample_labels():
    """
    Create sample binary labels for testing.

    Returns:
        torch.Tensor: A tensor of shape (32, 1) containing binary labels
                     (0.0 or 1.0).
    """
    # Create 32 binary labels
    batch_size = 32
    torch.manual_seed(42)  # For reproducibility
    return torch.randint(0, 2, (batch_size, 1)).float()


@pytest.fixture(name="mock_model_weights")
def mock_model_weights():
    """
    Create mock model weights for testing a dynamic CNN.

    Returns:
        dict: A dictionary containing mock weights for a dynamic CNN model.
    """
    torch.manual_seed(42)  # For reproducibility

    # These dimensions correspond to the default dynamic_cnn_params
    # in tests/models/test_dynamic_cnn.py
    # Input image: 91x91, 1 channel -> after convs and pool -> 6x45x45 feature map
    num_features_before_fc = 6 * 45 * 45
    return {
        # Conv1 + BatchNorm1
        "0.weight": torch.randn(3, 1, 3, 3),
        "0.bias": torch.randn(3),
        "1.weight": torch.randn(3),
        "1.bias": torch.randn(3),
        "1.running_mean": torch.randn(3),
        "1.running_var": torch.randn(3),
        "1.num_batches_tracked": torch.tensor(0),
        # Conv2 + BatchNorm2
        "3.weight": torch.randn(6, 3, 3, 3),
        "3.bias": torch.randn(6),
        "4.weight": torch.randn(6),
        "4.bias": torch.randn(6),
        "4.running_mean": torch.randn(6),
        "4.running_var": torch.randn(6),
        "4.num_batches_tracked": torch.tensor(0),
        # FC1 (index 8 in the nn.Sequential)
        "8.weight": torch.randn(50, num_features_before_fc),
        "8.bias": torch.randn(50),
        # FC2 (index 11 in the nn.Sequential)
        "11.weight": torch.randn(1, 50),
        "11.bias": torch.randn(1),
    }


@pytest.fixture(scope="session")
def mock_model_factory():
    """
    Session-scoped fixture providing lightweight mock models for fast testing.

    This fixture creates mock PyTorch models that behave like real models
    but are much faster to instantiate and use minimal memory.
    """
    # pylint: disable=import-outside-toplevel,redefined-outer-name,reimported
    from unittest.mock import MagicMock

    class MockModel(MagicMock):
        """Lightweight mock model that mimics PyTorch nn.Module behavior."""

        def __init__(self, *args, **kwargs):  # pylint: disable=unused-argument
            super().__init__()
            self._parameters = {}
            self._modules = {}
            self.training = True

        def parameters(self):
            """Mock parameters method."""
            # Return a mock parameter that behaves like a tensor
            mock_param = MagicMock()
            mock_param.data = MagicMock()
            mock_param.grad = None
            return [mock_param]

        def named_parameters(self):
            """Mock named_parameters method."""
            return [("mock_param", self.parameters()[0])]

        def state_dict(self):
            """Mock state_dict method."""
            return {"mock_param": MagicMock()}

        def load_state_dict(self, state_dict):  # pylint: disable=unused-argument
            """Mock load_state_dict method."""
            return None

        def eval(self):
            """Mock eval method."""
            self.training = False
            return self

        def train(self, mode=True):
            """Mock train method."""
            self.training = mode
            return self

        def to(self, device):  # pylint: disable=unused-argument
            """Mock to method for device placement."""
            return self

        def __call__(self, *args, **kwargs):  # pylint: disable=unused-argument
            """Mock forward pass - returns appropriately shaped mock tensor."""
            # Import locally to avoid circular imports
            from tests.integration.trainer_test_utils import _create_mock_tensor_output

            return _create_mock_tensor_output(args, output_size=1)

    class MockLoss(MagicMock):
        """Lightweight mock loss function."""

        def __call__(self, predictions, targets):  # pylint: disable=unused-argument
            """Mock loss calculation."""
            mock_loss = MagicMock()
            mock_loss.item.return_value = 0.5  # Fixed loss value
            mock_loss.backward = MagicMock()
            return mock_loss

    class MockOptimizer(MagicMock):
        """Lightweight mock optimizer."""

        def __init__(self, *args, **kwargs):  # pylint: disable=unused-argument
            super().__init__()
            self.param_groups = [{"lr": 0.001}]

        def step(self):
            """Mock optimizer step."""
            return None

        def zero_grad(self):
            """Mock zero_grad."""
            return None

    return {
        "model": MockModel,
        "loss": MockLoss,
        "optimizer": MockOptimizer,
    }
