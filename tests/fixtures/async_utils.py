"""
Async utilities and fixtures for tests.

This module provides async fixtures and utilities for testing async code.
"""

from unittest.mock import AsyncMock

import pytest


@pytest.fixture
async def async_mock_supabase_client():
    """
    Create an async mock Supabase client for testing.

    This fixture provides a consistent async mock Supabase client that can be used
    across different test modules.
    """
    client = AsyncMock()
    # Configure the mock to return empty data by default
    table_mock = AsyncMock()
    select_mock = AsyncMock()
    execute_mock = AsyncMock()
    execute_mock.data = []

    select_mock.execute.return_value = execute_mock
    table_mock.select.return_value = select_mock
    client.table.return_value = table_mock

    return client


@pytest.fixture
async def async_mock_supabase_client_with_dataset(sample_dataset_data):
    """
    Create an async mock Supabase client that returns a single dataset.

    This fixture provides an async mock Supabase client that returns a single dataset
    when queried.
    """
    client = AsyncMock()
    # Configure the mock to return a single dataset
    table_mock = AsyncMock()
    select_mock = AsyncMock()
    execute_mock = AsyncMock()
    execute_mock.data = [sample_dataset_data]

    select_mock.execute.return_value = execute_mock
    table_mock.select.return_value = select_mock
    client.table.return_value = table_mock

    return client


@pytest.fixture
async def async_mock_supabase_client_with_datasets(sample_dataset_list):
    """
    Create an async mock Supabase client that returns multiple datasets.

    This fixture provides an async mock Supabase client that returns multiple datasets
    when queried.
    """
    client = AsyncMock()
    # Configure the mock to return multiple datasets
    table_mock = AsyncMock()
    select_mock = AsyncMock()
    execute_mock = AsyncMock()
    execute_mock.data = sample_dataset_list

    select_mock.execute.return_value = execute_mock
    table_mock.select.return_value = select_mock
    client.table.return_value = table_mock

    return client


@pytest.fixture
async def async_mock_database_response(sample_dataset_data):
    """
    Mock async database response for testing.

    This fixture provides a consistent async mock for the fetch_data function
    that returns a single dataset.
    """
    mock = AsyncMock()
    mock.return_value = [sample_dataset_data]
    return mock


@pytest.fixture
async def async_mock_database_responses(sample_dataset_list):
    """
    Mock async database response for testing with multiple datasets.

    This fixture provides a consistent async mock for the fetch_data function
    that returns multiple datasets.
    """
    mock = AsyncMock()
    mock.return_value = sample_dataset_list
    return mock
