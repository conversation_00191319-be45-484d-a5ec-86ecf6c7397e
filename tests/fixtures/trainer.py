"""
Trainer-specific fixtures for testing.

This module provides fixtures specifically for testing the ModelTrainer class.
"""

import tempfile

import pytest
import torch
from torch import nn, optim
from torch.utils.data import DataLoader, TensorDataset


class SimpleModel(nn.Module):
    """A simple model for testing."""

    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(10, 1)

    def forward(self, x):
        """Forward pass of the model."""
        return self.linear(x)


@pytest.fixture
def mock_data_loaders():
    """Create mock data loaders for training and testing."""
    # Create random data
    x_train = torch.randn(20, 10)
    y_train = torch.randint(0, 2, (20, 1)).float()
    x_test = torch.randn(10, 10)
    y_test = torch.randint(0, 2, (10, 1)).float()

    # Create datasets
    train_dataset = TensorDataset(x_train, y_train)
    test_dataset = TensorDataset(x_test, y_test)

    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=5)
    test_loader = DataLoader(test_dataset, batch_size=5)

    return {"train": train_loader, "test": test_loader}


@pytest.fixture
def model_components():
    """Create model components for testing."""
    model = SimpleModel()
    optimizer = optim.SGD(model.parameters(), lr=0.01)

    return {"model": model, "optimizer": optimizer}


@pytest.fixture
def training_config():
    """Create training configuration for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield {
            "model_id": "test_model",
            "model_run_uuid": "test-integration-uuid",
            "run_output_dir": temp_dir,
            "epochs": 2,
        }
