"""
Shared fixtures for cache-related tests.

This module provides common fixtures for testing dataset cache functionality
to reduce code duplication across test files.
"""

from datetime import UTC, datetime, timedelta
from uuid import uuid4

import pytest

from database.services.dataset_state_cache import DatasetStateCacheService


@pytest.fixture(name="cache_dataset_uuid")
def fixture_cache_dataset_uuid():
    """Fixture providing a test dataset UUID for cache tests."""
    return str(uuid4())


@pytest.fixture(name="cache_timestamp")
def fixture_cache_timestamp():
    """Fixture providing a test timestamp for cache tests."""
    return datetime.now(UTC)


@pytest.fixture(name="cache_old_timestamp")
def fixture_cache_old_timestamp():
    """Fixture providing an old timestamp for cache invalidation tests."""
    return datetime.now(UTC)


@pytest.fixture(name="cache_new_timestamp")
def fixture_cache_new_timestamp(cache_old_timestamp):
    """Fixture providing a new timestamp (1 hour after old) for cache invalidation tests."""
    return cache_old_timestamp + timedelta(hours=1)


@pytest.fixture(name="cache_prepared_state")
def fixture_cache_prepared_state(cache_dataset_uuid, cache_timestamp):
    """Fixture that sets up a prepared cache state and returns dataset info."""
    DatasetStateCacheService.set_cache_state(
        dataset_uuid=cache_dataset_uuid,
        content_updated_at=cache_timestamp,
        is_prepared=True,
    )
    return cache_dataset_uuid, cache_timestamp


@pytest.fixture(name="cache_unprepared_state")
def fixture_cache_unprepared_state(cache_dataset_uuid, cache_timestamp):
    """Fixture that sets up an unprepared cache state and returns dataset info."""
    DatasetStateCacheService.set_cache_state(
        dataset_uuid=cache_dataset_uuid,
        content_updated_at=cache_timestamp,
        is_prepared=False,
    )
    return cache_dataset_uuid, cache_timestamp


@pytest.fixture(name="cache_invalidation_setup")
def fixture_cache_invalidation_setup(
    cache_dataset_uuid, cache_old_timestamp, cache_new_timestamp
):
    """Fixture that sets up cache state for invalidation testing."""
    # Set cache state with old timestamp
    DatasetStateCacheService.set_cache_state(
        dataset_uuid=cache_dataset_uuid,
        content_updated_at=cache_old_timestamp,
        is_prepared=True,
    )
    return cache_dataset_uuid, cache_old_timestamp, cache_new_timestamp


@pytest.fixture(name="cache_multiple_entries_setup")
def fixture_cache_multiple_entries_setup(
    cache_dataset_uuid, cache_old_timestamp, cache_new_timestamp
):
    """Fixture that sets up multiple cache entries for the same dataset."""
    # Set cache states for both timestamps
    DatasetStateCacheService.set_cache_state(
        dataset_uuid=cache_dataset_uuid,
        content_updated_at=cache_old_timestamp,
        is_prepared=True,
    )
    DatasetStateCacheService.set_cache_state(
        dataset_uuid=cache_dataset_uuid,
        content_updated_at=cache_new_timestamp,
        is_prepared=True,
    )
    return cache_dataset_uuid, cache_old_timestamp, cache_new_timestamp


def setup_cache_state(dataset_uuid, timestamp, is_prepared=True):
    """Helper function to set up cache state."""
    return DatasetStateCacheService.set_cache_state(
        dataset_uuid=dataset_uuid,
        content_updated_at=timestamp,
        is_prepared=is_prepared,
    )


def verify_cache_validity(dataset_uuid, timestamp, expected_validity=True):
    """Helper function to verify cache validity."""
    actual_validity = DatasetStateCacheService.is_cache_valid(dataset_uuid, timestamp)
    assert (
        actual_validity == expected_validity
    ), f"Expected cache validity {expected_validity}, got {actual_validity}"


def verify_cache_invalidation(dataset_uuid, old_timestamp, new_timestamp):
    """Helper function to verify cache invalidation behavior."""
    # Verify old timestamp is still valid
    verify_cache_validity(dataset_uuid, old_timestamp, True)

    # Verify new timestamp is invalid (different cache key)
    verify_cache_validity(dataset_uuid, new_timestamp, False)


def perform_cache_invalidation_test(dataset_uuid, timestamp):
    """Helper function to perform standard cache invalidation test."""
    # Set cache state
    setup_cache_state(dataset_uuid, timestamp, True)

    # Verify cache exists
    verify_cache_validity(dataset_uuid, timestamp, True)

    # Invalidate cache
    invalidated_count = DatasetStateCacheService.invalidate_dataset_cache(dataset_uuid)

    # Verify cache is invalidated
    verify_cache_validity(dataset_uuid, timestamp, False)

    return invalidated_count
