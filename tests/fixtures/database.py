"""
Database-related mock fixtures for tests.

This module provides consolidated database-related mock implementations and fixtures
that can be used across different test modules to avoid code duplication.
Follows DRY principles with a flexible configuration system.
"""

from typing import Generator
from unittest.mock import MagicMock, patch

import pytest


def create_mock_database(mock_type="modules", **kwargs):
    """
    Create a mock database component with specified configuration.

    This is a consolidated function that replaces multiple separate mock creation
    functions to follow DRY principles.

    Args:
        mock_type (str): Type of mock to create ("modules", "fetch_data", "supabase")
        **kwargs: Additional configuration options
            - data: Data to return for supabase client mocks
            - target: Target path for patching (for fetch_data)

    Returns:
        A context manager or mock object based on the specified type
    """
    if mock_type == "modules":
        return patch.dict(
            "sys.modules",
            {
                "database": MagicMock(),
                "database.utils": MagicMock(),
                "database.utils.file_utils": MagicMock(),
            },
        )
    if mock_type == "fetch_data":
        target = kwargs.get("target", "database.services.dataset_service.fetch_data")
        return patch(target)
    if mock_type == "supabase":
        client = MagicMock()
        client.table.return_value.select.return_value.execute.return_value.data = (
            kwargs.get("data", [])
        )
        return client
    raise ValueError(f"Unknown mock_type: {mock_type}")


# For backward compatibility, maintain the original function names
def create_mock_database_modules():
    """Create a context manager that mocks database modules."""
    return create_mock_database("modules")


def create_mock_fetch_data():
    """Create a context manager that mocks the fetch_data function."""
    return create_mock_database("fetch_data")


def create_mock_supabase_client():
    """Create a mock Supabase client for testing."""
    return create_mock_database("supabase")


@pytest.fixture
def mock_database_component(request):
    """
    Flexible fixture to mock database components based on parameters.

    Args:
        request: The pytest request object with fixture parameters
            - mock_type: Type of mock to create ("modules", "fetch_data", "supabase")
            - data: Data to return for supabase client mocks
            - target: Target path for patching

    Usage:
        def test_something(mock_database_component):
            # Uses default mock_type="modules"
            ...

        @pytest.mark.parametrize(
            "mock_database_component",
            [{"mock_type": "fetch_data"}],
            indirect=True
        )
        def test_with_fetch_data(mock_database_component):
            # mock_database_component is the fetch_data mock
            ...
    """
    params = getattr(request, "param", {})
    mock_type = params.get("mock_type", "modules")

    with create_mock_database(mock_type, **params) as mock:
        yield mock


# Keep the original fixtures for backward compatibility
@pytest.fixture(autouse=False)
def mock_database_modules():
    """Mock the database module to avoid real database connections."""
    with create_mock_database_modules():
        yield


@pytest.fixture(autouse=False)
def mock_fetch_data():
    """Mock the fetch_data function used by the dataset service."""
    with create_mock_fetch_data() as mock:
        yield mock


@pytest.fixture
def mock_supabase_client():
    """
    Create a mock Supabase client for testing.

    This fixture provides a consistent mock Supabase client that can be used
    across different test modules.
    """
    return create_mock_supabase_client()


@pytest.fixture
def mock_supabase_create_client():
    """
    Mock the create_client function from supabase module.

    This fixture provides a consistent mock for the create_client function
    that can be used across different test modules.
    """
    with patch("database.supabase_client.create_client") as mock:
        mock.return_value = MagicMock()
        yield mock


@pytest.fixture
def mock_supabase_client_with_dataset(sample_dataset_data):
    """
    Create a mock Supabase client that returns a single dataset.

    This fixture provides a mock Supabase client that returns a single dataset
    when queried.
    """
    client = MagicMock()
    client.table.return_value.select.return_value.execute.return_value.data = [
        sample_dataset_data
    ]
    return client


@pytest.fixture
def mock_supabase_client_with_datasets(sample_dataset_list):
    """
    Create a mock Supabase client that returns multiple datasets.

    This fixture provides a mock Supabase client that returns multiple datasets
    when queried.
    """
    client = MagicMock()
    client.table.return_value.select.return_value.execute.return_value.data = (
        sample_dataset_list
    )
    return client


@pytest.fixture
def mock_database_response(sample_dataset_data):
    """
    Mock database response for testing.

    This fixture provides a consistent mock for the fetch_data function
    that returns a single dataset.
    """
    with patch("database.supabase_client.fetch_data") as mock:
        mock.return_value = [sample_dataset_data]
        yield mock


@pytest.fixture
def mock_database_responses(sample_dataset_list):
    """
    Mock database response for testing with multiple datasets.

    This fixture provides a consistent mock for the fetch_data function
    that returns multiple datasets.
    """
    with patch("database.supabase_client.fetch_data") as mock:
        mock.return_value = sample_dataset_list
        yield mock


@pytest.fixture
def mock_supabase_profile() -> Generator[MagicMock, None, None]:
    """
    Mock the get_supabase_profile function.

    Yields:
        MagicMock: A mock of the get_supabase_profile function.
    """
    with patch("api.utils.get_supabase_profile") as mock:
        mock.return_value = "development"
        yield mock


@pytest.fixture
def mock_fetch_model_and_version() -> Generator[MagicMock, None, None]:
    """
    Mock the fetch_model_and_version function.

    Yields:
        MagicMock: A mock of the fetch_model_and_version function.
    """
    with patch("database.supabase_client.fetch_model_and_version") as mock:
        yield mock
