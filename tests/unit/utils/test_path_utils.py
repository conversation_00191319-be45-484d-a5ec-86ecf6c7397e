"""
Tests for path utility functions.
"""

from src.utils.path_utils import (
    build_image_path,
    get_filename,
    get_folders_structure_from_uuid,
    is_facts_image_location_key,
    is_image_location_key,
)


class TestGetFoldersStructureFromUuid:
    """Test suite for get_folders_structure_from_uuid function."""

    def test_get_folders_structure_from_uuid_basic(self):
        """Test basic UUID folder structure generation."""
        uuid = "d57d52e4-093a-4c26-96b6-6fdecb7e7191"
        result = get_folders_structure_from_uuid(uuid)
        expected = ["d57", "d52"]
        assert result == expected

    def test_get_folders_structure_from_uuid_custom_depth(self):
        """Test UUID folder structure generation with custom depth."""
        uuid = "d57d52e4-093a-4c26-96b6-6fdecb7e7191"
        result = get_folders_structure_from_uuid(uuid, depth=3)
        expected = ["d57", "d52", "e40"]
        assert result == expected

    def test_get_folders_structure_from_uuid_empty(self):
        """Test UUID folder structure generation with empty UUID."""
        result = get_folders_structure_from_uuid("")
        assert result == []

    def test_get_folders_structure_from_uuid_none(self):
        """Test UUID folder structure generation with None UUID."""
        result = get_folders_structure_from_uuid(None)
        assert result == []

    def test_get_folders_structure_from_uuid_short(self):
        """Test UUID folder structure generation with short UUID."""
        uuid = "abc"
        result = get_folders_structure_from_uuid(uuid)
        expected = ["abc"]
        assert result == expected


class TestIsImageLocationKey:
    """Test suite for is_image_location_key function."""

    def test_is_image_location_key_valid(self):
        """Test with valid image location keys."""
        assert is_image_location_key("CS") is True
        assert is_image_location_key("FB") is True
        assert is_image_location_key("FC") is True
        assert is_image_location_key("FM") is True
        assert is_image_location_key("FS") is True
        assert is_image_location_key("FW") is True
        assert is_image_location_key("SS") is True

    def test_is_image_location_key_invalid(self):
        """Test with invalid image location keys."""
        assert is_image_location_key("XX") is False
        assert is_image_location_key("") is False
        assert is_image_location_key("INVALID") is False


class TestIsFactsImageLocationKey:
    """Test suite for is_facts_image_location_key function."""

    def test_is_facts_image_location_key_valid(self):
        """Test with valid facts image location keys."""
        assert is_facts_image_location_key("FB") is True
        assert is_facts_image_location_key("FC") is True
        assert is_facts_image_location_key("FM") is True
        assert is_facts_image_location_key("FS") is True
        assert is_facts_image_location_key("FW") is True

    def test_is_facts_image_location_key_non_facts(self):
        """Test with non-facts image location keys."""
        assert is_facts_image_location_key("CS") is False
        assert is_facts_image_location_key("SS") is False

    def test_is_facts_image_location_key_invalid(self):
        """Test with invalid image location keys."""
        assert is_facts_image_location_key("XX") is False
        assert is_facts_image_location_key("") is False


class TestGetFilename:
    """Test suite for get_filename function."""

    def test_get_filename_basic(self):
        """Test basic filename extraction."""
        url = "/images/coins/4428.jpg"
        result = get_filename(url)
        assert result == "4428.jpg"

    def test_get_filename_complex_path(self):
        """Test filename extraction from complex path."""
        url = "https://example.com/path/to/image/test.png"
        result = get_filename(url)
        assert result == "test.png"

    def test_get_filename_no_extension(self):
        """Test filename extraction without extension."""
        url = "/path/to/filename"
        result = get_filename(url)
        assert result == "filename"

    def test_get_filename_empty(self):
        """Test filename extraction from empty string."""
        result = get_filename("")
        assert result == ""


class TestBuildImagePath:
    """Test suite for build_image_path function."""

    def test_build_image_path_coin_location(self):
        """Test image path building for coin location."""
        image_url = "/images/coins/4428.jpg"
        location_key = "CS"
        coin_side_uuid = "d57d52e4-093a-4c26-96b6-6fdecb7e7191"

        result = build_image_path(
            image_url=image_url,
            location_key=location_key,
            coin_side_uuid=coin_side_uuid,
        )

        expected = "static/coins/d57/d52/4428.jpg"
        assert result == expected

    def test_build_image_path_fact_coin_location(self):
        """Test image path building for fact coin location."""
        image_url = "/images/facts/coins/test.jpg"
        location_key = "FC"
        fact_uuid = "a1b2c3d4-e5f6-7890-abcd-ef1234567890"

        result = build_image_path(
            image_url=image_url,
            location_key=location_key,
            fact_uuid=fact_uuid,
        )

        expected = "static/facts/coins/a1b/2c3/test.jpg"
        assert result == expected

    def test_build_image_path_scrapes_location(self):
        """Test image path building for scrapes location."""
        image_url = "/scrapes/image.jpg"
        location_key = "SS"
        fact_uuid = "a1b2c3d4-e5f6-7890-abcd-ef1234567890"

        result = build_image_path(
            image_url=image_url,
            location_key=location_key,
            fact_uuid=fact_uuid,
        )

        expected = "static/scrapes/a1b2c3d4-e5f6-7890-abcd-ef1234567890/image.jpg"
        assert result == expected

    def test_build_image_path_invalid_location_key(self):
        """Test image path building with invalid location key."""
        image_url = "/images/test.jpg"
        location_key = "INVALID"

        result = build_image_path(
            image_url=image_url,
            location_key=location_key,
        )

        assert result == image_url

    def test_build_image_path_missing_required_uuid(self):
        """Test image path building with missing required UUID."""
        image_url = "/images/test.jpg"
        location_key = "CS"

        result = build_image_path(
            image_url=image_url,
            location_key=location_key,
        )

        assert result == image_url

    def test_build_image_path_fact_location_missing_fact_uuid(self):
        """Test image path building for fact location without fact UUID."""
        image_url = "/images/test.jpg"
        location_key = "FC"
        coin_side_uuid = "d57d52e4-093a-4c26-96b6-6fdecb7e7191"

        result = build_image_path(
            image_url=image_url,
            location_key=location_key,
            coin_side_uuid=coin_side_uuid,
        )

        assert result == image_url

    def test_build_image_path_all_fact_location_keys(self):
        """Test image path building for all fact location keys."""
        image_url = "/images/test.jpg"
        fact_uuid = "a1b2c3d4-e5f6-7890-abcd-ef1234567890"

        fact_location_keys = ["FB", "FC", "FM", "FS", "FW"]
        expected_bases = [
            "static/facts/banknotes",
            "static/facts/coins",
            "static/facts/miscellaneous",
            "static/facts/stamps",
            "static/facts/watches",
        ]

        for location_key, expected_base in zip(fact_location_keys, expected_bases):
            result = build_image_path(
                image_url=image_url,
                location_key=location_key,
                fact_uuid=fact_uuid,
            )

            expected = f"{expected_base}/a1b/2c3/test.jpg"
            assert result == expected

    def test_build_image_path_empty_image_url(self):
        """Test image path building with empty image URL."""
        image_url = ""
        location_key = "CS"
        coin_side_uuid = "d57d52e4-093a-4c26-96b6-6fdecb7e7191"

        result = build_image_path(
            image_url=image_url,
            location_key=location_key,
            coin_side_uuid=coin_side_uuid,
        )

        expected = "static/coins/d57/d52"
        assert result == expected

    def test_build_image_path_with_base_dir(self):
        """Test image path building with base directory."""
        image_url = "/images/coins/4428.jpg"
        location_key = "CS"
        coin_side_uuid = "d57d52e4-093a-4c26-96b6-6fdecb7e7191"
        base_dir = "/var/www/html/images"

        result = build_image_path(
            image_url=image_url,
            location_key=location_key,
            coin_side_uuid=coin_side_uuid,
            base_dir=base_dir,
        )

        expected = "/var/www/html/images/static/coins/d57/d52/4428.jpg"
        assert result == expected

    def test_build_image_path_without_base_dir(self):
        """Test image path building without base directory (relative path)."""
        image_url = "/images/coins/4428.jpg"
        location_key = "CS"
        coin_side_uuid = "d57d52e4-093a-4c26-96b6-6fdecb7e7191"

        result = build_image_path(
            image_url=image_url,
            location_key=location_key,
            coin_side_uuid=coin_side_uuid,
            base_dir=None,
        )

        expected = "static/coins/d57/d52/4428.jpg"
        assert result == expected
