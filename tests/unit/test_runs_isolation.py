"""
Test to verify that runs directory isolation is working correctly.

This test ensures that the fix for the regression where test folders
were being generated in the production /runs directory is working.
"""

from pathlib import Path

from src.config.paths import get_run_paths, get_runs_base_dir


class TestRunsIsolation:
    """Test that runs directory isolation is working correctly."""

    def test_runs_directory_is_isolated(self):
        """Test that get_run_dir uses isolated temporary directory."""
        model_run_uuid = "test-isolation-uuid"
        run_paths = get_run_paths(model_run_uuid)
        run_dir = run_paths.base

        # The run directory should be in a temporary directory, not production /runs
        run_dir_str = str(run_dir).lower()
        assert (
            "tmp" in run_dir_str
            or "temp" in run_dir_str
            or "/t/" in run_dir_str
            or "test_runs_" in run_dir_str
        )
        assert str(run_dir) != f"runs/{model_run_uuid}"

        # The base directory should be a temporary directory
        base_dir = get_runs_base_dir()
        base_dir_str = str(base_dir).lower()
        assert (
            "tmp" in base_dir_str
            or "temp" in base_dir_str
            or "/t/" in base_dir_str
            or "test_runs_" in base_dir_str
        )
        assert str(base_dir) != "runs"

    def test_ensure_run_directories_creates_in_isolated_location(self):
        """Test that ensure_run_directories creates directories in isolated location."""
        model_run_uuid = "test-ensure-isolation-uuid"
        run_paths = get_run_paths(model_run_uuid)
        directories = run_paths.ensure_directories()

        # All directories should be in temporary location
        for dir_path in directories.values():
            dir_path_str = str(dir_path).lower()
            assert (
                "tmp" in dir_path_str
                or "temp" in dir_path_str
                or "/t/" in dir_path_str
                or "test_runs_" in dir_path_str
            )
            assert dir_path.exists()  # Directory should be created

        # Verify the run directory exists and is in the right place
        run_dir = directories["run"]
        assert run_dir.name == model_run_uuid
        assert run_dir.exists()

    def test_production_runs_directory_not_affected(self):
        """Test that production /runs directory is not affected by tests."""
        # Get the actual production directory path (without test isolation)
        project_root = Path(__file__).parent.parent.parent
        actual_production_runs_dir = project_root / "runs"

        # Create some test directories using the isolated system
        model_run_uuid = "test-production-isolation-uuid"
        run_paths = get_run_paths(model_run_uuid)
        run_paths.ensure_directories()

        # Verify that the test directory was NOT created in production
        # The production runs directory should either not exist or not contain our test UUID
        if actual_production_runs_dir.exists():
            # If it exists, it should not contain our test UUID
            test_dirs = [
                d
                for d in actual_production_runs_dir.iterdir()
                if d.is_dir() and model_run_uuid in d.name
            ]
            assert (
                len(test_dirs) == 0
            ), f"Found test directories in production runs: {test_dirs}"

        # Verify that the test directory was created in the isolated location instead
        run_paths = get_run_paths(model_run_uuid)
        isolated_run_dir = run_paths.base
        assert (
            isolated_run_dir.exists()
        ), "Test directory should exist in isolated location"
        assert (
            actual_production_runs_dir not in isolated_run_dir.parents
        ), "Test directory should not be under production runs directory"

    def test_isolated_runs_config_is_independent(self):
        """Test that the isolated runs config is independent of production config."""
        # Get the current isolated base directory
        isolated_base = get_runs_base_dir()

        # It should be a temporary directory
        isolated_base_str = str(isolated_base).lower()
        assert (
            "tmp" in isolated_base_str
            or "temp" in isolated_base_str
            or "/t/" in isolated_base_str
            or "test_runs_" in isolated_base_str
        )

        # Create a run directory and verify it's in the isolated location
        model_run_uuid = "test-config-independence-uuid"
        run_paths = get_run_paths(model_run_uuid)
        run_dir = run_paths.base

        # The run directory should be under the isolated base
        assert run_dir.parent == isolated_base
        assert isolated_base in run_dir.parents
