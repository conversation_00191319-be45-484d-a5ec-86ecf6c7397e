"""
Unit tests for the dataset service.
"""

from unittest.mock import patch
from uuid import uuid4

import pytest

# Import directly from the model file to avoid circular imports
from database.models.dataset_set import DatasetSet
from database.services.dataset_service import DatasetError, DatasetService

# Import centralized test data
from tests.fixtures.data import get_sample_dataset_data, get_sample_dataset_sets


class TestDatasetService:
    """Test cases for the DatasetService class."""

    def _setup_dataset_test(self, mock_fetch_data, sample_data):
        """Setup common test data for dataset tests."""
        dataset_uuid = str(uuid4())
        count_result = [{"count": len(sample_data)}]

        # Update dataset_uuid in sample data to match the one we're testing with
        for item in sample_data:
            item["dataset_uuid"] = dataset_uuid

        # Set up side effect to return count first, then the data
        mock_fetch_data.side_effect = [count_result, sample_data]
        return dataset_uuid

    def _fetch_all_dataset_sets(self, dataset_uuid, batch_size=2):
        """Fetch and collect all dataset sets."""
        all_sets = []
        for batch in DatasetService.get_dataset_sets(
            dataset_uuid, batch_size=batch_size
        ):
            all_sets.extend(batch)
        return all_sets

    def _print_debug_info(self, dataset_sets):
        """Print debug information about dataset sets."""
        print("\nDebug - Types in dataset sets:")
        for i, ds in enumerate(dataset_sets):
            print(f"  Item {i}: Type={type(ds).__name__}, Value={ds}")

        print("\nDebug - Instance checks:")
        for i, ds in enumerate(dataset_sets):
            print(f"  Item {i} is DatasetSet: " f"{isinstance(ds, DatasetSet)}")

    @pytest.fixture(autouse=True)
    def mock_fetch_data(self):
        """Mock the fetch_data function."""
        with patch("database.services.dataset_service.fetch_data") as mock:
            yield mock

    @pytest.fixture
    def sample_dataset_data(self):
        """Sample dataset data for testing."""
        # Use centralized data function with custom values
        return get_sample_dataset_data(
            custom_values={
                "uuid": str(uuid4()),
                "name": "test-dataset",
                "images_count": 10,
                "created_at": "2023-01-01T00:00:00",
                "content_updated_at": "2023-01-01T00:00:00",
            }
        )

    @pytest.fixture
    def sample_dataset_sets_data(self):
        """Sample dataset sets data for testing."""
        # Generate a unique dataset UUID for this test run
        dataset_uuid = str(uuid4())

        # Use centralized function with custom values for this test
        return get_sample_dataset_sets(
            count=2,
            custom_values_list=[
                {
                    "uuid": str(uuid4()),
                    "coin_side_uuid": str(uuid4()),
                    "created_at": "2023-01-01T00:00:00",
                    "dataset_uuid": dataset_uuid,
                    "image_uuid": str(uuid4()),
                    "set_type": 1,
                    "images_reviews": {"image_url": "https://example.com/image1.jpg"},
                },
                {
                    "uuid": str(uuid4()),
                    "coin_side_uuid": str(uuid4()),
                    "created_at": "2023-01-01T00:00:00",
                    "dataset_uuid": dataset_uuid,
                    "image_uuid": str(uuid4()),
                    "set_type": 1,
                    "images_reviews": {"image_url": "https://example.com/image2.jpg"},
                },
            ],
        )

    def test_get_dataset_success(self, mock_fetch_data, sample_dataset_data):
        """Test successful dataset retrieval."""
        # Setup
        dataset_uuid = sample_dataset_data["uuid"]
        # Mock the return value with the sample data
        mock_fetch_data.return_value = [dict(sample_dataset_data)]

        # Test
        result = DatasetService.get_dataset(dataset_uuid)

        # Assert
        assert result["uuid"] == dataset_uuid
        assert result["name"] == "test-dataset"
        mock_fetch_data.assert_called_once_with(
            "datasets",
            {"filters": [{"column": "uuid", "value": dataset_uuid}]},
            profile=None,
        )

    def test_get_dataset_not_found(self, mock_fetch_data):
        """Test dataset not found scenario."""
        # Setup
        mock_fetch_data.return_value = []

        # Test & Assert
        with pytest.raises(DatasetError, match="Dataset not found with UUID"):
            DatasetService.get_dataset("non-existent-uuid")

        # Verify the fetch_data was called with the correct arguments
        mock_fetch_data.assert_called_once_with(
            "datasets",
            {"filters": [{"column": "uuid", "value": "non-existent-uuid"}]},
            profile=None,
        )

    def test_get_dataset_sets_success(self, mock_fetch_data, sample_dataset_sets_data):
        """Test successful dataset sets retrieval."""
        # Setup test data
        dataset_uuid = self._setup_dataset_test(
            mock_fetch_data, sample_dataset_sets_data
        )

        # Test - fetch and collect all dataset sets
        all_sets = self._fetch_all_dataset_sets(dataset_uuid)

        # Debug output
        self._print_debug_info(all_sets)

        # Assert
        assert len(all_sets) == 2
        # Check all items are DatasetSet instances
        all_are_dataset_set = all(isinstance(ds, DatasetSet) for ds in all_sets)
        type_names = [type(ds).__name__ for ds in all_sets]
        error_msg = f"Not all items are DatasetSet: {type_names}"
        assert all_are_dataset_set, error_msg
        # Check dataset_uuid - could be string or UUID
        assert all(str(ds.dataset_uuid) == dataset_uuid for ds in all_sets)
        assert all(ds.image_url is not None for ds in all_sets)
        mock_fetch_data.assert_any_call(
            "dataset_sets",
            {
                "filters": [{"column": "dataset_uuid", "value": dataset_uuid}],
                "count": "exact",
            },
            profile=None,
        )

        # Check the second call to fetch_data (the actual data fetch)
        # The first call is for count, the second is for data
        assert (
            mock_fetch_data.call_count >= 2
        ), "Expected at least 2 calls to fetch_data"

        # Get the second call (index 1) which should be the data fetch
        args, kwargs = mock_fetch_data.call_args_list[1]

        # First argument should be the table name
        assert args[0] == "dataset_sets"

        # Second argument should be the query dict
        query = args[1] if len(args) > 1 else kwargs

        # Check the query parameters
        assert (
            query["select"]
            == "*, images_reviews(image_url, location, fact_uuid, coin_side_uuid)"
        )
        assert query["filters"] == [
            {"column": "dataset_uuid", "value": dataset_uuid},
            {"column": "image_uuid", "operator": "is_not", "value": None},
        ]
        assert query["limit"] == 2  # batch_size=2
        assert query["offset"] == 0
        assert query["order_by"] == [{"column": "created_at", "ascending": True}]

        # Check profile is passed correctly
        assert kwargs.get("profile") is None

    def test_get_dataset_sets_empty(self, mock_fetch_data):
        """Test dataset sets retrieval with no results."""
        # Setup
        dataset_uuid = str(uuid4())
        # First call returns count=0, second call should not happen
        mock_fetch_data.return_value = [{"count": 0}]

        # Test
        all_sets = []
        for batch in DatasetService.get_dataset_sets(dataset_uuid):
            all_sets.extend(batch)

        # Assert
        assert len(all_sets) == 0
        mock_fetch_data.assert_called_once_with(
            "dataset_sets",
            {
                "filters": [{"column": "dataset_uuid", "value": dataset_uuid}],
                "count": "exact",
            },
            profile=None,
        )

    @pytest.mark.asyncio
    async def test_download_dataset_images(self):
        """Test downloading dataset images."""
        # This is a more complex test that would require mocking file operations
        # and HTTP requests. For now, we'll just test that the method exists
        # and can be called with the right parameters.
        assert hasattr(DatasetService, "download_dataset_images")
        assert callable(DatasetService.download_dataset_images)

    def test_verify_and_recover_dataset_images_method_exists(self):
        """Test that the verify_and_recover_dataset_images method exists."""
        assert hasattr(DatasetService, "verify_and_recover_dataset_images")
        assert callable(DatasetService.verify_and_recover_dataset_images)
