"""
Tests for the ModelTrainer class robustness against broken configuration values.
"""

# pylint: disable=protected-access,redefined-outer-name
# protected-access is needed because we need to test protected methods
# redefined-outer-name is needed for pytest fixtures

import tempfile
from unittest import mock
from unittest.mock import MagicMock

from torch import nn

from src.train.trainer import Model<PERSON>rainer, TrainerConfig


class TestModelTrainerRobustness:
    """Tests for the ModelTrainer class robustness against broken configuration values."""

    def test_broken_loss_function_none(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that trainer handles None as loss_function value."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add required model_run_uuid and None as loss_function in training_config
        training_config["model_run_uuid"] = "test-robustness-uuid-1"
        training_config["model_version_parameters"] = {"loss_function": None}

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock logger to check info message
        with mock.patch.object(trainer.logger, "info") as mock_info:
            # Initialize the loss function - should not raise an exception
            loss_fn = trainer._init_loss_function(config)

            # Verify that info about using default loss was logged
            assert any(
                "Using default BCE loss function" in call[0][0]
                for call in mock_info.call_args_list
            )

            # Verify that a default loss function was created
            assert loss_fn is not None

    def test_broken_loss_function_empty_dict(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that trainer handles empty dict as loss_function value."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add required model_run_uuid and empty dict as loss_function in training_config
        training_config["model_run_uuid"] = "test-robustness-uuid-2"
        training_config["model_version_parameters"] = {"loss_function": {}}

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock logger to check info message
        with mock.patch.object(trainer.logger, "info") as mock_info:
            # Initialize the loss function - should not raise an exception
            loss_fn = trainer._init_loss_function(config)

            # Verify that info about using default loss was logged
            assert any(
                "Using default BCE loss function" in call[0][0]
                for call in mock_info.call_args_list
            )

            # Verify that a default loss function was created
            assert loss_fn is not None

    def test_broken_loss_function_wrong_type(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that trainer handles wrong type (string) as loss_function value."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add required model_run_uuid and string as loss_function in training_config
        training_config["model_run_uuid"] = "test-robustness-uuid-3"
        training_config["model_version_parameters"] = {"loss_function": "bce_loss"}

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock logger to check warning message - this case should trigger a warning
        with mock.patch.object(trainer.logger, "warning") as mock_warning:
            # Initialize the loss function - should not raise an exception
            loss_fn = trainer._init_loss_function(config)

            # Verify that a warning was logged about invalid format
            mock_warning.assert_called_once()

            # Verify that a default loss function was created
            assert loss_fn is not None

    def test_broken_loss_function_dict_without_type(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that trainer handles dict without 'type' key as loss_function value."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add required model_run_uuid and dict without type as loss_function in training_config
        training_config["model_run_uuid"] = "test-robustness-uuid-4"
        training_config["model_version_parameters"] = {
            "loss_function": {
                "config": {"reduction": "mean"}
                # Missing 'type' key
            }
        }

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock logger to check warning message
        with mock.patch.object(trainer.logger, "warning") as mock_warning:
            # Initialize the loss function - should not raise an exception
            loss_fn = trainer._init_loss_function(config)

            # Verify that a warning was logged about invalid format
            mock_warning.assert_called_once()

            # Verify that a default loss function was created
            assert loss_fn is not None

    def test_broken_loss_function_invalid_type(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that trainer handles invalid loss function type."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add required model_run_uuid and invalid loss type in training_config
        training_config["model_run_uuid"] = "test-robustness-uuid-5"
        training_config["model_version_parameters"] = {
            "loss_function": {
                "type": "nonexistent_loss",
                "config": {"reduction": "mean"},
            }
        }

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Create a mock that raises an exception on the first call
        mock_create_loss = MagicMock()
        mock_create_loss.side_effect = [
            ValueError(
                "Unsupported loss function: nonexistent_loss"
            ),  # First call raises an exception
            nn.BCEWithLogitsLoss(),  # Second call returns a default loss function
        ]

        # Mock create_loss_function to use our controlled mock
        with mock.patch("src.train.trainer.create_loss_function", mock_create_loss):
            # Mock logger to check error message
            with mock.patch.object(trainer.logger, "error") as mock_error:
                # Initialize the loss function - should not raise an exception
                loss_fn = trainer._init_loss_function(config)

                # Verify that an error was logged
                mock_error.assert_called_once()

                # Verify that a default loss function was created
                assert loss_fn is not None

    def test_broken_loss_function_invalid_config_params(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that trainer handles invalid loss function config parameters."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add required model_run_uuid and loss function with invalid config parameters
        training_config["model_run_uuid"] = "test-robustness-uuid-6"
        training_config["model_version_parameters"] = {
            "loss_function": {
                "type": "bce",
                "config": {"reduction": "invalid_reduction", "nonexistent_param": 42},
            }
        }

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Create a mock that raises an exception on the first call
        mock_create_loss = MagicMock()
        mock_create_loss.side_effect = [
            ValueError(
                "reduction must be one of: 'none', 'mean', 'sum'"
            ),  # First call raises an exception
            nn.BCEWithLogitsLoss(),  # Second call returns a default loss function
        ]

        # Mock create_loss_function to use our controlled mock
        with mock.patch("src.train.trainer.create_loss_function", mock_create_loss):
            # Mock logger to check error message
            with mock.patch.object(trainer.logger, "error") as mock_error:
                # Initialize the loss function - should not raise an exception
                loss_fn = trainer._init_loss_function(config)

                # Verify that an error was logged
                mock_error.assert_called_once()

                # Verify that a default loss function was created
                assert loss_fn is not None

    def test_broken_model_version_parameters_none(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that trainer handles None as model_version_parameters value."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add required model_run_uuid and set model_version_parameters to None
        training_config["model_run_uuid"] = "test-robustness-uuid-7"
        training_config["model_version_parameters"] = None

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock logger to check info message
        with mock.patch.object(trainer.logger, "info") as mock_info:
            # Initialize the loss function - should not raise an exception
            loss_fn = trainer._init_loss_function(config)

            # Verify that a default loss function was created
            assert loss_fn is not None

            # Verify that info about using default loss was logged
            assert any(
                "Using default BCE loss function" in call[0][0]
                for call in mock_info.call_args_list
            )

    def test_broken_training_config_empty(self, model_components, mock_data_loaders):
        """Test that trainer handles empty training_config value."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        with tempfile.TemporaryDirectory() as temp_dir:
            # Create config with minimal training_config including required model_run_uuid
            config = TrainerConfig(
                model_components=model_components,
                data_loaders=mock_data_loaders,
                training_config={
                    "model_run_uuid": "test-robustness-uuid-8",
                    "run_output_dir": temp_dir,
                },
            )

            trainer = ModelTrainer(config)

            # Mock logger to check info message
            with mock.patch.object(trainer.logger, "info") as mock_info:
                # Initialize the loss function - should not raise an exception
                loss_fn = trainer._init_loss_function(config)

                # Verify that a default loss function was created
                assert loss_fn is not None

                # Verify that info about using default loss was logged
                assert any(
                    "Using default BCE loss function" in call[0][0]
                    for call in mock_info.call_args_list
                )

    def test_init_from_config_with_broken_values(
        self, model_components, mock_data_loaders
    ):
        """Test that _init_from_config handles broken values without crashing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a config with various broken values
            broken_training_config = {
                "model_id": None,  # Missing model_id
                "model_run_uuid": "test-robustness-uuid-9",  # Required field
                "run_output_dir": temp_dir,
                "epochs": "not_a_number",  # Wrong type for epochs
                "model_version_parameters": {
                    "loss_function": "invalid_format",  # Wrong format
                    "optimizer": None,  # Missing optimizer
                    "learning_rate": "not_a_number",  # Wrong type for learning rate
                },
                "model_run_parameters": None,  # Missing model run parameters
            }

            config = TrainerConfig(
                model_components=model_components,
                data_loaders=mock_data_loaders,
                training_config=broken_training_config,
            )

            # Mock all the initialization methods to isolate the test
            with mock.patch.object(
                ModelTrainer, "_init_loss_function", return_value=nn.BCEWithLogitsLoss()
            ):
                # Note: _collect_resource_metrics is now in MetricsCollectionCallback
                # Should not raise any exceptions
                trainer = ModelTrainer(config)

                # Basic assertions to verify trainer was initialized
                assert trainer.components.model == model_components["model"]
                assert trainer.components.optimizer == model_components["optimizer"]
                assert trainer.data_loaders == mock_data_loaders
