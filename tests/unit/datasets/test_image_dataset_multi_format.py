"""
Tests for multi-format image support in ImageDataset.

This module tests the enhanced ImageDataset functionality for handling
multiple image formats including format detection, validation, and performance monitoring.
"""

# pylint: disable=protected-access

from unittest.mock import MagicMock, patch

import pytest

from datasets.image_dataset import (
    FormatPerformanceMetrics,
    ImageDataset,
    ImageDatasetConfig,
)
from tests.fixtures.data import create_dataset_sets_for_background_tests
from utils.image_utils import find_image_file_with_extensions


class TestImageDatasetMultiFormat:
    """Test multi-format support in ImageDataset."""

    def test_image_dataset_config_defaults(self):
        """Test that ImageDatasetConfig has correct multi-format defaults."""
        config = ImageDatasetConfig()

        assert config.quality_threshold == 85
        assert config.max_file_size_mb == 10
        assert "jpg" in config.supported_extensions
        assert "png" in config.supported_extensions
        assert "webp" in config.supported_extensions
        assert "heic" in config.supported_extensions
        assert "hevc" in config.supported_extensions

    def test_image_dataset_config_customization(self):
        """Test that multi-format settings can be customized."""
        config = ImageDatasetConfig(
            supported_extensions=["png", "jpg"],
            quality_threshold=95,
            max_file_size_mb=5,
        )

        assert config.supported_extensions == ["png", "jpg"]
        assert config.quality_threshold == 95
        assert config.max_file_size_mb == 5

    def test_find_image_file_finds_any_supported_format(self, tmp_path):
        """Test that find_image_file_with_extensions finds any supported format."""
        # Create test files
        test_dir = tmp_path / "test_coin"
        test_dir.mkdir()

        # Create multiple format files
        (test_dir / "image123.jpg").touch()
        (test_dir / "image123.png").touch()
        (test_dir / "image123.webp").touch()

        # Should find one of the files (order depends on supported_extensions list)
        found_path = find_image_file_with_extensions(
            base_dir=tmp_path,
            image_uuid="image123",
            coin_side_uuid="test_coin",
        )
        assert found_path is not None
        assert found_path.name.startswith("image123.")
        assert found_path.suffix.lstrip(".") in ["jpg", "png", "webp"]

    def test_find_image_file_finds_available_format(self, tmp_path):
        """Test that find_image_file_with_extensions finds available formats."""
        # Create test files
        test_dir = tmp_path / "test_coin"
        test_dir.mkdir()

        # Create only WebP file
        (test_dir / "image123.webp").touch()

        # Should find WebP file when it's the only one available
        found_path = find_image_file_with_extensions(
            base_dir=tmp_path,
            image_uuid="image123",
            coin_side_uuid="test_coin",
        )
        assert found_path == test_dir / "image123.webp"

    def test_find_image_file_returns_none_when_not_found(self, tmp_path):
        """Test that _find_image_file returns None when no file is found."""
        test_dir = tmp_path / "test_coin"
        test_dir.mkdir()

        # Should return None when file doesn't exist
        found_path = find_image_file_with_extensions(
            base_dir=tmp_path, image_uuid="nonexistent", coin_side_uuid="test_coin"
        )
        assert found_path is None

    def test_find_image_file_with_limited_extensions(self, tmp_path):
        """Test that find_image_file_with_extensions works with limited extension list."""
        test_dir = tmp_path / "test_coin"
        test_dir.mkdir()

        # Create JPG file
        (test_dir / "image123.jpg").touch()

        # Create PNG file that should be ignored
        (test_dir / "image123.png").touch()

        # Should use only JPG when limited to that extension
        found_path = find_image_file_with_extensions(
            base_dir=tmp_path,
            image_uuid="image123",
            coin_side_uuid="test_coin",
            supported_extensions=["jpg"],
        )
        assert found_path == test_dir / "image123.jpg"

    @patch("PIL.Image.open")
    def test_load_heic_image_success(self, mock_image_open, tmp_path):
        """Test successful HEIC image loading."""
        # Mock the HEIC loading
        mock_image = MagicMock()
        mock_image_open.return_value = mock_image

        with patch("datasets.image_dataset.register_heif_opener") as mock_register:
            # Create a minimal ImageDataset instance just to test the _load_heic_image method
            config = ImageDatasetConfig()
            # Use empty dataset_sets since we're only testing the _load_heic_image method
            dataset = ImageDataset([], tmp_path, config)

            test_path = tmp_path / "test.heic"
            result = dataset._load_heic_image(test_path)

            mock_register.assert_called_once()
            mock_image_open.assert_called_once_with(test_path)
            assert result == mock_image

    def test_load_heic_image_import_error(self, tmp_path):
        """Test HEIC image loading when pillow-heif is not available."""
        config = ImageDatasetConfig()
        # Use empty dataset_sets since we're only testing the _load_heic_image method
        dataset = ImageDataset([], tmp_path, config)

        test_path = tmp_path / "test.heic"
        # Create a dummy file so the file exists
        test_path.write_bytes(b"dummy heic content")

        with patch(
            "datasets.image_dataset.register_heif_opener",
            side_effect=ImportError("No module named 'pillow_heif'"),
        ):
            with pytest.raises(Exception) as exc_info:
                dataset._load_heic_image(test_path)

            assert "HEIC support not available" in str(exc_info.value)

    @patch("PIL.Image.open")
    def test_load_webp_image_with_transparency(self, mock_image_open, tmp_path):
        """Test WebP image loading with transparency handling."""
        # Mock WebP image with alpha channel
        mock_image = MagicMock()
        mock_image.mode = "RGBA"
        mock_image.size = (100, 100)
        mock_image.split.return_value = [
            MagicMock(),
            MagicMock(),
            MagicMock(),
            MagicMock(),
        ]  # RGBA channels
        mock_image_open.return_value = mock_image

        # Mock the background image creation
        mock_background = MagicMock()
        with patch("PIL.Image.new", return_value=mock_background) as mock_new:
            config = ImageDatasetConfig()
            dataset_sets = create_dataset_sets_for_background_tests(
                dataset_uuid="test-uuid", labeled_count=1, background_count=0
            )
            dataset = ImageDataset(dataset_sets, tmp_path, config)

            test_path = tmp_path / "test.webp"
            result = dataset._load_webp_image(test_path)

            mock_new.assert_called_once_with("RGB", (100, 100), (255, 255, 255))
            mock_background.paste.assert_called_once()
            assert result == mock_background

    def test_performance_metrics_initialization(self):
        """Test that FormatPerformanceMetrics initializes correctly."""
        metrics = FormatPerformanceMetrics()

        assert isinstance(metrics.format_loading_times, dict)
        assert isinstance(metrics.format_memory_usage, dict)
        assert isinstance(metrics.format_error_rates, dict)
        assert isinstance(metrics.format_file_sizes, dict)
        assert len(metrics.format_loading_times) == 0

    def test_track_loading_performance_success(self, tmp_path):
        """Test performance tracking for successful image loading."""
        config = ImageDatasetConfig()
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid="test-uuid", labeled_count=1, background_count=0
        )
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Create a test file to get file size
        test_path = tmp_path / "test.jpg"
        test_path.write_bytes(b"fake image data" * 1000)  # Create some file content

        dataset._track_loading_performance(test_path, 0.5, True)

        assert hasattr(dataset, "performance_metrics")
        assert "jpg" in dataset.performance_metrics.format_loading_times
        assert dataset.performance_metrics.format_loading_times["jpg"] == [0.5]
        assert "jpg" in dataset.performance_metrics.format_file_sizes
        assert len(dataset.performance_metrics.format_file_sizes["jpg"]) == 1

    def test_track_loading_performance_failure(self, tmp_path):
        """Test performance tracking for failed image loading."""
        config = ImageDatasetConfig()
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid="test-uuid", labeled_count=1, background_count=0
        )
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        test_path = tmp_path / "test.png"
        dataset._track_loading_performance(test_path, 0.0, False)

        assert hasattr(dataset, "performance_metrics")
        assert dataset.performance_metrics.format_error_rates["png"] == 1
        assert "png" not in dataset.performance_metrics.format_loading_times

    def test_get_format_performance_report_no_data(self, tmp_path):
        """Test performance report when no data is available."""
        config = ImageDatasetConfig()
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid="test-uuid", labeled_count=1, background_count=0
        )
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        report = dataset.get_format_performance_report()
        assert "message" in report
        assert "No performance data available" in report["message"]

    def test_get_format_performance_report_with_data(self, tmp_path):
        """Test performance report generation with actual data."""
        config = ImageDatasetConfig()
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid="test-uuid", labeled_count=1, background_count=0
        )
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Add some performance data
        test_path_jpg = tmp_path / "test.jpg"
        test_path_jpg.write_bytes(b"fake data" * 100)
        test_path_png = tmp_path / "test.png"
        test_path_png.write_bytes(b"fake data" * 200)

        dataset._track_loading_performance(test_path_jpg, 0.1, True)
        dataset._track_loading_performance(test_path_jpg, 0.2, True)
        dataset._track_loading_performance(test_path_png, 0.3, False)

        report = dataset.get_format_performance_report()

        assert "jpg" in report
        assert "png" in report
        assert abs(report["jpg"]["avg_loading_time"] - 0.15) < 1e-10  # (0.1 + 0.2) / 2
        assert report["jpg"]["total_files"] == 2
        assert report["jpg"]["error_count"] == 0
        assert report["jpg"]["success_rate"] == 1.0
        assert report["png"]["error_count"] == 1
        assert report["png"]["success_rate"] == 0.0

    @patch("PIL.Image.open")
    def test_validate_dataset_formats_success(self, mock_image_open, tmp_path):
        """Test dataset format validation with valid images."""
        # Mock successful image opening
        mock_image = MagicMock()
        mock_image.format = "JPEG"
        mock_image_open.return_value.__enter__.return_value = mock_image

        # Create test files
        test_dir = tmp_path / "test_coin"
        test_dir.mkdir()
        test_file = test_dir / "image123.jpg"
        test_file.write_bytes(b"fake jpeg data" * 100)  # Small file

        config = ImageDatasetConfig()
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid="test-uuid",
            labeled_count=1,
            background_count=0,
            image_urls=["http://example.com/image123.jpg"],
        )
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Override image paths to point to our test file (for unit testing)
        dataset.image_paths = [test_file]

        results = dataset.validate_dataset_formats()

        assert results["total_images"] == 1
        assert results["format_distribution"]["JPEG"] == 1
        assert len(results["corrupted_files"]) == 0
        assert len(results["unsupported_formats"]) == 0
        assert len(results["large_files"]) == 0

    def test_validate_dataset_formats_missing_files(self, tmp_path):
        """Test dataset format validation with missing files."""
        config = ImageDatasetConfig()
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid="test-uuid",
            labeled_count=1,
            background_count=0,
            image_urls=["http://example.com/nonexistent.jpg"],
        )
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Override image paths to point to non-existent files
        dataset.image_paths = [tmp_path / "nonexistent.jpg"]

        results = dataset.validate_dataset_formats()

        assert results["total_images"] == 1
        assert len(results["unsupported_formats"]) == 1
        assert len(results["corrupted_files"]) == 0

    @patch("PIL.Image.open")
    def test_validate_dataset_formats_large_files(self, mock_image_open, tmp_path):
        """Test dataset format validation with large files."""
        # Mock successful image opening
        mock_image = MagicMock()
        mock_image.format = "PNG"
        mock_image_open.return_value.__enter__.return_value = mock_image

        # Create large test file (>10MB)
        test_dir = tmp_path / "test_coin"
        test_dir.mkdir()
        test_file = test_dir / "large_image.png"
        test_file.write_bytes(b"x" * (11 * 1024 * 1024))  # 11MB file

        config = ImageDatasetConfig(max_file_size_mb=10)
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid="test-uuid",
            labeled_count=1,
            background_count=0,
            image_urls=["http://example.com/large_image.png"],
        )
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Override image paths to point to our test file
        dataset.image_paths = [test_file]

        results = dataset.validate_dataset_formats()

        assert results["total_images"] == 1
        assert len(results["large_files"]) == 1
        assert results["large_files"][0]["size_mb"] > 10
        assert results["large_files"][0]["format"] == "PNG"

    @patch("PIL.Image.open")
    def test_validate_dataset_formats_corrupted_files(self, mock_image_open, tmp_path):
        """Test dataset format validation with corrupted files."""
        # Mock image opening failure
        mock_image_open.side_effect = Exception("Corrupted image")

        # Create test file
        test_dir = tmp_path / "test_coin"
        test_dir.mkdir()
        test_file = test_dir / "corrupted.jpg"
        test_file.write_bytes(b"not an image")

        config = ImageDatasetConfig()
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid="test-uuid",
            labeled_count=1,
            background_count=0,
            image_urls=["http://example.com/corrupted.jpg"],
        )
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Override image paths to point to our test file
        dataset.image_paths = [test_file]

        results = dataset.validate_dataset_formats()

        assert results["total_images"] == 1
        assert len(results["corrupted_files"]) == 1
        assert "Corrupted image" in results["corrupted_files"][0]["error"]
