"""
Tests for multi-format support in DataLoadingService.

This module tests the integration of multi-format image support in the data loading service.
"""

from unittest.mock import patch
from uuid import uuid4

from datasets.data_loading_service import (
    BackgroundClassConfig,
    DataLoaderConfig,
    DataLoadingService,
    FormatConfig,
    ImageDatasetParams,
)
from datasets.image_dataset import ImageDatasetConfig
from tests.fixtures.data import create_dataset_sets_for_background_tests


class TestDataLoadingServiceMultiFormat:
    """Test multi-format support in DataLoadingService."""

    def test_data_loader_config_image_format_defaults(self):
        """Test that DataLoaderConfig has correct image format defaults."""
        config = DataLoaderConfig(model_run_uuid="test-uuid")

        assert config.format_config.image_config is None
        assert config.format_config.format_validation is True
        assert config.format_config.performance_monitoring is False

    def test_data_loader_config_image_format_customization(self):
        """Test that image format settings can be customized."""

        image_config = ImageDatasetConfig(
            supported_extensions=["png", "jpg", "webp"],
        )

        format_config = FormatConfig(
            image_config=image_config,
            format_validation=False,
            performance_monitoring=True,
        )

        config = DataLoaderConfig(
            model_run_uuid="test-uuid",
            format_config=format_config,
        )

        assert config.format_config.image_config == image_config
        assert config.format_config.format_validation is False
        assert config.format_config.performance_monitoring is True

    def test_create_image_dataset_uses_provided_image_config(self, tmp_path):
        """Test that _create_image_dataset uses provided image config."""
        # Create custom image config
        image_config = ImageDatasetConfig(
            supported_extensions=["png", "jpg"],
            quality_threshold=95,
        )

        # Create dataset sets
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid, labeled_count=1, background_count=0
        )

        # Create config with custom image settings
        config = DataLoaderConfig(
            model_run_uuid="test-uuid",
            format_config=FormatConfig(image_config=image_config),
            background_config=BackgroundClassConfig(
                include_background_class=True, background_class_name="custom_background"
            ),
        )

        # Create dataset
        params = ImageDatasetParams(
            dataset_sets=dataset_sets,
            dataset_uuid=str(uuid4()),
            content_updated_at=None,
            set_name="test",
            config=config,
            base_dir=str(tmp_path),
        )

        # pylint: disable=protected-access
        dataset = DataLoadingService._create_image_dataset(params)

        # Verify that the dataset was created with custom image config
        assert dataset.config.supported_extensions == ["png", "jpg"]
        assert dataset.config.quality_threshold == 95

        # Verify background class settings are still applied
        assert dataset.config.include_background_class is True
        assert dataset.config.background_class_name == "custom_background"

    def test_create_image_dataset_default_image_config(self, tmp_path):
        """Test that _create_image_dataset creates default image config when none provided."""
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid, labeled_count=1, background_count=0
        )

        config = DataLoaderConfig(
            model_run_uuid="test-uuid",
            background_config=BackgroundClassConfig(
                include_background_class=False, background_class_name="no_object"
            ),
        )

        params = ImageDatasetParams(
            dataset_sets=dataset_sets,
            dataset_uuid=str(uuid4()),
            content_updated_at=None,
            set_name="test",
            config=config,
            base_dir=str(tmp_path),
        )

        # pylint: disable=protected-access
        dataset = DataLoadingService._create_image_dataset(params)

        # Verify that default image config was created
        assert "jpg" in dataset.config.supported_extensions  # Default includes jpg

        # Verify background class settings are applied
        assert dataset.config.include_background_class is False
        assert dataset.config.background_class_name == "no_object"

    @patch("datasets.image_dataset.ImageDataset.validate_dataset_formats")
    def test_create_image_dataset_format_validation_enabled(
        self, mock_validate, tmp_path
    ):
        """Test that format validation is called when enabled."""
        mock_validate.return_value = {
            "format_distribution": {"JPEG": 1},
            "corrupted_files": [],
            "unsupported_formats": [],
            "large_files": [],
            "total_images": 1,
        }

        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid, labeled_count=1, background_count=0
        )

        config = DataLoaderConfig(
            model_run_uuid="test-uuid",
            format_config=FormatConfig(format_validation=True),
        )

        params = ImageDatasetParams(
            dataset_sets=dataset_sets,
            dataset_uuid=str(uuid4()),
            content_updated_at=None,
            set_name="test",
            config=config,
            base_dir=str(tmp_path),
        )

        # pylint: disable=protected-access
        dataset = DataLoadingService._create_image_dataset(params)

        # Verify that the dataset was created successfully
        assert dataset is not None
        assert len(dataset) >= 0  # Should have valid length

        # Verify that format validation was called
        mock_validate.assert_called_once()

    @patch("datasets.image_dataset.ImageDataset.validate_dataset_formats")
    def test_create_image_dataset_format_validation_disabled(
        self, mock_validate, tmp_path
    ):
        """Test that format validation is not called when disabled."""
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid, labeled_count=1, background_count=0
        )

        config = DataLoaderConfig(
            model_run_uuid="test-uuid",
            format_config=FormatConfig(format_validation=False),
        )

        params = ImageDatasetParams(
            dataset_sets=dataset_sets,
            dataset_uuid=str(uuid4()),
            content_updated_at=None,
            set_name="test",
            config=config,
            base_dir=str(tmp_path),
        )

        # pylint: disable=protected-access
        dataset = DataLoadingService._create_image_dataset(params)

        # Verify that the dataset was created successfully
        assert dataset is not None
        assert len(dataset) >= 0  # Should have valid length

        # Verify that format validation was not called
        mock_validate.assert_not_called()

    def test_log_format_validation_results_success(self, caplog):
        """Test logging of successful format validation results."""
        results = {
            "format_distribution": {"JPEG": 5, "PNG": 3},
            "corrupted_files": [],
            "unsupported_formats": [],
            "large_files": [],
            "total_images": 8,
        }

        with caplog.at_level("INFO"):
            # pylint: disable=protected-access
            DataLoadingService._log_format_validation_results(results, "train")

        # Check that success information is logged
        assert "Format validation for train dataset:" in caplog.text
        assert "Format distribution: {'JPEG': 5, 'PNG': 3}" in caplog.text

    def test_log_format_validation_results_with_issues(self, caplog):
        """Test logging of format validation results with issues."""
        results = {
            "format_distribution": {"JPEG": 2},
            "corrupted_files": [
                {"path": "/path/to/corrupt.jpg", "error": "Cannot identify image file"}
            ],
            "unsupported_formats": ["missing_image_uuid"],
            "large_files": [
                {"path": "/path/to/large.png", "size_mb": 15.5, "format": "PNG"}
            ],
            "total_images": 4,
        }

        with caplog.at_level("WARNING"):
            # pylint: disable=protected-access
            DataLoadingService._log_format_validation_results(results, "validation")

        # Check that warnings are logged for issues
        assert "Found 1 corrupted files" in caplog.text
        assert "Found 1 unsupported formats" in caplog.text
        assert "Found 1 large files (>10MB)" in caplog.text

    def test_create_image_dataset_logging_includes_dataset_info(self, tmp_path, caplog):
        """Test that logging includes dataset creation information."""
        dataset_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid, labeled_count=1, background_count=0
        )

        # Test with format detection (always enabled)
        image_config = ImageDatasetConfig()

        config = DataLoaderConfig(
            model_run_uuid="test-uuid",
            format_config=FormatConfig(
                image_config=image_config,
                format_validation=False,  # Disable to avoid validation logs
            ),
        )

        with caplog.at_level("INFO"):
            params = ImageDatasetParams(
                dataset_sets=dataset_sets,
                dataset_uuid=str(uuid4()),
                content_updated_at=None,
                set_name="test",
                config=config,
                base_dir=str(tmp_path),
            )
            # pylint: disable=protected-access
            dataset = DataLoadingService._create_image_dataset(params)

        # Verify dataset was created and logging includes relevant information
        assert dataset is not None
        assert "Created test dataset" in caplog.text
        assert "background class: enabled" in caplog.text
