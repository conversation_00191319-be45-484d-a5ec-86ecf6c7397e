"""
Tests for background class functionality in ImageDataset.

This module tests the ability to include images with no labels (null coin_side_uuid)
as a special background class in the dataset.
"""

from uuid import uuid4

from config.paths import get_background_dir_name
from datasets.image_dataset import ImageDataset, ImageDatasetConfig
from tests.fixtures.data import create_dataset_sets_for_background_tests


class TestBackgroundClass:
    """Test background class functionality in ImageDataset."""

    def test_background_class_enabled_by_default(self):
        """Test that background class is enabled by default in config."""
        config = ImageDatasetConfig()
        assert config.include_background_class is True
        assert config.background_class_name == "background"

    def test_background_class_can_be_disabled(self):
        """Test that background class can be disabled."""
        config = ImageDatasetConfig(include_background_class=False)
        assert config.include_background_class is False

    def test_label_mapping_with_background_images(self, tmp_path):
        """Test label mapping creation when background images are present."""
        # Create test dataset sets with mixed labeled and unlabeled images
        dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=2, background_count=1
        )

        config = ImageDatasetConfig(include_background_class=True)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Should have 2 coin sides + 1 background class = 3 total classes
        assert dataset.get_num_classes() == 3

        # Background class should be present in label mapping
        label_mapping = dataset.get_label_mapping()
        assert "background" in label_mapping

        # Background class should have the highest label value
        background_label = label_mapping["background"]
        assert background_label == 2  # 0, 1 for coin sides, 2 for background

    def test_label_mapping_without_background_images(self, tmp_path):
        """Test label mapping when no background images are present."""
        # Create test dataset sets with only labeled images
        dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=2, background_count=0
        )

        config = ImageDatasetConfig(include_background_class=True)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Should have only 2 coin sides, no background class
        assert dataset.get_num_classes() == 2

        # Background class should not be present in label mapping
        label_mapping = dataset.get_label_mapping()
        assert "background" not in label_mapping

    def test_background_class_disabled_filters_unlabeled_images(self, tmp_path):
        """Test that unlabeled images are filtered out when background class is disabled."""
        dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=1,
            background_count=1,
            image_urls=[
                "http://example.com/labeled.jpg",
                "http://example.com/background.jpg",
            ],
        )

        config = ImageDatasetConfig(include_background_class=False)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Should have only 1 valid image (the labeled one)
        assert len(dataset) == 1
        assert dataset.get_num_classes() == 1

    def test_background_image_path_construction(self, tmp_path):
        """Test that background images are stored in the correct directory."""
        dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=0,
            background_count=1,
            image_urls=["http://example.com/test_image.jpg"],
        )
        # Override image_uuid for this specific test
        dataset_sets[0].image_uuid = "test_image"

        config = ImageDatasetConfig(include_background_class=True)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Check that the image path is constructed correctly
        expected_path = tmp_path / get_background_dir_name() / "test_image.jpg"
        assert dataset.image_paths[0] == expected_path

    def test_labeled_image_path_construction(self, tmp_path):
        """Test that labeled images are still stored in coin_side_uuid directories."""
        coin_side_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=1,
            background_count=0,
            image_urls=["http://example.com/test_image.jpg"],
        )
        # Override specific values for this test
        dataset_sets[0].coin_side_uuid = coin_side_uuid
        dataset_sets[0].image_uuid = "test_image"

        config = ImageDatasetConfig(include_background_class=True)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Check that the image path is constructed correctly
        expected_path = tmp_path / coin_side_uuid / "test_image.jpg"
        assert dataset.image_paths[0] == expected_path

    def test_custom_background_class_name(self, tmp_path):
        """Test using a custom background class name."""
        dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=0, background_count=1
        )

        config = ImageDatasetConfig(
            include_background_class=True, background_class_name="no_object"
        )
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Should use custom background class name
        label_mapping = dataset.get_label_mapping()
        assert "no_object" in label_mapping
        assert "background" not in label_mapping

    def test_dataset_info_includes_background_counts(self, tmp_path):
        """Test that dataset info includes background image counts."""
        dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=1,
            background_count=2,
            image_urls=[
                "http://example.com/labeled.jpg",
                "http://example.com/bg1.jpg",
                "http://example.com/bg2.jpg",
            ],
        )

        config = ImageDatasetConfig(include_background_class=True)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        info = dataset.get_dataset_info()

        # Should have background in coin_side_counts
        assert "background" in info["coin_side_counts"]
        assert info["coin_side_counts"]["background"] == 2

    def test_class_names_includes_background(self, tmp_path):
        """Test that get_class_names includes background class."""
        coin_side_uuid = str(uuid4())
        dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=1, background_count=1
        )
        # Override coin_side_uuid for the labeled image
        dataset_sets[0].coin_side_uuid = coin_side_uuid

        config = ImageDatasetConfig(include_background_class=True)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        class_names = dataset.get_class_names()

        # Should include both coin side and background
        assert len(class_names) == 2
        assert coin_side_uuid in class_names
        assert "background" in class_names

        # Background should be last (highest label value)
        assert class_names[-1] == "background"

    def test_different_image_extensions_in_dataset(self, tmp_path):
        """Test that ImageDataset handles different image extensions correctly."""
        coin_side_uuid = str(uuid4())

        # Create dataset sets with specific image URLs for extension testing
        image_urls = [
            "http://example.com/image.jpg",
            "http://example.com/image.png",
            "http://example.com/background.webp",
            "http://example.com/background.heic",
        ]
        dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=2, background_count=2, image_urls=image_urls
        )
        # Override coin_side_uuid for labeled images
        dataset_sets[0].coin_side_uuid = coin_side_uuid
        dataset_sets[1].coin_side_uuid = coin_side_uuid

        config = ImageDatasetConfig(include_background_class=True)
        dataset = ImageDataset(dataset_sets, tmp_path, config)

        # Verify all images are included
        assert len(dataset) == 4

        # Check that image paths have correct extensions
        # Note: fixture generates bg_image_2 for second background image (not heic_bg)
        expected_paths = [
            tmp_path / coin_side_uuid / "jpg_image.jpg",
            tmp_path / coin_side_uuid / "png_image.png",
            tmp_path / "background" / "webp_bg.webp",
            tmp_path / "background" / "bg_image_2.heic",
        ]

        for expected_path in expected_paths:
            assert (
                expected_path in dataset.image_paths
            ), f"Expected path {expected_path} not found in {dataset.image_paths}"
