"""
Unit tests for model creation and functionality.
"""

import torch

from src.models import MLModelFactory


def test_dynamic_cnn_output_shape():
    """Test that the dynamic CNN produces output with the correct shape."""
    batch_size = 32
    image_channels = 1
    image_size = 91
    num_classes = 1  # For binary classification with BCEWithLogitsLoss

    # Define sample model parameters (similar to manual test script)
    model_version_params_dict = {
        "convolutional_layers": [[16, 3, 1, 1], [32, 3, 1, 1]],
        "fully_connected_layers": [64],
        "activation": "relu",
        "batch_norm": True,
        "pooling": ["max", 2, 2],
    }
    model_run_params_dict = {"dropout_rate": 0.3}

    cnn_model_params = {
        "name": "CNN",
        "parameters": {
            "model_version": {"parameters": model_version_params_dict},
            "model_run": {"parameters": model_run_params_dict},
            "image_size": image_size,
            "image_channels": image_channels,
        },
    }

    model_factory = (
        MLModelFactory()
    )  # Assuming no persistence_base_dir needed for this test
    model = model_factory.create_model(
        architecture_params=cnn_model_params, num_classes=num_classes
    )

    # Create a dummy input batch
    dummy_input = torch.randn(batch_size, image_channels, image_size, image_size)

    # Perform a forward pass
    model.eval()  # Set model to evaluation mode
    with torch.no_grad():
        output = model(dummy_input)

    # Assert the output shape is correct
    expected_shape = (batch_size, num_classes)
    assert (
        output.shape == expected_shape
    ), f"Expected output shape {expected_shape}, but got {output.shape}"
