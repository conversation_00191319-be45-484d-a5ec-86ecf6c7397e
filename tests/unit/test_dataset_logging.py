"""
Unit tests for dataset logging functionality.
"""

# pylint: disable=line-too-long

import logging
import tempfile
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import patch
from uuid import uuid4

import pytest

from config.logging_config import setup_dataset_logger, setup_loggers
from database.services.dataset_service import DatasetService
from database.services.dataset_service import logger as dataset_service_logger
from src.config.paths import get_run_paths
from tests.fixtures.data import get_sample_dataset_data, get_sample_dataset_sets


@dataclass
class DatasetTestParams:
    """Parameters for dataset logging tests."""

    temp_logs_dir: Path
    sample_dataset_uuid: str
    sample_dataset_data: Dict[str, Any]
    sample_dataset_sets: List[Dict[str, Any]]


class TestDatasetLogging:
    """Test cases for dataset logging functionality."""

    @pytest.fixture
    def temp_logs_dir(self):
        """Create a temporary directory for log files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def sample_dataset_uuid(self):
        """Sample dataset UUID for testing."""
        return str(uuid4())

    @pytest.fixture
    def sample_dataset_data(self, sample_dataset_uuid):
        """Sample dataset data for testing."""
        return get_sample_dataset_data(
            custom_values={
                "uuid": sample_dataset_uuid,
                "name": "test-logging-dataset",
                "images_count": 2,
                "created_at": "2023-01-01T00:00:00",
                "content_updated_at": "2023-01-01T00:00:00",
            }
        )

    @pytest.fixture
    def sample_dataset_sets(self, sample_dataset_uuid):
        """Sample dataset sets for testing."""
        return get_sample_dataset_sets(
            count=2,
            custom_values_list=[
                {
                    "uuid": str(uuid4()),
                    "dataset_uuid": sample_dataset_uuid,
                    "coin_side_uuid": str(uuid4()),
                    "image_uuid": "img-1",
                    "created_at": "2023-01-01T00:00:00",
                    "set_type": 1,
                    "images_reviews": {"image_url": "https://example.com/image1.jpg"},
                },
                {
                    "uuid": str(uuid4()),
                    "dataset_uuid": sample_dataset_uuid,
                    "coin_side_uuid": str(uuid4()),
                    "image_uuid": "img-2",
                    "created_at": "2023-01-01T00:00:00",
                    "set_type": 1,
                    "images_reviews": {"image_url": "https://example.com/image2.jpg"},
                },
            ],
        )

    @pytest.fixture
    def test_params(
        self,
        temp_logs_dir,
        sample_dataset_uuid,
        sample_dataset_data,
        sample_dataset_sets,
    ):
        """Combined test parameters."""
        return DatasetTestParams(
            temp_logs_dir=temp_logs_dir,
            sample_dataset_uuid=sample_dataset_uuid,
            sample_dataset_data=sample_dataset_data,
            sample_dataset_sets=sample_dataset_sets,
        )

    def test_setup_loggers_creates_dataset_logger(self):
        """Test that setup_loggers creates a dataset logger with file handler."""
        model_run_uuid = "test-model-run-123"

        # Setup loggers with model_run_uuid
        loggers = setup_loggers(model_run_uuid)

        # Verify dataset logger exists
        assert "dataset" in loggers
        dataset_logger = loggers["dataset"]
        assert isinstance(dataset_logger, logging.Logger)
        assert dataset_logger.name == "dataset"

        # Test logging to verify file handler works
        dataset_logger.info("Test log message")

        # Verify log file is created in the correct isolated location
        # The logs will be created in the isolated runs directory, not temp_logs_dir
        run_paths = get_run_paths(model_run_uuid)
        expected_log_file = run_paths.logs / "dataset.log"

        # Check that log file exists and contains the message
        assert expected_log_file.exists()
        log_content = expected_log_file.read_text()
        assert "Test log message" in log_content

    def test_dataset_service_uses_dataset_logger(self):
        """Test that DatasetService uses the dataset logger."""
        # Get the logger used by DatasetService
        # Verify it's the dataset logger
        assert dataset_service_logger.name == "dataset"

    @patch("database.services.dataset_service.fetch_data")
    async def test_dataset_service_logs_operations(self, mock_fetch_data, test_params):
        """Test that dataset service operations are logged."""
        # Create a temporary log file to capture logs
        log_file = test_params.temp_logs_dir / "test_dataset.log"

        # Clear any existing handlers and set up fresh logging
        dataset_logger = dataset_service_logger
        # Remove existing handlers
        for handler in dataset_logger.handlers[:]:
            dataset_logger.removeHandler(handler)

        # Add our test file handler
        temp_handler = logging.FileHandler(log_file)
        temp_handler.setLevel(logging.INFO)
        formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        temp_handler.setFormatter(formatter)
        dataset_logger.addHandler(temp_handler)
        dataset_logger.setLevel(logging.INFO)

        try:
            # Setup mock responses
            mock_fetch_data.side_effect = [
                [test_params.sample_dataset_data],  # get_dataset call
                [{"count": 2}],  # count call for get_dataset_sets
                test_params.sample_dataset_sets,  # data call for get_dataset_sets
            ]

            # Call verify_and_recover to trigger logging
            await DatasetService.verify_and_recover_dataset_images(
                dataset_uuid=test_params.sample_dataset_uuid,
                base_output_dir=test_params.temp_logs_dir,
                auto_recover=False,  # Disable recovery to avoid download attempts
            )

            # Verify that logging occurred by checking the log file
            assert log_file.exists()
            log_content = log_file.read_text()
            assert len(log_content) > 0

            # Check for specific log messages
            assert "Auto-recovery disabled" in log_content
            assert "Dataset verification complete" in log_content

        finally:
            # Clean up the temporary handler
            dataset_logger.removeHandler(temp_handler)
            temp_handler.close()

    @patch("database.services.dataset_service.fetch_data")
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.recover_missing_dataset_images"  # noqa: E501
    )
    async def test_dataset_service_logs_recovery_attempts(
        self, mock_download_image, mock_fetch_data, test_params
    ):
        """Test that dataset recovery attempts are logged."""
        # Create a temporary log file to capture logs
        log_file = test_params.temp_logs_dir / "test_recovery.log"

        # Clear any existing handlers and set up fresh logging
        dataset_logger = dataset_service_logger
        # Remove existing handlers
        for handler in dataset_logger.handlers[:]:
            dataset_logger.removeHandler(handler)

        # Add our test file handler
        temp_handler = logging.FileHandler(log_file)
        temp_handler.setLevel(logging.INFO)
        formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        temp_handler.setFormatter(formatter)
        dataset_logger.addHandler(temp_handler)
        dataset_logger.setLevel(logging.INFO)

        try:
            # Setup mock responses
            mock_fetch_data.side_effect = [
                [test_params.sample_dataset_data],  # get_dataset call
                [{"count": 2}],  # count call for get_dataset_sets
                test_params.sample_dataset_sets,  # data call for get_dataset_sets
            ]

            # Mock recovery to return some results (to trigger retry logging)
            mock_download_image.return_value = {
                "recovered_count": 0,
                "still_missing_count": 2,
                "still_missing_images": {},
            }

            # Call verify_and_recover with auto_recover enabled
            await DatasetService.verify_and_recover_dataset_images(
                dataset_uuid=test_params.sample_dataset_uuid,
                base_output_dir=test_params.temp_logs_dir,
                auto_recover=True,
            )

            # Verify that recovery logging occurred by checking the log file
            assert log_file.exists()
            log_content = log_file.read_text()
            assert len(log_content) > 0

            # Check for specific log messages
            assert "Dataset verification:" in log_content
            assert "Dataset verification complete:" in log_content

        finally:
            # Clean up the temporary handler
            dataset_logger.removeHandler(temp_handler)
            temp_handler.close()

    def test_dataset_logger_configuration(self):
        """Test that dataset logger is properly configured."""
        # Clear any existing handlers first
        dataset_logger = logging.getLogger("dataset")
        for handler in dataset_logger.handlers[:]:
            dataset_logger.removeHandler(handler)

        model_run_uuid = "test-config-123"

        # Setup loggers fresh with model_run_uuid
        loggers = setup_loggers(model_run_uuid)
        dataset_logger = loggers["dataset"]

        # Verify logger configuration
        assert dataset_logger.level == logging.INFO
        assert dataset_logger.propagate is False  # Should not propagate to root

        # Verify handlers (should have exactly 2: file and console)
        assert len(dataset_logger.handlers) >= 2  # At least file and console handlers

        # Check handler types
        handler_types = [type(handler).__name__ for handler in dataset_logger.handlers]
        assert "FileHandler" in handler_types
        assert "StreamHandler" in handler_types

    def test_setup_dataset_logger_with_model_run_uuid(self):
        """Test that setup_dataset_logger creates logs in the correct model run directory."""
        model_run_uuid = "test-model-run-123"

        # Setup dataset logger with model_run_uuid
        dataset_logger = setup_dataset_logger(model_run_uuid)

        # Test logging to verify file handler works
        dataset_logger.info("Test log message for model run")

        # Check that log file exists in the correct isolated location
        # The logs will be created in the isolated runs directory
        run_paths = get_run_paths(model_run_uuid)
        expected_log_file = run_paths.logs / "dataset.log"
        assert expected_log_file.exists()

        # Check that log file contains the message
        log_content = expected_log_file.read_text()
        assert "Test log message for model run" in log_content
