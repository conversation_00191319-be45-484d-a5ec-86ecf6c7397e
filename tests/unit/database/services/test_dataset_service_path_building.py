"""
Tests for dataset service path building functionality.
"""

from unittest.mock import patch

from database.models.dataset_set import DatasetSet
from database.services.dataset_service import BatchConfig, DatasetService


class TestDatasetServicePathBuilding:
    """Test suite for dataset service path building functionality."""

    @patch("database.services.dataset_service._get_images_base_dir", return_value="")
    @patch("database.services.dataset_service.fetch_data")
    def test_get_dataset_sets_with_path_building_coin_location(
        self, mock_fetch_data, _, sample_dataset_sets_with_path_building
    ):
        """Test dataset sets retrieval with path building for coin location."""
        # Mock the fetch_data response
        mock_fetch_data.side_effect = [
            [{"count": 1}],  # count call
            [sample_dataset_sets_with_path_building[0]],  # data call
        ]

        # Create config
        config = BatchConfig(
            dataset_uuid="8b1003f7-c430-4fdc-999b-7c5fec055269",
            limit=100,
            offset=0,
        )

        # Call the method
        dataset_sets = list(DatasetService.get_dataset_sets(config))

        # Verify the result
        assert len(dataset_sets) == 1
        dataset_set = dataset_sets[0][0]  # First batch, first item
        assert isinstance(dataset_set, DatasetSet)
        assert dataset_set.image_url == "static/coins/d57/d52/4428.jpg"

    @patch("database.services.dataset_service._get_images_base_dir", return_value="")
    @patch("database.services.dataset_service.fetch_data")
    def test_get_dataset_sets_with_path_building_fact_location(
        self, mock_fetch_data, _, sample_dataset_sets_with_path_building
    ):
        """Test dataset sets retrieval with path building for fact location."""
        # Mock the fetch_data response
        mock_fetch_data.side_effect = [
            [{"count": 1}],  # count call
            [sample_dataset_sets_with_path_building[1]],  # data call
        ]

        # Create config
        config = BatchConfig(
            dataset_uuid="8b1003f7-c430-4fdc-999b-7c5fec055269",
            limit=100,
            offset=0,
        )

        # Call the method
        dataset_sets = list(DatasetService.get_dataset_sets(config))

        # Verify the result
        assert len(dataset_sets) == 1
        dataset_set = dataset_sets[0][0]  # First batch, first item
        assert isinstance(dataset_set, DatasetSet)
        assert dataset_set.image_url == "static/facts/coins/a1b/2c3/test.jpg"

    @patch("database.services.dataset_service._get_images_base_dir", return_value="")
    @patch("database.services.dataset_service.fetch_data")
    def test_get_dataset_sets_with_path_building_scrapes_location(
        self, mock_fetch_data, _, sample_dataset_sets_with_path_building
    ):
        """Test dataset sets retrieval with path building for scrapes location."""
        # Mock the fetch_data response
        mock_fetch_data.side_effect = [
            [{"count": 1}],  # count call
            [sample_dataset_sets_with_path_building[2]],  # data call
        ]

        # Create config
        config = BatchConfig(
            dataset_uuid="8b1003f7-c430-4fdc-999b-7c5fec055269",
            limit=100,
            offset=0,
        )

        # Call the method
        dataset_sets = list(DatasetService.get_dataset_sets(config))

        # Verify the result
        assert len(dataset_sets) == 1
        dataset_set = dataset_sets[0][0]  # First batch, first item
        assert isinstance(dataset_set, DatasetSet)
        expected_url = "static/scrapes/b2c3d4e5-f6g7-8901-bcde-fg2345678901/scrape.jpg"
        assert dataset_set.image_url == expected_url

    @patch("database.services.dataset_service._get_images_base_dir", return_value="")
    @patch("database.services.dataset_service.fetch_data")
    def test_get_dataset_sets_with_path_building_list_format(
        self,
        mock_fetch_data,
        _,
        sample_dataset_sets_with_path_building_list_format,
    ):
        """Test dataset sets retrieval with path building for list format images_reviews."""
        # Mock the fetch_data response
        mock_fetch_data.side_effect = [
            [{"count": 1}],  # count call
            sample_dataset_sets_with_path_building_list_format,  # data call
        ]

        # Create config
        config = BatchConfig(
            dataset_uuid="8b1003f7-c430-4fdc-999b-7c5fec055269",
            limit=100,
            offset=0,
        )

        # Call the method
        dataset_sets = list(DatasetService.get_dataset_sets(config))

        # Verify the result
        assert len(dataset_sets) == 1
        dataset_set = dataset_sets[0][0]  # First batch, first item
        assert isinstance(dataset_set, DatasetSet)
        assert dataset_set.image_url == "static/coins/d57/d52/4428.jpg"

    @patch("database.services.dataset_service.fetch_data")
    def test_get_dataset_sets_without_location_key(
        self, mock_fetch_data, sample_dataset_set_without_location_key
    ):
        """Test dataset sets retrieval without location key (no path building)."""
        # Mock the fetch_data response
        mock_fetch_data.side_effect = [
            [{"count": 1}],  # count call
            [sample_dataset_set_without_location_key],  # data call
        ]

        # Create config
        config = BatchConfig(
            dataset_uuid="8b1003f7-c430-4fdc-999b-7c5fec055269",
            limit=100,
            offset=0,
        )

        # Call the method
        dataset_sets = list(DatasetService.get_dataset_sets(config))

        # Verify the result - should keep original URL
        assert len(dataset_sets) == 1
        dataset_set = dataset_sets[0][0]  # First batch, first item
        assert isinstance(dataset_set, DatasetSet)
        assert dataset_set.image_url == "/images/coins/4428.jpg"

    @patch("database.services.dataset_service.fetch_data")
    def test_get_dataset_sets_without_image_url(
        self, mock_fetch_data, sample_dataset_set_without_image_url
    ):
        """Test dataset sets retrieval without image URL (no path building)."""
        # Mock the fetch_data response
        mock_fetch_data.side_effect = [
            [{"count": 1}],  # count call
            [sample_dataset_set_without_image_url],  # data call
        ]

        # Create config
        config = BatchConfig(
            dataset_uuid="8b1003f7-c430-4fdc-999b-7c5fec055269",
            limit=100,
            offset=0,
        )

        # Call the method
        dataset_sets = list(DatasetService.get_dataset_sets(config))

        # Verify the result - should have None image_url
        assert len(dataset_sets) == 1
        dataset_set = dataset_sets[0][0]  # First batch, first item
        assert isinstance(dataset_set, DatasetSet)
        assert dataset_set.image_url is None

    @patch("database.services.dataset_service.fetch_data")
    def test_get_dataset_sets_with_absolute_base_dir(
        self, mock_fetch_data, sample_dataset_sets_with_path_building
    ):
        """Test dataset sets retrieval with absolute base dir for coin location."""
        # Temporarily set the class attribute to test absolute path behavior
        # pylint: disable=protected-access
        original_base_dir = DatasetService._images_base_dir
        DatasetService._images_base_dir = "/tmp/images"

        try:
            mock_fetch_data.side_effect = [
                [{"count": 1}],  # count call
                [sample_dataset_sets_with_path_building[0]],  # data call
            ]
            config = BatchConfig(
                dataset_uuid="8b1003f7-c430-4fdc-999b-7c5fec055269",
                limit=100,
                offset=0,
            )
            dataset_sets = list(DatasetService.get_dataset_sets(config))
            assert len(dataset_sets) == 1
            dataset_set = dataset_sets[0][0]
            assert isinstance(dataset_set, DatasetSet)
            assert dataset_set.image_url.startswith("/tmp/images/static/coins/")
            assert dataset_set.image_url.endswith("4428.jpg")
        finally:
            # Restore the original value
            # pylint: disable=protected-access
            DatasetService._images_base_dir = original_base_dir
