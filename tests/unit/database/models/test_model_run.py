"""
Tests for the ModelRun class.
"""

from src.database.models.model_run import ModelRun
from tests.fixtures.data import get_sample_model_run_data


class TestModelRun:
    """Test suite for the ModelRun class."""

    def test_get_status_scheduled(self):
        """Test that a model run with only schedule_time returns 'scheduled' status."""
        # Create a model run with only schedule_time
        run_data = get_sample_model_run_data(
            {
                "prepared_time": None,
                "start_time": None,
                "end_time": None,
                "metrics": None,
                "log_path": True,
            }
        )
        model_run = ModelRun.model_validate(run_data)
        assert model_run.get_status() == "scheduled"

    def test_get_status_preparing(self):
        """Test that a model run with prepared_time returns 'preparing' status."""
        # Create a model run with prepared_time but no start_time
        run_data = get_sample_model_run_data(
            {"start_time": None, "end_time": None, "metrics": None, "log_path": True}
        )
        model_run = ModelRun.model_validate(run_data)
        assert model_run.get_status() == "preparing"

    def test_get_status_running(self):
        """Test that a model run with start_time returns 'running' status."""
        # Create a model run with start_time but no end_time
        run_data = get_sample_model_run_data(
            {"end_time": None, "metrics": None, "log_path": True}
        )
        model_run = ModelRun.model_validate(run_data)
        assert model_run.get_status() == "running"

    def test_get_status_completed(self):
        """Test that a model run with end_time returns 'completed' status."""
        # Create a model run with end_time and success metrics
        run_data = get_sample_model_run_data(
            {
                "end_time": "2023-10-01T13:30:00Z",
                "metrics": {"accuracy": 0.95},
                "log_path": True,
            }
        )
        model_run = ModelRun.model_validate(run_data)
        assert model_run.get_status() == "completed"

    def test_get_status_failed(self):
        """Test that a model run with end_time and error metrics returns 'failed' status."""
        # Create a model run with end_time and error metrics
        run_data = get_sample_model_run_data(
            {
                "end_time": "2023-10-01T13:30:00Z",
                "metrics": {"error": "Training failed due to out of memory"},
                "log_path": True,
            }
        )
        model_run = ModelRun.model_validate(run_data)
        assert model_run.get_status() == "failed"

    def test_get_status_cancelled(self):
        """Test that a model run with cancelled metrics returns 'cancelled' status."""
        # Create a model run with cancelled metrics
        run_data = get_sample_model_run_data(
            {
                "end_time": None,
                "metrics": {"cancelled": True, "reason": "User cancelled training"},
                "log_path": True,
            }
        )
        model_run = ModelRun.model_validate(run_data)
        assert model_run.get_status() == "cancelled"

    def test_get_status_precedence(self):
        """Test that status precedence is correctly applied."""
        # Cancelled takes precedence over completed
        run_data = get_sample_model_run_data(
            {
                "end_time": "2023-10-01T13:30:00Z",
                "metrics": {"cancelled": True, "accuracy": 0.5},
                "log_path": True,
            }
        )
        model_run = ModelRun.model_validate(run_data)
        assert model_run.get_status() == "cancelled"

        # Failed takes precedence over completed
        run_data = get_sample_model_run_data(
            {
                "end_time": "2023-10-01T13:30:00Z",
                "metrics": {"error": "Training failed", "accuracy": 0.5},
                "log_path": True,
            }
        )
        model_run = ModelRun.model_validate(run_data)
        assert model_run.get_status() == "failed"
