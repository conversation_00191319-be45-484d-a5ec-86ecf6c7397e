"""
Tests for background image download functionality in DatasetService.

This module tests that the dataset service correctly handles downloading
images with null coin_side_uuid to the background directory.
"""

# pylint: disable=line-too-long

from pathlib import Path
from unittest.mock import patch
from uuid import uuid4

import pytest

from database.models.dataset_set import SetType
from database.services.dataset_service import DatasetService
from tests.fixtures.data import create_dataset_sets_for_background_tests


class TestDatasetServiceBackground:
    """Test background image download functionality in DatasetService."""

    @pytest.mark.asyncio
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.download_dataset_images"  # noqa: E501
    )
    async def test_download_background_images_to_correct_directory(self, mock_download):
        """Test that background images are downloaded to the background directory."""
        mock_download.return_value = (2, [])

        # Mock dataset sets with background images
        image_urls = ["http://example.com/bg1.jpg", "http://example.com/bg2.jpg"]
        background_dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=0, background_count=2, image_urls=image_urls
        )

        # Mock the get_dataset_sets method to return our test data
        with patch.object(
            DatasetService, "get_dataset_sets", return_value=[background_dataset_sets]
        ):
            base_output_dir = Path("/tmp/test_dataset")

            # Call the download method
            downloaded_count, errors = await DatasetService.download_dataset_images(
                dataset_uuid=str(uuid4()),
                base_output_dir=base_output_dir,
                set_type=SetType.TRAIN,
            )

            # Verify that images were downloaded to the background directory
            assert downloaded_count == 2
            assert len(errors) == 0

            # Check that the downloader was called with the dataset sets
            assert mock_download.call_count == 1
            _, call_kwargs = mock_download.call_args

            # Verify the dataset sets were passed correctly
            assert "dataset_sets" in call_kwargs
            assert "base_output_dir" in call_kwargs
            assert call_kwargs["base_output_dir"] == base_output_dir

            # Verify the dataset sets contain the expected background images
            dataset_sets = call_kwargs["dataset_sets"]
            assert len(dataset_sets) == 2
            for dataset_set in dataset_sets:
                assert dataset_set.coin_side_uuid is None  # Background images
                assert dataset_set.image_url in image_urls

    @pytest.mark.asyncio
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.download_dataset_images"  # noqa: E501
    )
    async def test_download_labeled_images_to_coin_side_directory(self, mock_download):
        """Test that labeled images are still downloaded to coin_side_uuid directories."""
        mock_download.return_value = (1, [])

        coin_side_uuid = str(uuid4())
        image_urls = ["http://example.com/labeled1.jpg"]
        labeled_dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=1, background_count=0, image_urls=image_urls
        )
        # Override coin_side_uuid for this specific test
        labeled_dataset_sets[0].coin_side_uuid = coin_side_uuid

        # Mock the get_dataset_sets method
        with patch.object(
            DatasetService, "get_dataset_sets", return_value=[labeled_dataset_sets]
        ):
            base_output_dir = Path("/tmp/test_dataset")

            # Call the download method
            downloaded_count, errors = await DatasetService.download_dataset_images(
                dataset_uuid=str(uuid4()),
                base_output_dir=base_output_dir,
                set_type=SetType.TRAIN,
            )

            # Verify that image was downloaded to the coin_side_uuid directory
            assert downloaded_count == 1
            assert len(errors) == 0

            # Check that download_dataset_images was called with correct parameters
            mock_download.assert_called_once()
            call_args = mock_download.call_args
            assert call_args[1]["base_output_dir"] == base_output_dir
            assert len(call_args[1]["dataset_sets"]) == 1

    @pytest.mark.asyncio
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.download_dataset_images"  # noqa: E501
    )
    async def test_download_mixed_labeled_and_background_images(self, mock_download):
        """Test downloading a mix of labeled and background images."""
        mock_download.return_value = (2, [])

        # Setup test data
        coin_side_uuid = str(uuid4())
        image_urls = ["http://example.com/labeled.jpg", "http://example.com/bg.jpg"]
        mixed_dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=1, background_count=1, image_urls=image_urls
        )
        mixed_dataset_sets[0].coin_side_uuid = coin_side_uuid

        # Execute test
        with patch.object(
            DatasetService, "get_dataset_sets", return_value=[mixed_dataset_sets]
        ):
            base_output_dir = Path("/tmp/test_dataset")
            downloaded_count, errors = await DatasetService.download_dataset_images(
                dataset_uuid=str(uuid4()),
                base_output_dir=base_output_dir,
                set_type=SetType.TRAIN,
            )

            # Verify results
            assert downloaded_count == 2
            assert len(errors) == 0

            # Verify that download_dataset_images was called with correct parameters
            mock_download.assert_called_once()
            call_args = mock_download.call_args
            assert call_args[1]["base_output_dir"] == base_output_dir
            assert len(call_args[1]["dataset_sets"]) == 2

    @pytest.mark.asyncio
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.download_dataset_images"  # noqa: E501
    )
    async def test_skip_images_without_image_uuid(self, mock_download):
        """Test that images without image_uuid are skipped."""
        mock_download.return_value = (1, [])

        image_urls = ["http://example.com/invalid.jpg", "http://example.com/valid.jpg"]
        invalid_dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=0, background_count=2, image_urls=image_urls
        )
        # Override specific values for this test
        invalid_dataset_sets[0].image_uuid = (
            None  # Missing image_uuid - should be skipped
        )
        invalid_dataset_sets[1].image_uuid = "valid_bg_image"  # Valid image_uuid

        # Mock the get_dataset_sets method
        with patch.object(
            DatasetService, "get_dataset_sets", return_value=[invalid_dataset_sets]
        ):
            base_output_dir = Path("/tmp/test_dataset")

            # Call the download method
            downloaded_count, errors = await DatasetService.download_dataset_images(
                dataset_uuid=str(uuid4()),
                base_output_dir=base_output_dir,
                set_type=SetType.TRAIN,
            )

            # Only the valid image should be downloaded
            assert downloaded_count == 1
            assert len(errors) == 0

            # Check that download_dataset_images was called with the dataset sets
            mock_download.assert_called_once()
            call_args = mock_download.call_args
            assert call_args[1]["base_output_dir"] == base_output_dir
            # The DatasetDownloader will filter out invalid sets, so we pass all sets
            assert len(call_args[1]["dataset_sets"]) == 2

    @pytest.mark.asyncio
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.download_dataset_images"  # noqa: E501
    )
    async def test_skip_images_without_image_url(self, mock_download):
        """Test that images without image_url are skipped."""
        mock_download.return_value = (0, [])

        invalid_dataset_sets = create_dataset_sets_for_background_tests(
            labeled_count=0, background_count=1
        )
        # Override specific values for this test
        invalid_dataset_sets[0].image_uuid = "bg_image"
        invalid_dataset_sets[0].image_url = (
            None  # Missing image_url - should be skipped
        )

        # Mock the get_dataset_sets method
        with patch.object(
            DatasetService, "get_dataset_sets", return_value=[invalid_dataset_sets]
        ):
            base_output_dir = Path("/tmp/test_dataset")

            # Call the download method
            downloaded_count, errors = await DatasetService.download_dataset_images(
                dataset_uuid=str(uuid4()),
                base_output_dir=base_output_dir,
                set_type=SetType.TRAIN,
            )

            # No images should be downloaded
            assert downloaded_count == 0
            assert len(errors) == 0

            # Check that download_dataset_images was called but returned 0 downloads
            mock_download.assert_called_once()
            call_args = mock_download.call_args
            assert call_args[1]["base_output_dir"] == base_output_dir
            assert len(call_args[1]["dataset_sets"]) == 1

    @pytest.mark.asyncio
    async def test_verify_dataset_images_includes_background_images(self):
        """Test that verify_dataset_images includes background images in expected mapping."""
        coin_side_uuid = str(uuid4())
        dataset_uuid = str(uuid4())

        image_urls = ["http://example.com/labeled.jpg", "http://example.com/bg.jpg"]
        mixed_dataset_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=1,
            background_count=1,
            image_urls=image_urls,
        )
        # Override coin_side_uuid for the labeled image
        mixed_dataset_sets[0].coin_side_uuid = coin_side_uuid

        # Mock the get_dataset_sets method and dataset metadata
        with patch.object(
            DatasetService, "get_dataset_sets", return_value=[mixed_dataset_sets]
        ), patch.object(
            DatasetService,
            "_get_dataset_metadata",
            return_value={"expected_count": 2, "dataset": {"uuid": dataset_uuid}},
        ):
            base_output_dir = Path("/tmp/test_dataset")

            # Call the public method that uses _build_expected_images_mapping internally
            result = await DatasetService.verify_dataset_images(
                dataset_uuid=dataset_uuid, base_output_dir=base_output_dir, profile=None
            )

            # Verify both images are included in expected_images
            expected_images = result["expected_images"]
            assert len(expected_images) == 2
            assert "jpg_image" in expected_images  # Based on fixture logic
            assert "bg_image_1" in expected_images  # Based on fixture logic

            # Verify labeled image path
            labeled_info = expected_images["jpg_image"]
            assert labeled_info["coin_side_uuid"] == coin_side_uuid
            assert (
                labeled_info["path"]
                == base_output_dir / coin_side_uuid / "jpg_image.jpg"
            )

            # Verify background image path
            bg_info = expected_images["bg_image_1"]
            assert bg_info["coin_side_uuid"] == "background"
            assert bg_info["path"] == base_output_dir / "background" / "bg_image_1.jpg"

    @pytest.mark.asyncio
    async def test_different_image_extensions_handled_correctly(self):
        """Test that different image extensions (png, webp, heic) are handled correctly."""
        dataset_uuid = str(uuid4())

        # Create dataset sets with different image extensions
        image_urls = [
            "http://example.com/image.jpg",
            "http://example.com/image.png",
            "http://example.com/background.webp",
            "http://example.com/background.heic",
        ]
        mixed_extension_sets = create_dataset_sets_for_background_tests(
            dataset_uuid=dataset_uuid,
            labeled_count=2,
            background_count=2,
            image_urls=image_urls,
        )
        # Override coin_side_uuids for labeled images
        mixed_extension_sets[0].coin_side_uuid = str(uuid4())
        mixed_extension_sets[1].coin_side_uuid = str(uuid4())

        # Mock the get_dataset_sets method and dataset metadata
        with patch.object(
            DatasetService, "get_dataset_sets", return_value=[mixed_extension_sets]
        ), patch.object(
            DatasetService,
            "_get_dataset_metadata",
            return_value={"expected_count": 4, "dataset": {"uuid": dataset_uuid}},
        ):
            base_output_dir = Path("/tmp/test_dataset")

            # Call the public method that uses _build_expected_images_mapping internally
            result = await DatasetService.verify_dataset_images(
                dataset_uuid=dataset_uuid, base_output_dir=base_output_dir, profile=None
            )

            # Verify all images are included with correct extensions
            expected_images = result["expected_images"]
            assert len(expected_images) == 4

            # Check JPG image (labeled)
            jpg_info = expected_images["jpg_image"]
            assert jpg_info["filename"] == "jpg_image.jpg"
            assert "jpg_image.jpg" in str(jpg_info["path"])

            # Check PNG image (labeled)
            png_info = expected_images["png_image"]
            assert png_info["filename"] == "png_image.png"
            assert "png_image.png" in str(png_info["path"])

            # Check WebP background image
            webp_info = expected_images["webp_bg"]
            assert webp_info["filename"] == "webp_bg.webp"
            assert webp_info["coin_side_uuid"] == "background"
            assert "background/webp_bg.webp" in str(webp_info["path"])

            # Check HEIC background image (fixture generates bg_image_2 for second background)
            heic_info = expected_images["bg_image_2"]
            assert heic_info["filename"] == "bg_image_2.heic"
            assert heic_info["coin_side_uuid"] == "background"
            assert "background/bg_image_2.heic" in str(heic_info["path"])
