"""
Tests for file utility functions.
"""

import tempfile
from pathlib import Path

import pytest

from database.utils.file_utils import ensure_directory, get_unique_filename


class TestFileUtils:
    """Test suite for file utility functions."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    def test_ensure_directory_creates_new_directory(self, temp_dir):
        """Test that ensure_directory creates a new directory."""
        new_dir = temp_dir / "new_directory"
        assert not new_dir.exists()

        result = ensure_directory(new_dir)

        assert new_dir.exists()
        assert new_dir.is_dir()
        assert result == new_dir

    def test_ensure_directory_with_existing_directory(self, temp_dir):
        """Test that ensure_directory works with existing directory."""
        existing_dir = temp_dir / "existing"
        existing_dir.mkdir()
        assert existing_dir.exists()

        result = ensure_directory(existing_dir)

        assert existing_dir.exists()
        assert existing_dir.is_dir()
        assert result == existing_dir

    def test_ensure_directory_creates_nested_directories(self, temp_dir):
        """Test that ensure_directory creates nested directories."""
        nested_dir = temp_dir / "level1" / "level2" / "level3"
        assert not nested_dir.exists()

        result = ensure_directory(nested_dir)

        assert nested_dir.exists()
        assert nested_dir.is_dir()
        assert result == nested_dir
        # Check that all parent directories were created
        assert (temp_dir / "level1").exists()
        assert (temp_dir / "level1" / "level2").exists()

    def test_get_unique_filename_no_conflict(self, temp_dir):
        """Test get_unique_filename when no file exists."""
        filename = "test.txt"

        result = get_unique_filename(temp_dir, filename)

        assert result == temp_dir / filename
        assert result.name == filename

    def test_get_unique_filename_with_conflict(self, temp_dir):
        """Test get_unique_filename when file already exists."""
        filename = "test.txt"
        existing_file = temp_dir / filename
        existing_file.write_text("existing content")

        result = get_unique_filename(temp_dir, filename)

        assert result == temp_dir / "test_1.txt"
        assert result.name == "test_1.txt"

    def test_get_unique_filename_multiple_conflicts(self, temp_dir):
        """Test get_unique_filename with multiple existing files."""
        filename = "test.txt"
        # Create multiple existing files
        (temp_dir / "test.txt").write_text("content")
        (temp_dir / "test_1.txt").write_text("content")
        (temp_dir / "test_2.txt").write_text("content")

        result = get_unique_filename(temp_dir, filename)

        assert result == temp_dir / "test_3.txt"
        assert result.name == "test_3.txt"

    def test_get_unique_filename_with_extension(self, temp_dir):
        """Test get_unique_filename preserves file extension."""
        filename = "document.pdf"
        existing_file = temp_dir / filename
        existing_file.write_text("existing content")

        result = get_unique_filename(temp_dir, filename)

        assert result == temp_dir / "document_1.pdf"
        assert result.suffix == ".pdf"

    def test_get_unique_filename_no_extension(self, temp_dir):
        """Test get_unique_filename with file without extension."""
        filename = "README"
        existing_file = temp_dir / filename
        existing_file.write_text("existing content")

        result = get_unique_filename(temp_dir, filename)

        assert result == temp_dir / "README_1"
        assert result.name == "README_1"

    def test_get_unique_filename_creates_directory(self, temp_dir):
        """Test that get_unique_filename creates the directory if it doesn't exist."""
        new_dir = temp_dir / "new_subdir"
        filename = "test.txt"
        assert not new_dir.exists()

        result = get_unique_filename(new_dir, filename)

        assert new_dir.exists()
        assert new_dir.is_dir()
        assert result == new_dir / filename
