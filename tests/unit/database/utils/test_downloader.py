"""
Tests for the generic Downloader class.
"""

import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest

from common.downloader import (
    DownloadConfig,
    Downloader,
    FileDownloadConfig,
    ImageDownloadConfig,
)


class TestDownloader:
    """Test suite for generic Downloader class."""

    @pytest.fixture
    def downloader(self):
        """Create a Downloader instance for testing."""
        return Downloader(timeout=30)

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def temp_source_file(self, temp_dir):
        """Create a temporary source file for testing."""
        source_file = temp_dir / "source.txt"
        source_file.write_text("test content")
        return source_file

    @pytest.fixture
    def sample_download_items(self):
        """Create sample download items for testing."""
        return [
            {
                "url": "https://example.com/file1.jpg",
                "output_dir": Path("/tmp/test/dir1"),
                "filename": "file1.jpg",
                "timeout": 60,
            },
            {
                "url": "https://example.com/file2.png",
                "output_dir": Path("/tmp/test/dir2"),
                "filename": "file2.png",
                "timeout": 60,
            },
        ]

    @pytest.mark.asyncio
    async def test_download_file_local_copy_success(
        self, downloader, temp_dir, temp_source_file
    ):
        """Test successful copying of a local file."""
        output_dir = temp_dir / "output"

        config = DownloadConfig(
            url=str(temp_source_file), output_dir=output_dir, filename="copied.txt"
        )
        success, message = await downloader.download_file(config)

        assert success is True
        assert "Copied" in message
        assert (output_dir / "copied.txt").exists()
        assert (output_dir / "copied.txt").read_text() == "test content"

    @pytest.mark.asyncio
    async def test_download_file_local_copy_nonexistent(self, downloader, temp_dir):
        """Test copying a non-existent local file."""
        output_dir = temp_dir / "output"
        non_existent_file = temp_dir / "nonexistent.txt"

        config = DownloadConfig(
            url=str(non_existent_file), output_dir=output_dir, filename="copied.txt"
        )
        success, message = await downloader.download_file(config)

        assert success is False
        assert "Source file does not exist" in message
        assert not (output_dir / "copied.txt").exists()

    @pytest.mark.asyncio
    async def test_download_file_file_already_exists(
        self, downloader, temp_dir, temp_source_file
    ):
        """Test that existing files are skipped."""
        output_dir = temp_dir / "output"
        output_dir.mkdir(parents=True)
        existing_file = output_dir / "existing.txt"
        existing_file.write_text("existing content")

        config = DownloadConfig(
            url=str(temp_source_file), output_dir=output_dir, filename="existing.txt"
        )
        success, message = await downloader.download_file(config)

        assert success is True
        assert "File already exists" in message
        assert existing_file.read_text() == "existing content"  # Unchanged

    @pytest.mark.asyncio
    async def test_download_file_filename_extraction(
        self, downloader, temp_dir, temp_source_file
    ):
        """Test that filename is extracted from URL when not provided."""
        output_dir = temp_dir / "output"

        config = DownloadConfig(
            url=str(temp_source_file),
            output_dir=output_dir,
            # No filename provided
        )
        success, _ = await downloader.download_file(config)

        assert success is True
        assert (output_dir / "source.txt").exists()

    @pytest.mark.asyncio
    async def test_download_file_http_success(self, downloader, temp_dir):
        """Test successful HTTP download."""
        output_dir = temp_dir / "output"

        # Mock the HTTP download method
        with patch.object(downloader, "_download_http_file") as mock_download:
            mock_download.return_value = (True, "Downloaded successfully")

            config = DownloadConfig(
                url="https://example.com/file.txt",
                output_dir=output_dir,
                filename="downloaded.txt",
            )
            success, message = await downloader.download_file(config)

            assert success is True
            assert "Downloaded successfully" in message
            mock_download.assert_called_once()

    @pytest.mark.asyncio
    async def test_download_file_http_failure(self, downloader, temp_dir):
        """Test HTTP download failure."""
        output_dir = temp_dir / "output"

        # Mock the HTTP download method to return failure
        with patch.object(downloader, "_download_http_file") as mock_download:
            mock_download.return_value = (False, "HTTP 404")

            config = DownloadConfig(
                url="https://example.com/nonexistent.txt",
                output_dir=output_dir,
                filename="downloaded.txt",
            )
            success, message = await downloader.download_file(config)

            assert success is False
            assert "HTTP 404" in message

    @pytest.mark.asyncio
    async def test_download_single_file_success(self, downloader, temp_dir):
        """Test successful single file download."""
        output_dir = temp_dir / "output"

        # Mock the download_file method
        with patch.object(downloader, "download_file") as mock_download:
            mock_download.return_value = (True, "Downloaded successfully")

            config = FileDownloadConfig(
                url="https://example.com/file.jpg",
                output_dir=output_dir,
                filename="file.jpg",
            )
            result = await downloader.download_single_file(config)

            assert result == output_dir / "file.jpg"
            mock_download.assert_called_once()

    @pytest.mark.asyncio
    async def test_download_image_failure(self, downloader, temp_dir):
        """Test image download failure."""
        output_dir = temp_dir / "output"

        # Mock the download_file method to return failure
        with patch.object(downloader, "download_file") as mock_download:
            mock_download.return_value = (False, "Download failed")

            config = ImageDownloadConfig(
                image_url="https://example.com/image.jpg",
                output_dir=output_dir,
                filename="image.jpg",
            )
            result = await downloader.download_image(config)

            assert result is None


class TestDownloaderHTTPMethods:
    """Test suite for HTTP-specific download methods."""

    @pytest.fixture
    def downloader(self):
        """Create a Downloader instance for testing."""
        return Downloader(timeout=30)

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.mark.asyncio
    async def test_download_http_file_timeout_error(self, downloader, temp_dir):
        """Test HTTP download with timeout error."""
        output_path = temp_dir / "downloaded.txt"

        # Mock the _download_http_file method to return timeout error
        with patch.object(
            downloader, "_download_http_file", return_value=(False, "Request timeout")
        ):
            # pylint: disable=protected-access
            success, message = await downloader._download_http_file(
                url="https://example.com/file.txt",
                output_path=output_path,
                session=None,
                timeout=30,
            )

            assert success is False
            assert "Request timeout" in message

    @pytest.mark.asyncio
    async def test_download_http_file_client_error(self, downloader, temp_dir):
        """Test HTTP download with client error."""
        output_path = temp_dir / "downloaded.txt"

        # Mock the _download_http_file method to return client error
        with patch.object(
            downloader, "_download_http_file", return_value=(False, "Connection failed")
        ):
            # pylint: disable=protected-access
            success, message = await downloader._download_http_file(
                url="https://example.com/file.txt",
                output_path=output_path,
                session=None,
                timeout=30,
            )

            assert success is False
            assert "Connection failed" in message

    @pytest.mark.asyncio
    async def test_copy_local_file_file_url_scheme(self, downloader, temp_dir):
        """Test copying local file with file:// URL scheme."""
        source_file = temp_dir / "source.txt"
        source_file.write_text("test content")
        output_path = temp_dir / "output.txt"

        # pylint: disable=protected-access
        success, message = await downloader._copy_local_file(
            source_path=f"file://{source_file}", output_path=output_path
        )

        assert success is True
        assert "Copied" in message
        assert output_path.exists()
        assert output_path.read_text() == "test content"

    @pytest.mark.asyncio
    async def test_copy_local_file_permission_error(self, downloader, temp_dir):
        """Test copying local file with permission error."""
        source_file = temp_dir / "source.txt"
        source_file.write_text("test content")
        output_path = temp_dir / "output.txt"

        # Mock shutil.copy2 to raise PermissionError
        with patch("shutil.copy2", side_effect=PermissionError("Permission denied")):
            # pylint: disable=protected-access
            success, message = await downloader._copy_local_file(
                source_path=str(source_file), output_path=output_path
            )

            assert success is False
            assert "Permission denied" in message


class TestDownloaderEdgeCases:
    """Test suite for edge cases and error conditions."""

    @pytest.fixture
    def downloader(self):
        """Create a Downloader instance for testing."""
        return Downloader(timeout=30)

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.mark.asyncio
    async def test_download_file_no_filename_in_url(self, downloader, temp_dir):
        """Test download when filename cannot be extracted from URL."""
        output_dir = temp_dir / "output"

        config = DownloadConfig(
            url="https://example.com/", output_dir=output_dir  # No filename in URL
        )
        success, message = await downloader.download_file(config)

        assert success is False
        assert "Could not determine filename from URL" in message

    @pytest.mark.asyncio
    async def test_download_image_exception_handling(self, downloader, temp_dir):
        """Test image download exception handling."""
        output_dir = temp_dir / "output"

        # Mock download_file to raise an exception
        with patch.object(
            downloader, "download_file", side_effect=Exception("Unexpected error")
        ):
            config = ImageDownloadConfig(
                image_url="https://example.com/image.jpg",
                output_dir=output_dir,
                filename="image.jpg",
            )
            result = await downloader.download_image(config)

            assert result is None
