"""
Helper classes for testing callbacks.

This module provides test-specific subclasses with
additional methods to facilitate testing.
"""

from src.jobs.base import JobDispatcher


class Mockable<PERSON><PERSON><PERSON><PERSON>patcher(JobDispatcher):
    """
    A mockable version of JobDispatcher with public methods
    for accessing protected members during tests.
    """

    def notify_callbacks(self, method_name, *args, **kwargs):
        """
        Public wrapper for _notify_callbacks to use in tests.

        Args:
            method_name: The name of the callback method to call
            *args: Arguments to pass to the callback method
            **kwargs: Keyword arguments to pass to the callback method
        """
        return self._notify_callbacks(method_name, *args, **kwargs)
