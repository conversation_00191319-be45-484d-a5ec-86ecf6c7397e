"""
Unit tests for the job callback system.

This module tests the functionality of job callbacks and their integration
with the job dispatcher system.
"""

# pylint: disable=duplicate-code

import time
import unittest
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest

from src.common.callbacks.job import JobCallback
from src.jobs.base import Job, JobStatus

# Import fixtures to make them available for pytest discovery
pytest_plugins = ["tests.fixtures.job_callbacks"]


class TestJobCallback(unittest.TestCase):
    """Test the JobCallback base class and its integration with JobDispatcher."""

    def __init__(self, *args, **kwargs):
        """Initialize the test case."""
        super().__init__(*args, **kwargs)
        self.job_id = None
        self.job = None
        self.error = None

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Patch asyncio.get_event_loop().time() to avoid the "There is no current event loop" error
        with patch("asyncio.get_event_loop") as mock_get_loop:
            mock_loop = MagicMock()
            mock_loop.time.return_value = time.time()
            mock_get_loop.return_value = mock_loop
            # Create a mock job
            self.job_id = str(uuid4())
            self.job = Job(job_id=self.job_id, data={})

        # Create a mock error
        self.error = Exception("Test error")

    def test_job_callback_methods_exist(self):
        """Test that all expected callback methods exist in JobCallback."""
        callback = JobCallback()

        # Check that all expected methods exist
        assert hasattr(callback, "on_dispatcher_start")
        assert hasattr(callback, "on_dispatcher_stop")
        assert hasattr(callback, "on_job_scheduled")
        assert hasattr(callback, "on_job_start")
        assert hasattr(callback, "on_job_complete")
        assert hasattr(callback, "on_job_failed")
        assert hasattr(callback, "on_job_cancelled")

        # Check that methods can be called without errors
        callback.on_dispatcher_start()
        callback.on_dispatcher_stop()
        callback.on_job_scheduled(self.job)
        callback.on_job_start(self.job)
        callback.on_job_complete(self.job)
        callback.on_job_failed(self.job, self.error)
        callback.on_job_cancelled(self.job)


class TestJobCallbackIntegration:
    """Test the integration of JobCallback with JobDispatcher."""

    @pytest.mark.asyncio
    async def test_dispatcher_start_callbacks(
        self, dispatcher, database_callback_mock, resource_callback_mock
    ):
        """Test that callbacks are called when dispatcher starts."""
        await dispatcher.start()

        # Check that callbacks were called
        database_callback_mock.on_dispatcher_start.assert_called_once()
        resource_callback_mock.on_dispatcher_start.assert_called_once()

    @pytest.mark.asyncio
    async def test_dispatcher_stop_callbacks(
        self, dispatcher, database_callback_mock, resource_callback_mock
    ):
        """Test that callbacks are called when dispatcher stops."""
        await dispatcher.start()
        await dispatcher.stop()

        # Check that callbacks were called
        database_callback_mock.on_dispatcher_stop.assert_called_once()
        resource_callback_mock.on_dispatcher_stop.assert_called_once()

    @pytest.mark.asyncio
    async def test_job_scheduled_callbacks(
        self, dispatcher, job, database_callback_mock, resource_callback_mock
    ):
        """Test that callbacks are called when a job is scheduled."""
        await dispatcher.schedule_job(job)

        # Check that callbacks were called
        database_callback_mock.on_job_scheduled.assert_called_once_with(job)
        resource_callback_mock.on_job_scheduled.assert_called_once_with(job)

    @pytest.mark.asyncio
    async def test_job_cancelled_callbacks(
        self, dispatcher, job, database_callback_mock, resource_callback_mock
    ):
        """Test that callbacks are called when a job is cancelled."""
        await dispatcher.schedule_job(job)
        await dispatcher.cancel_job(job.job_id)

        # Check that callbacks were called
        database_callback_mock.on_job_cancelled.assert_called_once_with(job)
        resource_callback_mock.on_job_cancelled.assert_called_once_with(job)


class TestCustomJobCallbackIntegration:
    """Test the integration of a custom JobCallback with JobDispatcher."""

    @pytest.mark.asyncio
    async def test_custom_callback_lifecycle(
        self, mock_job_dispatcher_factory, custom_test_callback, job
    ):
        """Test the complete lifecycle of a job with custom callback."""
        # Create dispatcher with custom callback
        dispatcher = mock_job_dispatcher_factory(callbacks=[custom_test_callback])

        # Start dispatcher
        await dispatcher.start()
        assert custom_test_callback.start_count == 1

        # Schedule job
        await dispatcher.schedule_job(job)
        assert custom_test_callback.scheduled_count == 1
        assert custom_test_callback.last_job == job

        # Simulate job start
        job.status = JobStatus.RUNNING
        dispatcher._notify_callbacks(  # pylint: disable=protected-access
            "on_job_start", job
        )
        assert custom_test_callback.start_job_count == 1

        # Simulate job completion
        job.status = JobStatus.COMPLETED
        dispatcher._notify_callbacks(  # pylint: disable=protected-access
            "on_job_complete", job
        )
        assert custom_test_callback.complete_count == 1

        # Stop dispatcher
        await dispatcher.stop()
        assert custom_test_callback.stop_count == 1

    @pytest.mark.asyncio
    async def test_custom_callback_job_failure(
        self, mock_job_dispatcher_factory, custom_test_callback, job
    ):
        """Test callback handling of job failure."""
        # Create dispatcher with custom callback
        dispatcher = mock_job_dispatcher_factory(callbacks=[custom_test_callback])

        await dispatcher.start()
        await dispatcher.schedule_job(job)

        # Simulate job failure
        error = Exception("Test failure")
        job.status = JobStatus.FAILED
        dispatcher._notify_callbacks(  # pylint: disable=protected-access
            "on_job_failed", job, error
        )

        assert custom_test_callback.failed_count == 1
        assert custom_test_callback.last_job == job
        assert custom_test_callback.last_error == error

    @pytest.mark.asyncio
    async def test_custom_callback_job_cancellation(
        self, mock_job_dispatcher_factory, custom_test_callback, job
    ):
        """Test callback handling of job cancellation."""
        # Create dispatcher with custom callback
        dispatcher = mock_job_dispatcher_factory(callbacks=[custom_test_callback])

        await dispatcher.start()
        await dispatcher.schedule_job(job)

        # Cancel job
        await dispatcher.cancel_job(job.job_id)

        assert custom_test_callback.cancelled_count == 1
        assert custom_test_callback.last_job == job
        assert custom_test_callback.last_job.status == JobStatus.CANCELLED


class TestTrainingJobCallbacks:  # pylint: disable=too-few-public-methods
    """Test callbacks with TrainingJob specifically."""

    @pytest.mark.asyncio
    async def test_training_job_lifecycle(
        self, mock_job_dispatcher_factory, custom_test_callback, training_job
    ):
        """Test the complete lifecycle of a training job with callbacks."""
        # Create dispatcher with callbacks
        dispatcher = mock_job_dispatcher_factory(callbacks=[custom_test_callback])

        # Start dispatcher
        await dispatcher.start()

        # Schedule job
        await dispatcher.schedule_job(training_job)
        assert custom_test_callback.scheduled_count == 1
        assert custom_test_callback.last_job == training_job

        # Simulate job start
        training_job.status = JobStatus.RUNNING
        dispatcher._notify_callbacks(  # pylint: disable=protected-access
            "on_job_start", training_job
        )
        assert custom_test_callback.start_job_count == 1

        # Simulate job completion
        training_job.status = JobStatus.COMPLETED
        training_job.result = {"accuracy": 0.95}
        dispatcher._notify_callbacks(  # pylint: disable=protected-access
            "on_job_complete", training_job
        )
        assert custom_test_callback.complete_count == 1
        assert custom_test_callback.last_job.result == {"accuracy": 0.95}
