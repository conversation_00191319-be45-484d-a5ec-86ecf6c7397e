"""
Unit tests for the ResourceMonitorCallback.

This module tests the functionality of the ResourceMonitorCallback class
which monitors system resources during job execution.
"""

# pylint: disable=duplicate-code

import asyncio
import time
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

import pytest

from src.jobs.base import Job
from src.jobs.callbacks.resource_monitor import ResourceMonitorConfig
from tests.unit.jobs.callbacks.job_callback_helpers import ResourceMonitorCallbackHelper


class TestResourceMonitorCallback:
    """Test the ResourceMonitorCallback class."""

    callback = None
    job_id = None
    job = None

    @pytest.fixture(autouse=True)
    async def setup_callback(self):
        """Set up the callback for testing."""
        # Create a testable callback with default settings
        config = ResourceMonitorConfig(
            monitoring_interval=0.1,
            cpu_threshold=80.0,
            memory_threshold=80.0,
            gpu_memory_threshold=80.0,
        )
        self.callback = ResourceMonitorCallbackHelper(config)

        # Patch asyncio.get_event_loop().time() to avoid the "There is no current event loop" error
        with patch("asyncio.get_event_loop") as mock_get_loop:
            mock_loop = MagicMock()
            mock_loop.time.return_value = time.time()
            mock_get_loop.return_value = mock_loop

            # Create a mock job
            self.job_id = str(uuid4())
            self.job = Job(job_id=self.job_id, data={})

        yield

        # Clean up any monitoring tasks
        for _, task in list(self.callback.get_monitoring_tasks().items()):
            task.cancel()

    def test_init(self):
        """Test initialization of ResourceMonitorCallback."""
        config = ResourceMonitorConfig(
            monitoring_interval=45.0,
            cpu_threshold=85.0,
            memory_threshold=75.0,
            gpu_memory_threshold=95.0,
        )
        callback = ResourceMonitorCallbackHelper(config)

        assert callback.monitoring_interval == 45.0
        assert callback.cpu_threshold == 85.0
        assert callback.memory_threshold == 75.0
        assert callback.gpu_memory_threshold == 95.0
        assert not callback.get_monitoring_tasks()

    @pytest.mark.asyncio
    async def test_on_job_start_creates_monitoring_task(self):
        """Test that on_job_start creates a monitoring task."""
        # Call the callback method
        self.callback.on_job_start(self.job)

        # Verify that a task was created
        assert len(self.callback.get_monitoring_tasks()) == 1
        assert self.callback.has_monitoring_task(self.job_id)
        assert isinstance(self.callback.get_monitoring_task(self.job_id), asyncio.Task)

        # Clean up
        task = self.callback.get_monitoring_task(self.job_id)
        if task:
            task.cancel()
        await asyncio.sleep(0.1)

    @pytest.mark.asyncio
    async def test_on_job_complete_cancels_monitoring_task(self):
        """Test that on_job_complete stops the monitoring task."""
        # Create a mock task
        mock_task = AsyncMock()
        mock_task.cancel = MagicMock()
        # Add the mock task to the monitoring tasks using our test helper method
        self.callback.get_monitoring_tasks()[self.job_id] = mock_task

        # Call the callback method
        self.callback.on_job_complete(self.job)

        # Verify that the task was cancelled
        assert not self.callback.has_monitoring_task(self.job_id)
        mock_task.cancel.assert_called_once()

    @pytest.mark.asyncio
    async def test_on_job_failed_cancels_monitoring_task(self):
        """Test that on_job_failed stops the monitoring task."""
        # Create a mock task
        mock_task = AsyncMock()
        mock_task.cancel = MagicMock()
        # Add the mock task to the monitoring tasks using our test helper method
        self.callback.get_monitoring_tasks()[self.job_id] = mock_task

        # Call the callback method
        self.callback.on_job_failed(self.job, Exception("Test error"))

        # Verify that the task was cancelled
        assert not self.callback.has_monitoring_task(self.job_id)
        mock_task.cancel.assert_called_once()

    @pytest.mark.asyncio
    async def test_on_job_cancelled_cancels_monitoring_task(self):
        """Test that on_job_cancelled stops the monitoring task."""
        # Create a mock task
        mock_task = AsyncMock()
        mock_task.cancel = MagicMock()
        # Add the mock task to the monitoring tasks using our test helper method
        self.callback.get_monitoring_tasks()[self.job_id] = mock_task

        # Call the callback method
        self.callback.on_job_cancelled(self.job)

        # Verify that the task was cancelled
        assert not self.callback.has_monitoring_task(self.job_id)
        mock_task.cancel.assert_called_once()

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.resource_monitor.psutil")
    @patch("src.jobs.callbacks.resource_monitor.torch.cuda")
    async def test_monitor_resources_collects_metrics(self, mock_cuda, mock_psutil):
        """Test that monitor_resources collects and stores metrics."""
        # Setup mocks
        mock_psutil.cpu_percent.return_value = 50.0
        mock_memory = MagicMock()
        mock_memory.percent = 60.0
        mock_memory.available = 4 * (1024**3)  # 4GB
        mock_psutil.virtual_memory.return_value = mock_memory

        # Setup CUDA mock
        mock_cuda.is_available.return_value = True
        mock_cuda.memory_allocated.return_value = 2 * (1024**3)  # 2GB
        mock_cuda.memory_reserved.return_value = 8 * (1024**3)  # 8GB

        # Create a job with empty metadata
        job = Job(job_id=str(uuid4()), data={})
        job.metadata = {}

        # Initialize metrics by calling on_job_start
        self.callback.on_job_start(job)

        # Start monitoring in a task that we'll cancel after a short time
        task = self.callback.monitor_resources(job)

        # Wait for a couple of monitoring intervals
        await asyncio.sleep(0.25)

        # Cancel the task
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

        # Verify that metrics were collected in the Metrics instance (before cleanup)
        job_metrics = self.callback.get_job_metrics(job.job_id)
        assert job_metrics is not None

        # Check the metrics in the Metrics instance
        assert len(job_metrics.storage.resources["cpu_percent"]) > 0
        assert job_metrics.storage.resources["cpu_percent"][0] == 50.0
        assert job_metrics.storage.resources["memory_percent"][0] == 60.0
        assert job_metrics.storage.resources["memory_available_gb"][0] == 4.0
        assert job_metrics.storage.resources["gpu_memory_used_gb"][0] == 2.0
        assert job_metrics.storage.resources["gpu_memory_reserved_gb"][0] == 8.0
        assert (
            job_metrics.storage.resources["gpu_memory_percent"][0] == 25.0
        )  # 2/8 * 100

        # Stop monitoring to finalize metrics
        self.callback.on_job_complete(job)

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.resource_monitor.psutil")
    @patch("src.jobs.callbacks.resource_monitor.torch.cuda")
    @patch("src.jobs.callbacks.resource_monitor.logger")
    async def test_error_handling_in_monitor_resources(
        self, mock_logger, _mock_cuda, mock_psutil
    ):
        """Test error handling in monitor_resources method."""
        # Setup mock to raise an exception
        mock_psutil.cpu_percent.side_effect = Exception("Test error")

        # Create a job
        job = Job(job_id=str(uuid4()), data={})

        # Initialize metrics by calling on_job_start
        self.callback.on_job_start(job)

        # Run the monitoring function using our public wrapper
        task = self.callback.monitor_resources(job)

        # Wait for the error to be logged
        await asyncio.sleep(0.2)

        # Cancel the task
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

        # Verify the error was logged
        assert mock_logger.error.called, "logger.error was not called"

        # Assuming the error is logged at least once, check the details of the last logged error.
        # In this test setup, subsequent errors will have the same details.
        last_call_args = mock_logger.error.call_args[0]

        expected_format_string = "Error collecting resource metrics for job %s: %s"
        assert (
            last_call_args[0] == expected_format_string
        ), f"Expected format string '{expected_format_string}', got '{last_call_args[0]}'"

        assert (
            last_call_args[1] == job.job_id
        ), f"Expected job_id '{job.job_id}', got '{last_call_args[1]}'"

        # Check that the exception was passed to the logger
        # The exception is passed as an argument, not as a string
        logged_exception = last_call_args[2]
        assert isinstance(
            logged_exception, Exception
        ), f"Expected logged_exception to be an instance of Exception, got {type(logged_exception)}"
        assert (
            str(logged_exception) == "Test error"
        ), f"Expected exception message 'Test error', got '{str(logged_exception)}'"

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.resource_monitor.psutil")
    @patch("src.jobs.callbacks.resource_monitor.torch.cuda")
    async def test_gpu_metrics_when_cuda_available(self, mock_cuda, mock_psutil):
        """Test that GPU metrics are collected when CUDA is available."""
        # Setup mocks
        mock_psutil.cpu_percent.return_value = 50.0
        mock_memory = MagicMock()
        mock_memory.percent = 60.0
        mock_memory.available = 4 * (1024**3)
        mock_psutil.virtual_memory.return_value = mock_memory

        # Setup CUDA mock
        mock_cuda.is_available.return_value = True
        mock_cuda.memory_allocated.return_value = 2 * (1024**3)
        mock_cuda.memory_reserved.return_value = 8 * (1024**3)

        # Create a job with empty metadata
        job = Job(job_id=str(uuid4()), data={})
        job.metadata = {}

        # Initialize metrics by calling on_job_start
        self.callback.on_job_start(job)

        # Start monitoring in a task that we'll cancel after a short time
        task = self.callback.monitor_resources(job)

        # Wait for a monitoring interval
        await asyncio.sleep(0.15)

        # Cancel the task
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

        # Verify that GPU metrics were collected in the Metrics instance
        job_metrics = self.callback.get_job_metrics(job.job_id)
        assert job_metrics is not None
        assert len(job_metrics.storage.resources["gpu_memory_used_gb"]) > 0
        assert len(job_metrics.storage.resources["gpu_memory_reserved_gb"]) > 0
        assert len(job_metrics.storage.resources["gpu_memory_percent"]) > 0

        # Stop monitoring to finalize metrics
        self.callback.on_job_complete(job)

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.resource_monitor.psutil")
    @patch("src.jobs.callbacks.resource_monitor.torch.cuda")
    async def test_gpu_metrics_when_cuda_not_available(self, mock_cuda, mock_psutil):
        """Test that GPU metrics are not collected when CUDA is not available."""
        # Setup mocks
        mock_psutil.cpu_percent.return_value = 50.0
        mock_memory = MagicMock()
        mock_memory.percent = 60.0
        mock_memory.available = 4 * (1024**3)
        mock_psutil.virtual_memory.return_value = mock_memory

        # Setup CUDA mock to not be available
        mock_cuda.is_available.return_value = False

        # Create a job with empty metadata
        job = Job(job_id=str(uuid4()), data={})
        job.metadata = {}

        # Initialize metrics by calling on_job_start
        self.callback.on_job_start(job)

        # Start monitoring in a task that we'll cancel after a short time
        task = self.callback.monitor_resources(job)

        # Wait for a monitoring interval
        await asyncio.sleep(0.15)

        # Cancel the task
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

        # Verify that GPU metrics were not collected in the Metrics instance
        job_metrics = self.callback.get_job_metrics(job.job_id)
        assert job_metrics is not None
        assert len(job_metrics.storage.resources.get("gpu_memory_used_gb", [])) == 0
        assert len(job_metrics.storage.resources.get("gpu_memory_reserved_gb", [])) == 0
        assert len(job_metrics.storage.resources.get("gpu_memory_percent", [])) == 0

        # Stop monitoring to finalize metrics
        self.callback.on_job_complete(job)
