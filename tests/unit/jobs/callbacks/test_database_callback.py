"""
Unit tests for the DatabaseUpdateCallback.

This module tests the functionality of the DatabaseUpdateCallback class
which updates the database when job events occur.
"""

# pylint: disable=duplicate-code

import asyncio
import time
from datetime import datetime
from unittest.mock import ANY, MagicMock, patch
from uuid import uuid4

import pytest

from src.database.services.model_run_service import (
    ModelRunCompleteUpdate,
    ModelRunTimingUpdate,
)
from src.jobs.base import Job
from src.jobs.callbacks.database import DatabaseUpdateCallback
from src.jobs.training_data import TrainingJob, TrainingJobData
from src.train.metrics import Metrics


class TestDatabaseUpdateCallback:
    """Test the DatabaseUpdateCallback class."""

    model_run_uuid = None
    job_id = None
    job_data = None
    job = None
    regular_job = None
    error = None
    callback = None

    def setup_method(self):
        """Set up test fixtures before each test method."""
        # Patch asyncio.get_event_loop().time() to avoid the "There is no current event loop" error
        with patch("asyncio.get_event_loop") as mock_get_loop:
            mock_loop = MagicMock()
            mock_loop.time.return_value = time.time()
            mock_get_loop.return_value = mock_loop

            # Create a mock training job
            self.model_run_uuid = str(uuid4())
            self.job_id = str(uuid4())
            self.job_data = TrainingJobData(
                model_run_uuid=self.model_run_uuid,
                model_components={},
                data_loaders={},
                training_config={},
                callbacks=[],
            )
            self.job = TrainingJob(job_id=self.job_id, data=self.job_data)

            # Create a regular job (not a training job)
            self.regular_job = Job(job_id=str(uuid4()), data={})

        # Create an error for testing failure cases
        self.error = Exception("Test error")

    @pytest.fixture(autouse=True)
    async def setup_callback(self):
        """Set up the callback for testing."""
        # Create a callback
        self.callback = DatabaseUpdateCallback()
        # Patch maybe_await to return the input directly
        patcher = patch("src.jobs.callbacks.database.maybe_await", lambda x: x)
        patcher.start()

        yield

        # Clean up
        patcher.stop()

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.database.ModelRunService")
    @patch("src.jobs.callbacks.database.asyncio.create_task")
    async def test_on_job_start_training_job(
        self, mock_create_task, mock_model_run_service
    ):
        """Test that on_job_start updates the database for training jobs."""
        # Setup mock to run the coroutine immediately instead of creating a task
        mock_create_task.side_effect = asyncio.ensure_future

        # Setup mock service
        mock_service_instance = MagicMock()
        mock_model_run_service.return_value = mock_service_instance
        # Use a regular MagicMock since we patched maybe_await
        mock_service_instance.update_model_run_times = MagicMock()

        # Call the callback method
        self.callback.on_job_start(self.job)

        # Wait for any async tasks to complete
        await asyncio.sleep(0.5)

        # Verify the service was called with correct parameters
        mock_service_instance.update_model_run_times.assert_called_once()

        # Get the actual call arguments
        call_args = mock_service_instance.update_model_run_times.call_args[0][0]
        assert isinstance(call_args, ModelRunTimingUpdate)
        assert call_args.model_run_uuid == self.model_run_uuid
        assert isinstance(call_args.start_time, datetime)

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.database.ModelRunService")
    @patch("src.jobs.callbacks.database.asyncio.create_task")
    async def test_on_job_start_regular_job(
        self, mock_create_task, mock_model_run_service
    ):
        """Test that on_job_start does nothing for non-training jobs."""
        # Setup mock to run the coroutine immediately instead of creating a task
        mock_create_task.side_effect = asyncio.ensure_future

        # Setup mock service
        mock_service_instance = mock_model_run_service.return_value
        mock_service_instance.update_model_run_times = MagicMock()

        # Call the callback method with a regular job
        self.callback.on_job_start(self.regular_job)

        # Wait for any async tasks to complete
        await asyncio.sleep(0.5)

        # Verify the service was not called
        mock_service_instance.update_model_run_times.assert_not_called()

    @pytest.fixture
    def mock_job_complete_dependencies(self, mocker, tmp_path):
        """Fixture to mock dependencies for on_job_complete tests."""
        mocks = {
            "create_task": mocker.patch(
                "src.jobs.callbacks.database.asyncio.create_task",
                side_effect=asyncio.ensure_future,
            ),
            "model_run_service_class": mocker.patch(
                "src.jobs.callbacks.database.ModelRunService"
            ),
            "metrics_persistence_class": mocker.patch(
                "src.jobs.callbacks.database.MetricsPersistence"
            ),
            "get_run_paths": mocker.patch("src.jobs.callbacks.database.get_run_paths"),
        }

        # Configure service instance mock
        mock_service_instance = mocks["model_run_service_class"].return_value
        mock_service_instance.update_model_run_complete = MagicMock()
        mocks["service_instance"] = mock_service_instance

        # Configure run paths mock
        metrics_dir = tmp_path / "metrics"
        mock_run_paths = type("MockRunPaths", (), {"metrics": metrics_dir})()
        mocks["get_run_paths"].return_value = mock_run_paths
        mocks["metrics_dir"] = metrics_dir

        # Configure persistence instance mock
        mock_persistence_instance = mocks["metrics_persistence_class"].return_value
        expected_history_path = metrics_dir / "history.json"
        expected_summary_path = metrics_dir / "summary.json"
        mock_persistence_instance.save_metrics.return_value = {
            "history": expected_history_path,
            "summary": expected_summary_path,
        }
        mocks["persistence_instance"] = mock_persistence_instance
        mocks["expected_history_path"] = expected_history_path

        return mocks

    @pytest.mark.asyncio
    async def test_on_job_complete_training_job(self, mock_job_complete_dependencies):
        """Test that on_job_complete updates the database for training jobs."""
        # --- Setup Job and Metrics Data ---
        metrics = Metrics()
        metrics.storage.train.losses.append(0.1)
        self.job.result = metrics

        # --- Call the Method Under Test ---
        self.callback.on_job_complete(self.job)
        await asyncio.sleep(0.1)

        # --- Assertions ---
        mocks = mock_job_complete_dependencies
        mocks["get_run_paths"].assert_called_once_with(self.model_run_uuid)
        mocks["metrics_persistence_class"].assert_called_once_with(logger=ANY)
        mocks["persistence_instance"].save_metrics.assert_called_once()
        call_kwargs = mocks["persistence_instance"].save_metrics.call_args[1]
        assert call_kwargs["metrics"] is metrics
        assert call_kwargs["output_dir"] == mocks["metrics_dir"]

        mocks["service_instance"].update_model_run_complete.assert_called_once()
        update_arg = mocks["service_instance"].update_model_run_complete.call_args[0][0]
        assert isinstance(update_arg, ModelRunCompleteUpdate)
        assert update_arg.model_run_uuid == self.model_run_uuid
        assert isinstance(update_arg.end_time, datetime)
        assert update_arg.metrics_file_path == str(mocks["expected_history_path"])
        assert "final" in update_arg.metrics
        assert update_arg.metrics["final"]["train_loss"] == 0.1

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.database.MetricsPersistence")
    @patch("src.jobs.callbacks.database.ModelRunService")
    @patch("src.jobs.callbacks.database.asyncio.create_task")
    async def test_on_job_complete_with_logs(
        self, mock_create_task, mock_model_run_service, mock_metrics_persistence_class
    ):
        """Test on_job_complete with logs as a fallback."""
        mock_create_task.side_effect = asyncio.ensure_future
        mock_service_instance = mock_model_run_service.return_value
        mock_service_instance.update_model_run_complete = MagicMock()

        # Setup job result that is not a Metrics object
        self.job.result = "some_other_result"

        # Call the callback method with logs
        logs = {"accuracy": 0.95}
        self.callback.on_job_complete(self.job, logs=logs)
        await asyncio.sleep(0.1)  # Allow async task to run

        # Verify that MetricsPersistence was not instantiated
        mock_metrics_persistence_class.assert_not_called()

        # Verify the service was called with correct parameters
        mock_service_instance.update_model_run_complete.assert_called_once()
        update_arg = mock_service_instance.update_model_run_complete.call_args[0][0]
        assert isinstance(update_arg, ModelRunCompleteUpdate)
        assert update_arg.model_run_uuid == self.model_run_uuid
        assert isinstance(update_arg.end_time, datetime)
        assert update_arg.metrics == logs
        assert update_arg.metrics_file_path is None

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.database.ModelRunService")
    @patch("src.jobs.callbacks.database.asyncio.create_task")
    async def test_on_job_failed_training_job(
        self, mock_create_task, mock_model_run_service
    ):
        """Test that on_job_failed updates the database for training jobs."""
        # Setup mock to run the coroutine immediately instead of creating a task
        mock_create_task.side_effect = asyncio.ensure_future

        # Setup mock service
        mock_service_instance = mock_model_run_service.return_value
        mock_service_instance.update_model_run_complete = MagicMock()

        # Call the callback method
        self.callback.on_job_failed(self.job, self.error)

        # Wait for any async tasks to complete
        await asyncio.sleep(0.5)

        # Verify the service was called with correct parameters
        mock_service_instance.update_model_run_complete.assert_called_once()

        # Get the actual call arguments
        call_args = mock_service_instance.update_model_run_complete.call_args[0][0]
        assert isinstance(call_args, ModelRunCompleteUpdate)
        assert call_args.model_run_uuid == self.model_run_uuid
        assert isinstance(call_args.end_time, datetime)
        assert call_args.metrics["error"] == "Test error"
        assert call_args.metrics["error_type"] == "Exception"

    @pytest.mark.asyncio
    @patch("src.database.services.model_run_service.ModelRunService")
    @patch("src.jobs.callbacks.database.asyncio.create_task")
    async def test_on_job_failed_regular_job(
        self, mock_create_task, mock_model_run_service
    ):
        """Test that on_job_failed does nothing for non-training jobs."""
        # Setup mock to run the coroutine immediately instead of creating a task
        mock_create_task.side_effect = asyncio.ensure_future

        # Setup mock service
        mock_service_instance = mock_model_run_service.return_value
        mock_service_instance.update_model_run_complete = MagicMock()

        # Call the callback method with a regular job
        self.callback.on_job_failed(self.regular_job, self.error)

        # Wait for any async tasks to complete
        await asyncio.sleep(0.5)

        # Verify the service was not called
        mock_service_instance.update_model_run_complete.assert_not_called()

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.database.ModelRunService")
    @patch("src.jobs.callbacks.database.logger")
    @patch("src.jobs.callbacks.database.asyncio.create_task")
    async def test_error_handling_in_update_start(
        self, mock_create_task, mock_logger, mock_model_run_service
    ):
        """Test error handling in _update_training_job_start method."""
        # Setup mock to run the coroutine immediately instead of creating a task
        mock_create_task.side_effect = asyncio.ensure_future

        # Setup mock to raise an exception
        mock_service_instance = MagicMock()
        mock_model_run_service.return_value = mock_service_instance
        mock_service_instance.update_model_run_times.side_effect = Exception(
            "Database error"
        )

        # Call the callback method
        self.callback.on_job_start(self.job)

        # Wait for any async tasks to complete
        await asyncio.sleep(0.5)

        # Verify the error was logged
        mock_logger.error.assert_called_once()
        assert "Error updating training job start" in mock_logger.error.call_args[0][0]

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.database.ModelRunService")
    @patch("src.jobs.callbacks.database.logger")
    @patch("src.jobs.callbacks.database.asyncio.create_task")
    async def test_error_handling_in_update_complete(
        self, mock_create_task, mock_logger, mock_model_run_service
    ):
        """Test error handling in _update_training_job_complete method."""
        # Setup mock to run the coroutine immediately instead of creating a task
        mock_create_task.side_effect = asyncio.ensure_future

        # Setup mock to raise an exception
        mock_service_instance = mock_model_run_service.return_value
        mock_service_instance.update_model_run_complete.side_effect = Exception(
            "Database error"
        )

        # Call the callback method
        self.callback.on_job_complete(self.job)

        # Wait for any async tasks to complete
        await asyncio.sleep(0.5)

        # Verify the error was logged
        mock_logger.error.assert_called_once()
        assert (
            "Error updating training job completion"
            in mock_logger.error.call_args[0][0]
        )

    @pytest.mark.asyncio
    @patch("src.jobs.callbacks.database.ModelRunService")
    @patch("src.jobs.callbacks.database.logger")
    @patch("src.jobs.callbacks.database.asyncio.create_task")
    async def test_error_handling_in_update_failed(
        self, mock_create_task, mock_logger, mock_model_run_service
    ):
        """Test error handling in _update_training_job_failed method."""
        # Setup mock to run the coroutine immediately instead of creating a task
        mock_create_task.side_effect = asyncio.ensure_future

        # Setup mock to raise an exception
        mock_service_instance = mock_model_run_service.return_value
        mock_service_instance.update_model_run_complete.side_effect = Exception(
            "Database error"
        )

        # Call the callback method
        self.callback.on_job_failed(self.job, self.error)

        # Wait for any async tasks to complete
        await asyncio.sleep(0.5)

        # Verify the error was logged
        mock_logger.error.assert_called_once()
        assert (
            "Error updating training job failure" in mock_logger.error.call_args[0][0]
        )
