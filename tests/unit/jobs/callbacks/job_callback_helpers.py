"""
Helper classes for testing callbacks.

This module provides test-specific subclasses of callbacks with
additional methods to facilitate testing.
"""

import asyncio
from typing import Dict, Optional

from src.jobs.base import Job
from src.jobs.callbacks.resource_monitor import (
    ResourceMonitorCallback,
    ResourceMonitorConfig,
)
from src.train.metrics import Metrics


class ResourceMonitorCallbackHelper(ResourceMonitorCallback):
    """
    A testable version of ResourceMonitorCallback with public methods
    for accessing protected members during tests.
    """

    def __init__(self, config: Optional[ResourceMonitorConfig] = None):
        """Initialize with optional config and add convenience properties for tests."""
        super().__init__(config)

        # Add convenience properties for backward compatibility in tests
        self.monitoring_interval = self.config.monitoring_interval
        self.cpu_threshold = self.config.cpu_threshold
        self.memory_threshold = self.config.memory_threshold
        self.gpu_memory_threshold = self.config.gpu_memory_threshold
        self.save_metrics = self.config.save_metrics

    def get_monitoring_tasks(self) -> Dict[str, asyncio.Task]:
        """
        Get the monitoring tasks dictionary.

        Returns:
            Dict of job_id to monitoring task
        """
        return self._monitoring_tasks

    def get_monitoring_task(self, job_id: str) -> Optional[asyncio.Task]:
        """
        Get the monitoring task for a specific job.

        Args:
            job_id: The job ID to get the task for

        Returns:
            The monitoring task or None if not found
        """
        return self._monitoring_tasks.get(job_id)

    def has_monitoring_task(self, job_id: str) -> bool:
        """
        Check if a monitoring task exists for a job.

        Args:
            job_id: The job ID to check

        Returns:
            True if a monitoring task exists, False otherwise
        """
        return job_id in self._monitoring_tasks

    def monitor_resources(self, job: Job) -> asyncio.Task:
        """
        Public wrapper for _monitor_resources to use in tests.

        Args:
            job: The job to monitor

        Returns:
            The created monitoring task
        """
        return asyncio.create_task(self._monitor_resources(job))

    def get_job_metrics(self, job_id: str) -> Optional[Metrics]:
        """
        Get the Metrics instance for a specific job.

        Args:
            job_id: The job ID to get metrics for

        Returns:
            The Metrics instance or None if not found
        """
        return self._job_metrics.get(job_id)

    def get_all_job_metrics(self) -> Dict[str, Metrics]:
        """
        Get all job metrics.

        Returns:
            Dictionary mapping job IDs to Metrics instances
        """
        return self._job_metrics
