"""
Unit tests for the metrics and metrics persistence classes.
"""

import json
from unittest.mock import MagicMock

import pytest

from src.train.metrics import Metrics, MetricsDict
from src.train.metrics_persistence import MetricsPersistence


class TestMetricsDict:
    """Tests for the MetricsDict class."""

    def test_get_latest(self):
        """Test getting the latest value."""
        metrics_dict = MetricsDict(list)
        metrics_dict["test"] = [1, 2, 3]
        assert metrics_dict.get_latest("test") == 3
        assert metrics_dict.get_latest("nonexistent", "default") == "default"

    def test_get_max(self):
        """Test getting the maximum value."""
        metrics_dict = MetricsDict(list)
        metrics_dict["test"] = [1, 5, 3]
        assert metrics_dict.get_max("test") == 5
        assert metrics_dict.get_max("nonexistent", "default") == "default"

    def test_get_min(self):
        """Test getting the minimum value."""
        metrics_dict = MetricsDict(list)
        metrics_dict["test"] = [3, 1, 5]
        assert metrics_dict.get_min("test") == 1
        assert metrics_dict.get_min("nonexistent", "default") == "default"

    def test_get_mean(self):
        """Test getting the mean value."""
        metrics_dict = MetricsDict(list)
        metrics_dict["test"] = [1, 2, 3]
        assert metrics_dict.get_mean("test") == 2
        assert metrics_dict.get_mean("nonexistent", "default") == "default"


class TestMetrics:
    """Tests for the Metrics class."""

    def test_initialization(self):
        """Test initialization of metrics."""
        metrics = Metrics()
        assert metrics.epoch_metrics == []
        assert metrics.batch_metrics == []
        assert metrics.storage.train.losses == []
        assert metrics.storage.train.accuracies == []
        assert metrics.storage.test.losses == []
        assert metrics.storage.test.accuracies == []
        assert metrics.storage.classification.precision == []
        assert metrics.storage.classification.recall == []
        assert metrics.storage.classification.f1_score == []
        assert isinstance(metrics.storage.timing, MetricsDict)
        assert isinstance(metrics.storage.resources, MetricsDict)
        assert metrics.custom_metrics == {}
        assert metrics.error is None

    def test_add_epoch_metric(self):
        """Test adding epoch metrics."""
        metrics = Metrics()
        epoch_data = {
            "train_loss": 0.5,
            "train_accuracy": 0.8,
            "validation_loss": 0.4,
            "validation_accuracy": 0.85,
            "precision": 0.75,
            "recall": 0.70,
            "f1_score": 0.72,
            "other_metric": "value",
        }
        metrics.add_epoch_metric(epoch_data)

        assert len(metrics.epoch_metrics) == 1
        assert metrics.epoch_metrics[0] == epoch_data
        assert metrics.storage.train.losses == [0.5]
        assert metrics.storage.train.accuracies == [0.8]
        assert metrics.storage.test.losses == [0.4]
        assert metrics.storage.test.accuracies == [0.85]
        assert metrics.storage.classification.precision == [0.75]
        assert metrics.storage.classification.recall == [0.70]
        assert metrics.storage.classification.f1_score == [0.72]

    def test_add_batch_metric(self):
        """Test adding batch metrics."""
        metrics = Metrics()
        batch_data = {"batch_loss": 0.3, "batch_accuracy": 0.75}
        metrics.add_batch_metric(batch_data)

        assert len(metrics.batch_metrics) == 1
        assert metrics.batch_metrics[0] == batch_data

    def test_add_timing(self):
        """Test adding timing metrics."""
        metrics = Metrics()
        metrics.add_timing("epoch_time", 10.5)
        metrics.add_timing("epoch_time", 11.2)

        assert metrics.storage.timing["epoch_time"] == [10.5, 11.2]

    def test_add_resource(self):
        """Test adding resource metrics."""
        metrics = Metrics()
        metrics.add_resource("cpu_percent", 45.6)
        metrics.add_resource("cpu_percent", 50.2)

        assert metrics.storage.resources["cpu_percent"] == [45.6, 50.2]

    def test_add_custom_metric(self):
        """Test adding custom metrics."""
        metrics = Metrics()
        metrics.add_custom_metric("custom1", 5)
        metrics.add_custom_metric("custom1", 10)
        metrics.add_custom_metric("custom2", "value")

        assert metrics.custom_metrics["custom1"] == [5, 10]
        assert metrics.custom_metrics["custom2"] == "value"

    def test_set_error(self):
        """Test setting error information."""
        metrics = Metrics()
        metrics.set_error("RuntimeError", "Something went wrong")

        assert metrics.error == {
            "type": "RuntimeError",
            "message": "Something went wrong",
        }

    def test_get_metric_summary(self):
        """Test getting metrics summary."""
        metrics = Metrics()
        metrics.storage.train.losses = [0.5, 0.4, 0.3]
        metrics.storage.train.accuracies = [0.7, 0.8, 0.9]
        metrics.storage.test.losses = [0.45, 0.35, 0.25]
        metrics.storage.test.accuracies = [0.75, 0.85, 0.95]
        metrics.storage.classification.precision = [0.75, 0.80, 0.85]
        metrics.storage.classification.recall = [0.70, 0.75, 0.80]
        metrics.storage.classification.f1_score = [0.72, 0.77, 0.82]
        metrics.add_timing("epoch_time", 10.0)
        metrics.add_resource("cpu_percent", 50.0)
        metrics.add_custom_metric("custom1", 5)

        summary = metrics.get_metric_summary()

        assert "final" in summary
        final = summary["final"]
        assert final["train_loss"] == 0.3
        assert final["train_accuracy"] == 0.9
        assert final["test_loss"] == 0.25
        assert final["test_accuracy"] == 0.95
        assert final["precision"] == 0.85
        assert final["recall"] == 0.80
        assert final["f1_score"] == 0.82
        assert "timing" in summary
        assert "epoch_time" in summary["timing"]
        assert "resources" in summary
        assert "cpu_percent_max" in summary["resources"]
        assert "custom_metrics" in summary
        assert summary["custom_metrics"]["custom1"] == 5

    def test_get(self):
        """Test getting metrics by key."""
        metrics = Metrics()
        metrics.storage.train.losses = [0.5, 0.4]
        metrics.add_timing("epoch_time", 10.0)
        metrics.add_custom_metric("custom1", 5)

        assert metrics.get("train_losses") == [0.5, 0.4]
        assert metrics.get("epoch_time") == [10.0]
        assert metrics.get("custom1") == 5
        assert metrics.get("nonexistent", "default") == "default"

    def test_to_dict(self):
        """Test converting metrics to dictionary."""
        metrics = Metrics()
        metrics.storage.train.losses = [0.5]
        metrics.add_timing("epoch_time", 10.0)

        metrics_dict = metrics.to_dict()

        assert isinstance(metrics_dict, dict)
        assert metrics_dict["train"]["losses"] == [0.5]
        assert "timing" in metrics_dict
        assert metrics_dict["timing"]["epoch_time"] == [10.0]

    def test_from_dict(self):
        """Test creating metrics from dictionary."""
        # Test with nested structure format
        metrics_dict = {
            "train": {"losses": [0.5], "accuracies": [0.8]},
            "timing": {"epoch_time": [10.0]},
            "resources": {"cpu_percent": [50.0]},
            "custom_metrics": {"custom1": 5},
        }

        metrics = Metrics.from_dict(metrics_dict)

        assert metrics.storage.train.losses == [0.5]
        assert metrics.storage.train.accuracies == [0.8]
        assert metrics.storage.timing["epoch_time"] == [10.0]
        assert metrics.storage.resources["cpu_percent"] == [50.0]
        assert metrics.custom_metrics["custom1"] == 5

        # Test with another nested structure format
        new_metrics_dict = {
            "train": {"losses": [0.6], "accuracies": [0.9]},
            "test": {"losses": [0.4], "accuracies": [0.85]},
            "classification": {
                "precision": [0.75],
                "recall": [0.7],
                "f1_score": [0.72],
            },
            "timing": {"epoch_time": [11.0]},
            "custom_metrics": {"custom2": 10},
        }

        metrics = Metrics.from_dict(new_metrics_dict)

        assert metrics.storage.train.losses == [0.6]
        assert metrics.storage.test.losses == [0.4]
        assert metrics.storage.classification.precision == [0.75]
        assert metrics.storage.timing["epoch_time"] == [11.0]
        assert metrics.custom_metrics["custom2"] == 10


class TestMetricsPersistence:
    """Tests for the MetricsPersistence class."""

    def test_save_metrics(self, tmp_path):
        """Test saving metrics to files."""
        metrics = Metrics()
        metrics.storage.train.losses = [0.5, 0.4, 0.3]
        metrics.storage.train.accuracies = [0.7, 0.8, 0.9]
        metrics.add_timing("epoch_time", 10.0)

        persistence = MetricsPersistence(logger=MagicMock())
        saved_files = persistence.save_metrics(metrics, tmp_path)

        assert "history" in saved_files
        assert "summary" in saved_files
        assert saved_files["history"].exists()
        assert saved_files["summary"].exists()

        # Check history file content
        with open(saved_files["history"], "r", encoding="utf-8") as f:
            history_data = json.load(f)
        assert history_data["train"]["losses"] == [0.5, 0.4, 0.3]
        assert history_data["train"]["accuracies"] == [0.7, 0.8, 0.9]
        assert "timing" in history_data

        # Check summary file content
        with open(saved_files["summary"], "r", encoding="utf-8") as f:
            summary_data = json.load(f)
        assert "final" in summary_data
        final = summary_data["final"]
        assert final["train_loss"] == 0.3
        assert final["train_accuracy"] == 0.9

    def test_load_metrics(self, tmp_path):
        """Test loading metrics from a file."""
        # Create a metrics file with new format only
        metrics_data = {
            "train": {"losses": [0.5, 0.4], "accuracies": [0.7, 0.8]},
            "timing": {"epoch_time": [10.0]},
            "custom_metrics": {"custom1": 5},
        }

        metrics_file = tmp_path / "metrics_history.json"
        with open(metrics_file, "w", encoding="utf-8") as f:
            json.dump(metrics_data, f)

        persistence = MetricsPersistence(logger=MagicMock())
        loaded_metrics = persistence.load_metrics(metrics_file)

        # Should load the new format correctly
        assert loaded_metrics.storage.train.losses == [0.5, 0.4]
        assert loaded_metrics.storage.train.accuracies == [0.7, 0.8]
        assert loaded_metrics.storage.timing["epoch_time"] == [10.0]
        assert loaded_metrics.custom_metrics["custom1"] == 5

        # Create a metrics file with new format
        new_metrics_data = {
            "train": {"losses": [0.6, 0.5], "accuracies": [0.8, 0.9]},
            "test": {"losses": [0.4, 0.3], "accuracies": [0.85, 0.9]},
            "timing": {"epoch_time": [11.0]},
            "custom_metrics": {"custom2": 10},
        }

        new_metrics_file = tmp_path / "new_metrics_history.json"
        with open(new_metrics_file, "w", encoding="utf-8") as f:
            json.dump(new_metrics_data, f)

        loaded_new_metrics = persistence.load_metrics(new_metrics_file)

        assert loaded_new_metrics.storage.train.losses == [0.6, 0.5]
        assert loaded_new_metrics.storage.test.losses == [0.4, 0.3]
        assert loaded_new_metrics.storage.timing["epoch_time"] == [11.0]
        assert loaded_new_metrics.custom_metrics["custom2"] == 10

    def test_update_existing_metrics(self, tmp_path):
        """Test updating existing metrics from a file."""
        # Create a metrics file with new nested structure
        metrics_data = {
            "train": {"losses": [0.5, 0.4], "accuracies": [0.7, 0.8]},
            "timing": {"epoch_time": [10.0]},
            "custom_metrics": {"custom1": 5},
        }

        metrics_file = tmp_path / "metrics_history.json"
        with open(metrics_file, "w", encoding="utf-8") as f:
            json.dump(metrics_data, f)

        # Create existing metrics instance
        existing_metrics = Metrics()
        existing_metrics.storage.test.losses = [0.45, 0.35]
        existing_metrics.add_custom_metric("custom2", 10)

        persistence = MetricsPersistence(logger=MagicMock())
        updated_metrics = persistence.load_metrics(metrics_file, existing_metrics)

        # Should update with file data
        assert updated_metrics.storage.train.losses == [0.5, 0.4]
        assert updated_metrics.storage.train.accuracies == [0.7, 0.8]
        assert updated_metrics.storage.timing["epoch_time"] == [10.0]
        assert updated_metrics.custom_metrics["custom1"] == 5

        # Should keep existing data for fields not in the file
        assert updated_metrics.storage.test.losses == [0.45, 0.35]
        # For numeric values, we store them as lists
        assert updated_metrics.custom_metrics["custom2"] == [10]
        # But when accessing via get(), we should get the single value
        assert updated_metrics.get("custom2") == 10

    def test_load_nonexistent_file(self):
        """Test loading from a nonexistent file."""
        persistence = MetricsPersistence(logger=MagicMock())
        with pytest.raises(
            FileNotFoundError, match="Metrics file not found: nonexistent_file.json"
        ):
            persistence.load_metrics("nonexistent_file.json")
