"""
Tests for the callback system in src.train.callbacks.
"""

from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

from src.train.callbacks import Callback, CallbackHandler
from src.train.callbacks.early_stopping import EarlyStoppingCallback
from src.train.callbacks.model_run import ModelRunCallback


@pytest.fixture
def _mock_trainer():  # Renamed fixture
    """Fixture to create a mock trainer object."""
    trainer = MagicMock()
    trainer.stop_training = False
    return trainer


class TestCallbackHandler:
    """Tests for the CallbackHandler."""

    def test_initialization(self, _mock_trainer):  # Updated argument name
        """Test that the handler initializes and sets the trainer on callbacks."""
        mock_callback1 = Mock(spec=Callback)
        mock_callback2 = Mock(spec=Callback)
        callbacks = [mock_callback1, mock_callback2]

        handler = Callback<PERSON>andler(callbacks, _mock_trainer)

        assert handler.callbacks == callbacks
        mock_callback1.set_trainer.assert_called_once_with(_mock_trainer)
        mock_callback2.set_trainer.assert_called_once_with(_mock_trainer)

    def test_event_propagation(self, _mock_trainer):  # Updated argument name
        """Test that events are correctly propagated to all callbacks."""
        mock_callback = Mock(spec=Callback)
        handler = CallbackHandler([mock_callback], _mock_trainer)

        handler.on_train_begin()
        mock_callback.on_train_begin.assert_called_once()

        handler.on_train_end()
        mock_callback.on_train_end.assert_called_once()

        handler.on_epoch_begin(epoch=1)
        mock_callback.on_epoch_begin.assert_called_once_with(1, None)

        logs = {"loss": 0.5}
        handler.on_epoch_end(epoch=1, logs=logs)
        mock_callback.on_epoch_end.assert_called_once_with(1, logs)

        handler.on_batch_begin(batch=1)
        mock_callback.on_batch_begin.assert_called_once_with(1, None)

        handler.on_batch_end(batch=1, logs=logs)
        mock_callback.on_batch_end.assert_called_once_with(1, logs)


class TestEarlyStoppingCallback:
    """Tests for the EarlyStoppingCallback."""

    def test_initialization(self):
        """Test that the callback initializes with correct defaults and custom values."""
        callback = EarlyStoppingCallback()
        assert callback.monitor == "validation_loss"
        assert callback.patience == 5
        assert callback.mode == "min"

        custom_callback = EarlyStoppingCallback(
            monitor="accuracy", patience=10, min_delta=0.1, mode="max", verbose=False
        )
        assert custom_callback.monitor == "accuracy"
        assert custom_callback.patience == 10
        assert custom_callback.min_delta == 0.1
        assert custom_callback.mode == "max"
        assert custom_callback.verbose is False

    def test_min_mode_logic(self, _mock_trainer):  # Updated argument name
        """Test the logic for 'min' mode (e.g., loss)."""
        callback = EarlyStoppingCallback(patience=2, verbose=False)
        callback.set_trainer(_mock_trainer)  # Updated usage

        # First epoch, sets baseline
        callback.on_epoch_end(1, {"validation_loss": 0.5})
        assert callback.best_score == 0.5
        assert callback.wait == 0

        # Second epoch, improves
        callback.on_epoch_end(2, {"validation_loss": 0.4})
        assert callback.best_score == 0.4
        assert callback.wait == 0
        assert not _mock_trainer.stop_training  # Updated usage

        # Third epoch, no improvement
        callback.on_epoch_end(3, {"validation_loss": 0.45})
        assert callback.wait == 1
        assert not _mock_trainer.stop_training  # Updated usage

        # Fourth epoch, no improvement
        callback.on_epoch_end(4, {"validation_loss": 0.5})
        assert callback.wait == 2
        assert _mock_trainer.stop_training  # Updated usage

    def test_max_mode_logic(self, _mock_trainer):  # Updated argument name
        """Test the logic for 'max' mode (e.g., accuracy)."""
        callback = EarlyStoppingCallback(
            monitor="acc", patience=2, mode="max", verbose=False
        )
        callback.set_trainer(_mock_trainer)  # Updated usage

        callback.on_epoch_end(1, {"acc": 0.8})
        assert callback.best_score == 0.8
        assert callback.wait == 0

        callback.on_epoch_end(2, {"acc": 0.75})  # No improvement
        assert callback.wait == 1
        assert not _mock_trainer.stop_training  # Updated usage

        callback.on_epoch_end(3, {"acc": 0.9})  # Improvement
        assert callback.best_score == 0.9
        assert callback.wait == 0

        callback.on_epoch_end(4, {"acc": 0.88})  # No improvement
        callback.on_epoch_end(5, {"acc": 0.89})  # No improvement
        assert callback.wait == 2
        assert _mock_trainer.stop_training  # Updated usage

    def test_min_delta(self, _mock_trainer):  # Updated argument name
        """Test that improvements are only counted if they exceed min_delta."""
        callback = EarlyStoppingCallback(patience=1, min_delta=0.1, verbose=False)
        callback.set_trainer(_mock_trainer)  # Updated usage

        callback.on_epoch_end(1, {"validation_loss": 0.5})
        assert callback.wait == 0

        # Improvement is less than min_delta, so it shouldn't count
        callback.on_epoch_end(2, {"validation_loss": 0.45})
        assert callback.wait == 1
        assert _mock_trainer.stop_training  # Updated usage

    def test_missing_metric(self, _mock_trainer):  # Updated argument name
        """Test that the callback handles logs with missing metrics gracefully."""
        callback = EarlyStoppingCallback(verbose=False)
        callback.set_trainer(_mock_trainer)  # Updated usage

        with patch("src.train.callbacks.early_stopping.logger") as mock_logger:
            callback.on_epoch_end(1, {"some_other_metric": 1.0})
            mock_logger.warning.assert_called_once()
            assert not _mock_trainer.stop_training  # Updated usage

    def test_verbose_logging(self, _mock_trainer):  # Updated argument name
        """Test that logs are printed only when verbose is True."""
        callback = EarlyStoppingCallback(patience=1, verbose=True)
        callback.set_trainer(_mock_trainer)  # Updated usage

        with patch("src.train.callbacks.early_stopping.logger") as mock_logger:
            # First call, sets baseline, no log
            callback.on_epoch_end(1, {"validation_loss": 0.5})
            mock_logger.info.assert_not_called()

            # Improvement
            callback.on_epoch_end(2, {"validation_loss": 0.4})
            mock_logger.info.assert_called_once()
            assert "improved" in mock_logger.info.call_args.args[0]
            mock_logger.reset_mock()

            # No improvement, patience is 1, so stop
            callback.on_epoch_end(3, {"validation_loss": 0.45})
            assert mock_logger.info.call_count == 2
            assert "did not improve" in mock_logger.info.call_args_list[0].args[0]
            assert (
                "Stopping training early" in mock_logger.info.call_args_list[1].args[0]
            )


class TestModelRunCallback:
    """Tests for the ModelRunCallback."""

    @patch("src.train.callbacks.model_run.ModelRunCallback._run_async_update")
    def test_initialization(self, mock_run_async):  # pylint: disable=unused-argument
        """Test that the callback initializes correctly."""

        model_run_uuid = "test-uuid-123"
        profile = "development"

        callback = ModelRunCallback(
            model_run_uuid=model_run_uuid,
            profile=profile,
            update_frequency=2,
            verbose=False,
        )

        assert callback.config.model_run_uuid == model_run_uuid
        assert callback.config.profile == profile
        assert callback.config.update_frequency == 2
        assert callback.config.verbose is False
        assert callback.start_time is None

    @patch("src.train.callbacks.model_run.ModelRunCallback._run_async_update")
    def test_on_train_begin(self, mock_run_async):
        """Test that on_train_begin sets times and triggers database update."""

        callback = ModelRunCallback("test-uuid", verbose=True)

        with patch("src.train.callbacks.model_run.datetime") as mock_datetime:
            mock_now = MagicMock()
            mock_datetime.now.return_value = mock_now

            callback.on_train_begin()

            assert callback.start_time == mock_now
            # pylint: disable=protected-access
            mock_run_async.assert_called_once_with(callback._update_start_time)

    @patch("src.train.callbacks.model_run.ModelRunCallback._run_async_update")
    def test_on_epoch_end_with_frequency(self, mock_run_async):
        """Test that on_epoch_end respects update frequency and sends correct metrics."""
        callback = ModelRunCallback("test-uuid", update_frequency=3, verbose=False)
        callback.trainer = MagicMock()
        logs = {"train_loss": 0.5, "validation_loss": 0.6}

        with patch("src.train.callbacks.model_run.datetime") as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = (
                "2023-01-01T12:00:00"
            )

            # Epoch 0 (1st epoch) - should not update (0+1 % 3 != 0)
            callback.on_epoch_end(0, logs)
            mock_run_async.assert_not_called()

            # Epoch 1 (2nd epoch) - should not update (1+1 % 3 != 0)
            callback.on_epoch_end(1, logs)
            mock_run_async.assert_not_called()

            # Epoch 2 (3rd epoch) - should update (2+1 % 3 == 0)
            with patch.object(
                callback.trainer.metrics, "add_epoch_metric"
            ) as mock_add_epoch_metric:
                callback.on_epoch_end(2, logs)
                expected_metrics = {
                    "epoch": 3,
                    "timestamp": "2023-01-01T12:00:00",
                    "train_loss": 0.5,
                    "validation_loss": 0.6,
                }
                mock_add_epoch_metric.assert_called_once_with(expected_metrics)
                # pylint: disable=protected-access
                mock_run_async.assert_called_once_with(
                    callback._update_epoch_metrics,
                    expected_metrics,
                )
                # pylint: enable=protected-access

    @patch("src.train.callbacks.model_run.ModelRunCallback._run_async_update")
    def test_on_train_end(self, _):
        """Test that on_train_end triggers completion update with correct summary."""
        callback = ModelRunCallback("test-uuid", verbose=True)
        callback.trainer = MagicMock()
        summary = {"train_loss": 0.1, "validation_loss": 0.2}
        callback.trainer.metrics.get_metric_summary.return_value = summary
        with patch("src.train.callbacks.model_run.datetime") as mock_datetime:
            mock_end_time = MagicMock()
            mock_end_time.isoformat.return_value = "2023-01-01T12:00:00"
            mock_datetime.now.return_value = mock_end_time
