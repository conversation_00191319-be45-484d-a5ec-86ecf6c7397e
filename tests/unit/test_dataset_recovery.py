"""
Unit tests for dataset recovery functionality in DatasetService.
"""

# pylint: disable=line-too-long

import tempfile
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import patch
from uuid import uuid4

import pytest

from database.services.dataset_service import DatasetError, DatasetService
from tests.fixtures.data import get_sample_dataset_data, get_sample_dataset_sets


@dataclass
class DatasetRecoveryTestParams:
    """Parameters for dataset recovery tests."""

    temp_dir: Path
    sample_dataset_uuid: str
    sample_dataset_data_with_images: Dict[str, Any]
    sample_dataset_sets_with_images: List[Dict[str, Any]]


class TestDatasetRecovery:
    """Test cases for dataset recovery functionality."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def sample_dataset_uuid(self):
        """Sample dataset UUID for testing."""
        return str(uuid4())

    @pytest.fixture
    def sample_dataset_data_with_images(self, sample_dataset_uuid):
        """Sample dataset data with images for testing."""
        return get_sample_dataset_data(
            custom_values={
                "uuid": sample_dataset_uuid,
                "name": "test-recovery-dataset",
                "images_count": 3,
                "created_at": "2023-01-01T00:00:00",
                "content_updated_at": "2023-01-01T00:00:00",
            }
        )

    @pytest.fixture
    def sample_dataset_sets_with_images(self, sample_dataset_uuid):
        """Sample dataset sets with image URLs for testing."""
        return get_sample_dataset_sets(
            count=3,
            custom_values_list=[
                {
                    "uuid": str(uuid4()),
                    "dataset_uuid": sample_dataset_uuid,
                    "coin_side_uuid": str(uuid4()),
                    "image_uuid": "img-1",
                    "created_at": "2023-01-01T00:00:00",
                    "set_type": 1,
                    "images_reviews": {"image_url": "https://example.com/image1.jpg"},
                },
                {
                    "uuid": str(uuid4()),
                    "dataset_uuid": sample_dataset_uuid,
                    "coin_side_uuid": str(uuid4()),
                    "image_uuid": "img-2",
                    "created_at": "2023-01-01T00:00:00",
                    "set_type": 1,
                    "images_reviews": {"image_url": "https://example.com/image2.jpg"},
                },
                {
                    "uuid": str(uuid4()),
                    "dataset_uuid": sample_dataset_uuid,
                    "coin_side_uuid": str(uuid4()),
                    "image_uuid": "img-3",
                    "created_at": "2023-01-01T00:00:00",
                    "set_type": 1,
                    "images_reviews": {"image_url": "https://example.com/image3.jpg"},
                },
            ],
        )

    @pytest.fixture
    def test_params(
        self,
        temp_dir,
        sample_dataset_uuid,
        sample_dataset_data_with_images,
        sample_dataset_sets_with_images,
    ):
        """Combined test parameters."""
        return DatasetRecoveryTestParams(
            temp_dir=temp_dir,
            sample_dataset_uuid=sample_dataset_uuid,
            sample_dataset_data_with_images=sample_dataset_data_with_images,
            sample_dataset_sets_with_images=sample_dataset_sets_with_images,
        )

    @patch("database.services.dataset_service.fetch_data")
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.recover_missing_dataset_images"  # noqa: E501
    )
    async def test_verify_and_recover_all_images_present(
        self, mock_download_image, mock_fetch_data, test_params
    ):
        """Test verification when all images are already present."""
        # Setup mock responses
        mock_fetch_data.side_effect = [
            [test_params.sample_dataset_data_with_images],  # get_dataset call
            [{"count": 3}],  # count call for get_dataset_sets
            test_params.sample_dataset_sets_with_images,  # data call for get_dataset_sets
        ]

        # Create all expected image files
        for dataset_set in test_params.sample_dataset_sets_with_images:
            coin_side_uuid = dataset_set["coin_side_uuid"]
            image_uuid = dataset_set["image_uuid"]
            image_dir = test_params.temp_dir / coin_side_uuid
            image_dir.mkdir(parents=True, exist_ok=True)
            image_file = image_dir / f"{image_uuid}.jpg"
            image_file.write_text("fake image content")

        # Call the method
        result = await DatasetService.verify_and_recover_dataset_images(
            dataset_uuid=test_params.sample_dataset_uuid,
            base_output_dir=test_params.temp_dir,
            auto_recover=True,
        )

        # Verify results
        assert result["expected_count"] == 3
        assert result["available_count"] == 3
        assert result["missing_count"] == 0
        assert result["recovered_count"] == 0
        assert result["recovery_failed_count"] == 0
        assert result["is_complete"] is True
        assert len(result["missing_images"]) == 0
        assert result["recovery_attempts"] == 0

        # Verify recovery was not called since all images were present
        mock_download_image.assert_not_called()

    @patch("database.services.dataset_service.fetch_data")
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.recover_missing_dataset_images"  # noqa: E501
    )
    async def test_verify_and_recover_missing_images_successful_recovery(
        self, mock_download_image, mock_fetch_data, test_params
    ):
        """Test successful recovery of missing images."""
        # Setup mock responses
        mock_fetch_data.side_effect = [
            [test_params.sample_dataset_data_with_images],  # get_dataset call
            [{"count": 3}],  # count call for get_dataset_sets
            test_params.sample_dataset_sets_with_images,  # data call for get_dataset_sets
        ]

        # Create only 1 out of 3 expected image files (2 missing)
        first_dataset_set = test_params.sample_dataset_sets_with_images[0]
        coin_side_uuid = first_dataset_set["coin_side_uuid"]
        image_uuid = first_dataset_set["image_uuid"]
        image_dir = test_params.temp_dir / coin_side_uuid
        image_dir.mkdir(parents=True, exist_ok=True)
        image_file = image_dir / f"{image_uuid}.jpg"
        image_file.write_text("fake image content")

        # Mock successful recovery for missing images
        mock_download_image.return_value = {
            "recovered_count": 2,
            "still_missing_count": 0,
            "still_missing_images": {},
        }

        # Call the method
        result = await DatasetService.verify_and_recover_dataset_images(
            dataset_uuid=test_params.sample_dataset_uuid,
            base_output_dir=test_params.temp_dir,
            auto_recover=True,
        )

        # Verify results
        assert result["expected_count"] == 3
        assert result["available_count"] == 3
        assert result["missing_count"] == 0
        assert result["recovered_count"] == 2
        assert result["recovery_failed_count"] == 0
        assert result["is_complete"] is True
        assert len(result["missing_images"]) == 0
        assert result["recovery_attempts"] == 2

        # Verify recovery was called once
        mock_download_image.assert_called_once()

    @patch("database.services.dataset_service.fetch_data")
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.recover_missing_dataset_images"  # noqa: E501
    )
    async def test_verify_and_recover_auto_recover_disabled(
        self, mock_download_image, mock_fetch_data, test_params
    ):
        """Test verification with auto_recover disabled."""
        # Setup mock responses
        mock_fetch_data.side_effect = [
            [test_params.sample_dataset_data_with_images],  # get_dataset call
            [{"count": 3}],  # count call for get_dataset_sets
            test_params.sample_dataset_sets_with_images,  # data call for get_dataset_sets
        ]

        # Create no image files (all missing)

        # Call the method with auto_recover=False
        result = await DatasetService.verify_and_recover_dataset_images(
            dataset_uuid=test_params.sample_dataset_uuid,
            base_output_dir=test_params.temp_dir,
            auto_recover=False,
        )

        # Verify results
        assert result["expected_count"] == 3
        assert result["available_count"] == 0
        assert result["missing_count"] == 3
        assert result["recovered_count"] == 0
        assert result["recovery_failed_count"] == 3
        assert result["is_complete"] is False
        assert len(result["missing_images"]) == 3
        assert result["recovery_attempts"] == 0

        # Verify download was not called since auto_recover is disabled
        mock_download_image.assert_not_called()

    @patch("database.services.dataset_service.fetch_data")
    async def test_verify_and_recover_dataset_not_found(
        self, mock_fetch_data, temp_dir
    ):
        """Test verification when dataset is not found."""
        # Setup mock to return empty result (dataset not found)
        mock_fetch_data.return_value = []

        # Call the method and expect DatasetError
        with pytest.raises(DatasetError, match="Dataset not found with UUID"):
            await DatasetService.verify_and_recover_dataset_images(
                dataset_uuid="non-existent-uuid",
                base_output_dir=temp_dir,
                auto_recover=True,
            )

    @patch("database.services.dataset_service.fetch_data")
    async def test_verify_and_recover_empty_dataset(
        self, mock_fetch_data, temp_dir, sample_dataset_uuid
    ):
        """Test verification of dataset with no images."""
        # Setup mock for dataset with 0 images
        empty_dataset_data = get_sample_dataset_data(
            custom_values={
                "uuid": sample_dataset_uuid,
                "name": "empty-dataset",
                "images_count": 0,
                "created_at": "2023-01-01T00:00:00",
                "content_updated_at": "2023-01-01T00:00:00",
            }
        )
        mock_fetch_data.return_value = [empty_dataset_data]

        # Call the method
        result = await DatasetService.verify_and_recover_dataset_images(
            dataset_uuid=sample_dataset_uuid,
            base_output_dir=temp_dir,
            auto_recover=True,
        )

        # Verify results for empty dataset
        assert result["expected_count"] == 0
        assert result["available_count"] == 0
        assert result["missing_count"] == 0
        assert result["recovered_count"] == 0
        assert result["recovery_failed_count"] == 0
        assert result["is_complete"] is True
        assert len(result["missing_images"]) == 0
        assert result["recovery_attempts"] == 0

    @patch("database.services.dataset_service.fetch_data")
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.recover_missing_dataset_images"  # noqa: E501
    )
    async def test_verify_and_recover_partial_recovery_with_retries(
        self, mock_download_image, mock_fetch_data, test_params
    ):
        """Test recovery with some failures and retries."""
        # Setup mock responses
        mock_fetch_data.side_effect = [
            [test_params.sample_dataset_data_with_images],  # get_dataset call
            [{"count": 3}],  # count call for get_dataset_sets
            test_params.sample_dataset_sets_with_images,  # data call for get_dataset_sets
        ]

        # Create no image files (all missing)
        # Mock partial recovery (2 recovered, 1 still missing)
        mock_download_image.return_value = {
            "recovered_count": 2,
            "still_missing_count": 1,
            "still_missing_images": {
                "image3": {
                    "image_url": "http://example.com/image3.jpg",
                    "coin_side_uuid": "coin3",
                    "path": "/path/to/image3.jpg",
                },
            },
        }

        # Call the method
        result = await DatasetService.verify_and_recover_dataset_images(
            dataset_uuid=test_params.sample_dataset_uuid,
            base_output_dir=test_params.temp_dir,
            auto_recover=True,
        )

        # Verify results
        assert result["expected_count"] == 3
        assert result["available_count"] == 2  # 2 recovered
        assert result["missing_count"] == 1  # 1 still missing
        assert result["recovered_count"] == 2
        assert result["recovery_failed_count"] == 1
        assert result["is_complete"] is False
        assert len(result["missing_images"]) == 1
        assert result["recovery_attempts"] == 2  # MAX_RECOVERY_ATTEMPTS

        # Verify recovery was called once
        mock_download_image.assert_called_once()

    @patch("database.services.dataset_service.fetch_data")
    @patch(
        "database.services.dataset_service.DatasetService._dataset_downloader.recover_missing_dataset_images"  # noqa: E501
    )
    async def test_verify_and_recover_download_failure(
        self, mock_download_image, mock_fetch_data, test_params
    ):
        """Test recovery when all downloads fail."""
        # Setup mock responses
        mock_fetch_data.side_effect = [
            [test_params.sample_dataset_data_with_images],  # get_dataset call
            [{"count": 3}],  # count call for get_dataset_sets
            test_params.sample_dataset_sets_with_images,  # data call for get_dataset_sets
        ]

        # Create no image files (all missing)
        # Mock all downloads to fail
        mock_download_image.return_value = {
            "recovered_count": 0,
            "still_missing_count": 3,
            "still_missing_images": {
                "image1": {
                    "image_url": "http://example.com/image1.jpg",
                    "coin_side_uuid": "coin1",
                    "path": "/path/to/image1.jpg",
                },
                "image2": {
                    "image_url": "http://example.com/image2.jpg",
                    "coin_side_uuid": "coin2",
                    "path": "/path/to/image2.jpg",
                },
                "image3": {
                    "image_url": "http://example.com/image3.jpg",
                    "coin_side_uuid": "coin3",
                    "path": "/path/to/image3.jpg",
                },
            },
        }

        # Call the method
        result = await DatasetService.verify_and_recover_dataset_images(
            dataset_uuid=test_params.sample_dataset_uuid,
            base_output_dir=test_params.temp_dir,
            auto_recover=True,
        )

        # Verify results
        assert result["expected_count"] == 3
        assert result["available_count"] == 0
        assert result["missing_count"] == 3
        assert result["recovered_count"] == 0
        assert result["recovery_failed_count"] == 3
        assert result["is_complete"] is False
        assert len(result["missing_images"]) == 3
        assert result["recovery_attempts"] == 2  # MAX_RECOVERY_ATTEMPTS

        # Verify recovery was called once (the DatasetDownloader handles retries internally)
        mock_download_image.assert_called_once()
