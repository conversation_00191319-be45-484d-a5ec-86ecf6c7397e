"""
Tests for the AugmentationPipelineFactory.

This module contains comprehensive tests for the augmentation pipeline factory,
including validation of transform creation, serialization, and parameter extraction.
"""

import tempfile
from pathlib import Path

import pytest
from torchvision import transforms

from augmentations import (
    AugmentationPipelineFactory,
    HorizontalFlip,
    NormalizationMean,
    NormalizationStd,
    ResizeDimensions,
    RotationRange,
    ZoomRange,
)
from augmentations.factory import ValuePreservingToTensor


class TestAugmentationPipelineFactory:
    """Test cases for AugmentationPipelineFactory."""

    def test_create_training_pipeline_empty_augmentations(self):
        """Test creating training pipeline with no augmentations."""
        pipeline = AugmentationPipelineFactory.create_training_pipeline([])

        # Should only contain ValuePreservingToTensor transform
        assert len(pipeline.transforms) == 1
        assert isinstance(pipeline.transforms[0], ValuePreservingToTensor)

    def test_create_training_pipeline_none_augmentations(self):
        """Test creating training pipeline with None augmentations."""
        pipeline = AugmentationPipelineFactory.create_training_pipeline(None)

        # Should only contain ValuePreservingToTensor transform
        assert len(pipeline.transforms) == 1
        assert isinstance(pipeline.transforms[0], ValuePreservingToTensor)

    def test_create_training_pipeline_with_horizontal_flip(self):
        """Test creating training pipeline with horizontal flip."""
        augmentations = [HorizontalFlip(horizontal_flip=True)]
        pipeline = AugmentationPipelineFactory.create_training_pipeline(augmentations)

        # Should contain horizontal flip transform and ValuePreservingToTensor
        assert len(pipeline.transforms) == 2
        # First transform should be the horizontal flip (callable object)
        assert hasattr(pipeline.transforms[0], "__call__")
        assert isinstance(pipeline.transforms[1], ValuePreservingToTensor)

    def test_create_training_pipeline_with_rotation(self):
        """Test creating training pipeline with rotation."""
        augmentations = [RotationRange(rotation_range=15.0)]
        pipeline = AugmentationPipelineFactory.create_training_pipeline(augmentations)

        # Should contain rotation transform and ValuePreservingToTensor
        assert len(pipeline.transforms) == 2
        # First transform should be the rotation (callable object)
        assert hasattr(pipeline.transforms[0], "__call__")
        assert isinstance(pipeline.transforms[1], ValuePreservingToTensor)

    def test_create_training_pipeline_with_zoom(self):
        """Test creating training pipeline with zoom."""
        augmentations = [ZoomRange(zoom_range=0.2)]
        pipeline = AugmentationPipelineFactory.create_training_pipeline(augmentations)

        # Should contain zoom transform and ValuePreservingToTensor
        assert len(pipeline.transforms) == 2
        assert hasattr(pipeline.transforms[0], "__call__")  # zoom transform
        assert isinstance(pipeline.transforms[1], ValuePreservingToTensor)

    def test_create_training_pipeline_with_resize(self):
        """Test creating training pipeline with resize."""
        augmentations = [ResizeDimensions(resize_dimensions=[224, 224])]
        pipeline = AugmentationPipelineFactory.create_training_pipeline(augmentations)

        # Should contain resize transform and ValuePreservingToTensor
        assert len(pipeline.transforms) == 2
        # First transform should be the resize (callable object)
        assert hasattr(pipeline.transforms[0], "__call__")
        assert isinstance(pipeline.transforms[1], ValuePreservingToTensor)

    def test_create_training_pipeline_with_normalization(self):
        """Test creating training pipeline with normalization."""
        augmentations = [
            NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
            NormalizationStd(normalization_std=[0.229, 0.224, 0.225]),
        ]
        pipeline = AugmentationPipelineFactory.create_training_pipeline(augmentations)

        # Should contain ValuePreservingToTensor and Normalize
        assert len(pipeline.transforms) == 2
        assert isinstance(pipeline.transforms[0], ValuePreservingToTensor)
        assert isinstance(pipeline.transforms[1], transforms.Normalize)

    def test_create_training_pipeline_complex(self):
        """Test creating training pipeline with multiple augmentations."""
        augmentations = [
            ResizeDimensions(resize_dimensions=[224, 224]),
            HorizontalFlip(horizontal_flip=True),
            RotationRange(rotation_range=15.0),
            ZoomRange(zoom_range=0.1),
            NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
            NormalizationStd(normalization_std=[0.229, 0.224, 0.225]),
        ]
        pipeline = AugmentationPipelineFactory.create_training_pipeline(augmentations)

        # Should contain:
        # resize, horizontal flip, rotation, zoom, ValuePreservingToTensor, Normalize
        assert len(pipeline.transforms) == 6
        # First four should be callable tensor transforms
        assert hasattr(pipeline.transforms[0], "__call__")  # resize
        assert hasattr(pipeline.transforms[1], "__call__")  # horizontal flip
        assert hasattr(pipeline.transforms[2], "__call__")  # rotation
        assert hasattr(pipeline.transforms[3], "__call__")  # zoom
        assert isinstance(pipeline.transforms[4], ValuePreservingToTensor)
        assert isinstance(pipeline.transforms[5], transforms.Normalize)

    def test_create_inference_pipeline_empty_augmentations(self):
        """Test creating inference pipeline with no augmentations."""
        pipeline = AugmentationPipelineFactory.create_inference_pipeline([])

        # Should only contain ValuePreservingToTensor transform
        assert len(pipeline.transforms) == 1
        assert isinstance(pipeline.transforms[0], ValuePreservingToTensor)

    def test_create_inference_pipeline_with_random_augmentations(self):
        """Test that inference pipeline excludes random augmentations."""
        augmentations = [
            HorizontalFlip(horizontal_flip=True),
            RotationRange(rotation_range=15.0),
            ZoomRange(zoom_range=0.1),
            ResizeDimensions(resize_dimensions=[224, 224]),
            NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
            NormalizationStd(normalization_std=[0.229, 0.224, 0.225]),
        ]
        pipeline = AugmentationPipelineFactory.create_inference_pipeline(augmentations)

        # Should only contain deterministic transforms: Resize, ValuePreservingToTensor, Normalize
        assert len(pipeline.transforms) == 3
        assert isinstance(pipeline.transforms[0], transforms.Resize)
        assert isinstance(pipeline.transforms[1], ValuePreservingToTensor)
        assert isinstance(pipeline.transforms[2], transforms.Normalize)

    def test_extract_normalization_params(self):
        """Test extraction of normalization parameters."""
        augmentations = [
            NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
            NormalizationStd(normalization_std=[0.229, 0.224, 0.225]),
        ]

        mean, std = AugmentationPipelineFactory.extract_normalization_params(
            augmentations
        )

        assert mean == [0.485, 0.456, 0.406]
        assert std == [0.229, 0.224, 0.225]

    def test_extract_normalization_params_missing(self):
        """Test extraction when normalization parameters are missing."""
        augmentations = [HorizontalFlip(horizontal_flip=True)]

        mean, std = AugmentationPipelineFactory.extract_normalization_params(
            augmentations
        )

        assert mean is None
        assert std is None

    def test_extract_resize_dimensions(self):
        """Test extraction of resize dimensions."""
        augmentations = [ResizeDimensions(resize_dimensions=[224, 224])]

        dims = AugmentationPipelineFactory.extract_resize_dimensions(augmentations)

        assert dims == [224, 224]

    def test_extract_resize_dimensions_missing(self):
        """Test extraction when resize dimensions are missing."""
        augmentations = [HorizontalFlip(horizontal_flip=True)]

        dims = AugmentationPipelineFactory.extract_resize_dimensions(augmentations)

        assert dims is None

    def test_serialize_and_load_pipeline(self):
        """Test serialization and loading of transform pipeline."""
        augmentations = [
            ResizeDimensions(resize_dimensions=[224, 224]),
            NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
            NormalizationStd(normalization_std=[0.229, 0.224, 0.225]),
        ]
        original_pipeline = AugmentationPipelineFactory.create_training_pipeline(
            augmentations
        )

        with tempfile.TemporaryDirectory() as temp_dir:
            pipeline_path = Path(temp_dir) / "transforms.pkl"

            # Serialize pipeline
            AugmentationPipelineFactory.serialize_pipeline(
                original_pipeline, pipeline_path
            )

            # Load pipeline
            loaded_pipeline = AugmentationPipelineFactory.load_pipeline(pipeline_path)

            # Verify pipeline structure is preserved
            assert len(loaded_pipeline.transforms) == len(original_pipeline.transforms)
            assert isinstance(
                loaded_pipeline.transforms[0], type(original_pipeline.transforms[0])
            )

    def test_load_pipeline_file_not_found(self):
        """Test loading pipeline when file doesn't exist."""
        with pytest.raises(FileNotFoundError):
            AugmentationPipelineFactory.load_pipeline("nonexistent_file.pkl")

    def test_validate_augmentations_empty(self):
        """Test validation of empty augmentations list."""
        result = AugmentationPipelineFactory.validate_augmentations([])

        assert result["valid"] is True
        assert len(result["errors"]) == 0
        assert result["summary"]["total_augmentations"] == 0
        assert result["summary"]["geometric_transforms"] == 0
        assert result["summary"]["normalization"] is False

    def test_validate_augmentations_complete(self):
        """Test validation of complete augmentations."""
        augmentations = [
            HorizontalFlip(horizontal_flip=True),
            RotationRange(rotation_range=15.0),
            ResizeDimensions(resize_dimensions=[224, 224]),
            NormalizationMean(normalization_mean=[0.485, 0.456, 0.406]),
            NormalizationStd(normalization_std=[0.229, 0.224, 0.225]),
        ]

        result = AugmentationPipelineFactory.validate_augmentations(augmentations)

        assert result["valid"] is True
        assert len(result["errors"]) == 0
        assert result["summary"]["total_augmentations"] == 5
        assert result["summary"]["geometric_transforms"] == 2
        assert result["summary"]["normalization"] is True
        assert result["summary"]["resize"] == [224, 224]

    def test_validate_augmentations_incomplete_normalization(self):
        """Test validation with incomplete normalization."""
        augmentations = [
            NormalizationMean(normalization_mean=[0.485, 0.456, 0.406])
            # Missing NormalizationStd
        ]

        result = AugmentationPipelineFactory.validate_augmentations(augmentations)

        assert result["valid"] is True
        assert len(result["warnings"]) == 1
        assert "Incomplete normalization" in result["warnings"][0]
        assert result["summary"]["normalization"] is False
