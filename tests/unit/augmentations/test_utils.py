"""
Tests for AugmentationUtils.

This module contains tests for augmentation utility functions.
"""

import pytest

from augmentations import (
    AugmentationUtils,
    HorizontalFlip,
    NormalizationMean,
    NormalizationStd,
    ResizeDimensions,
    RotationRange,
    ZoomRange,
)


class TestAugmentationUtils:
    """Test cases for AugmentationUtils."""

    def test_from_dict_list_single_augmentation(self):
        """Test converting single augmentation from dict."""
        aug_dicts = [{"horizontal_flip": True}]

        augmentations = AugmentationUtils.from_dict_list(aug_dicts)

        assert len(augmentations) == 1
        assert isinstance(augmentations[0], HorizontalFlip)
        assert augmentations[0].horizontal_flip is True

    def test_from_dict_list_multiple_augmentations(self):
        """Test converting multiple augmentations from dicts."""
        aug_dicts = [
            {"horizontal_flip": True},
            {"rotation_range": 15.0},
            {"resize_dimensions": [224, 224]},
        ]

        augmentations = AugmentationUtils.from_dict_list(aug_dicts)

        assert len(augmentations) == 3
        assert isinstance(augmentations[0], HorizontalFlip)
        assert isinstance(augmentations[1], RotationRange)
        assert isinstance(augmentations[2], ResizeDimensions)

    def test_from_dict_list_unknown_type(self):
        """Test error handling for unknown augmentation type."""
        aug_dicts = [{"unknown_augmentation": True}]

        with pytest.raises(ValueError, match="Unknown augmentation type"):
            AugmentationUtils.from_dict_list(aug_dicts)

    def test_count_augmentation_types(self):
        """Test counting augmentations by type."""
        augmentations = [
            HorizontalFlip(horizontal_flip=True),
            RotationRange(rotation_range=15.0),
            HorizontalFlip(horizontal_flip=False),  # Duplicate type
            NormalizationMean(normalization_mean=[0.5, 0.5, 0.5]),
        ]

        type_counts = AugmentationUtils.count_augmentation_types(augmentations)

        assert type_counts["HorizontalFlip"] == 2
        assert type_counts["RotationRange"] == 1
        assert type_counts["NormalizationMean"] == 1
        assert len(type_counts) == 3

    def test_count_augmentation_types_empty(self):
        """Test counting with empty list."""
        type_counts = AugmentationUtils.count_augmentation_types([])

        assert not type_counts

    def test_augmentation_type_map(self):
        """Test that AUGMENTATION_TYPE_MAP contains all expected mappings."""
        expected_keys = {
            "horizontal_flip",
            "rotation_range",
            "zoom_range",
            "resize_dimensions",
            "normalization_mean",
            "normalization_std",
        }

        assert set(AugmentationUtils.AUGMENTATION_TYPE_MAP.keys()) == expected_keys
        assert (
            AugmentationUtils.AUGMENTATION_TYPE_MAP["horizontal_flip"] == HorizontalFlip
        )
        assert (
            AugmentationUtils.AUGMENTATION_TYPE_MAP["rotation_range"] == RotationRange
        )
        assert AugmentationUtils.AUGMENTATION_TYPE_MAP["zoom_range"] == ZoomRange
        assert (
            AugmentationUtils.AUGMENTATION_TYPE_MAP["resize_dimensions"]
            == ResizeDimensions
        )
        assert (
            AugmentationUtils.AUGMENTATION_TYPE_MAP["normalization_mean"]
            == NormalizationMean
        )
        assert (
            AugmentationUtils.AUGMENTATION_TYPE_MAP["normalization_std"]
            == NormalizationStd
        )
