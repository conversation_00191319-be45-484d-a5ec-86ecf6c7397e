"""
Training-specific callback classes.

This module provides callback interfaces specifically for the training process.
"""

from __future__ import annotations

from typing import Any, Dict

from .base import Callback


class TrainingCallback(Callback["ModelTrainer"]):
    """
    Base class for training callbacks.

    This class extends the generic Callback with training-specific lifecycle hooks.
    """

    def on_train_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of training."""

    def on_train_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of training."""

    def on_epoch_begin(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of an epoch."""

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of an epoch."""

    def on_batch_begin(self, batch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of a training batch."""

    def on_batch_end(self, batch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of a training batch."""

    def on_validation_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of a validation run."""

    def on_validation_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of a validation run."""
