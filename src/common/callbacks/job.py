"""
Job-specific callback classes.

This module provides callback interfaces specifically for the job dispatcher system.
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict

from .base import Callback

if TYPE_CHECKING:
    from src.jobs.base import Job  # Only import Job for type checking


class JobCallback(Callback["JobDispatcher"]):
    """
    Base class for job callbacks.

    This class extends the generic Callback with job-specific lifecycle hooks.
    """

    def on_dispatcher_start(self, logs: Dict[str, Any] | None = None) -> None:
        """Called when the dispatcher starts."""

    def on_dispatcher_stop(self, logs: Dict[str, Any] | None = None) -> None:
        """Called when the dispatcher stops."""

    def on_job_scheduled(self, job: "Job", logs: Dict[str, Any] | None = None) -> None:
        """Called when a job is scheduled."""

    def on_job_start(self, job: "Job", logs: Dict[str, Any] | None = None) -> None:
        """Called when a job starts execution."""

    def on_job_complete(self, job: "Job", logs: Dict[str, Any] | None = None) -> None:
        """Called when a job completes successfully."""

    def on_job_failed(
        self, job: "Job", error: Exception, logs: Dict[str, Any] | None = None
    ) -> None:
        """Called when a job fails."""

    def on_job_cancelled(self, job: "Job", logs: Dict[str, Any] | None = None) -> None:
        """Called when a job is cancelled."""
