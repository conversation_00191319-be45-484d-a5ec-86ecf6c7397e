"""
Base callback classes for a generic callback system.

This module provides the foundation for creating callbacks that can be used
across different components like training and job dispatching.
"""

from __future__ import annotations

import logging
from abc import ABC
from typing import Generic, List, Optional, TypeVar

logger = logging.getLogger(__name__)

# Generic type for the owner object (Trainer, Dispatcher, etc.)
T = TypeVar("T")


class Callback(Generic[T], ABC):
    """
    Abstract base class for creating callbacks.

    Callbacks can be used to customize the behavior of components like
    ModelTrainer or JobDispatcher at various stages of their lifecycle.
    Subclasses can override the methods for the events they are interested in.
    """

    def __init__(self) -> None:
        """Initialize the callback."""
        self.owner: Optional[T] = None

    def set_owner(self, owner: T) -> None:
        """Set the owner instance for this callback."""
        self.owner = owner


class CallbackHandler(Generic[T]):
    """
    Handles a list of callbacks and orchestrates their execution.

    This class is responsible for managing callbacks and invoking their methods
    at appropriate times during the lifecycle of the owner component.
    """

    def __init__(
        self, callbacks: Optional[List[Callback[T]]] = None, owner: Optional[T] = None
    ):
        """
        Initialize the callback handler.

        Args:
            callbacks: List of callbacks to manage
            owner: The owner instance that will be passed to the callbacks
        """
        self.callbacks = callbacks or []
        self.owner: Optional[T] = None
        if owner:
            self.set_owner(owner)

    def set_owner(self, owner: T) -> None:
        """
        Set the owner instance on all callbacks.

        Args:
            owner: The owner instance to set
        """
        for callback in self.callbacks:
            callback.set_owner(owner)

    def add_callback(self, callback: Callback[T]) -> None:
        """
        Add a callback to the handler.

        Args:
            callback: The callback to add
        """
        if hasattr(self, "owner") and self.owner is not None:
            callback.set_owner(self.owner)
        self.callbacks.append(callback)

    def remove_callback(self, callback: Callback[T]) -> None:
        """
        Remove a callback from the handler.

        Args:
            callback: The callback to remove
        """
        if callback in self.callbacks:
            self.callbacks.remove(callback)

    def call_method(self, method_name: str, *args, **kwargs) -> None:
        """
        Call a method on all callbacks.

        Args:
            method_name: Name of the method to call
            *args: Positional arguments to pass to the method
            **kwargs: Keyword arguments to pass to the method
        """
        for callback in self.callbacks:
            if hasattr(callback, method_name):
                try:
                    method = getattr(callback, method_name)
                    method(*args, **kwargs)
                except Exception as e:
                    # Log the error but continue with other callbacks
                    logger.error(
                        "Error in callback %s.%s: %s",
                        callback.__class__.__name__,
                        method_name,
                        str(e),
                    )
