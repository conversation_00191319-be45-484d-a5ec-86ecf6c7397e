"""
Generic Downloader utility for handling all download operations.

This module provides a centralized Downloader class that handles:
- HTTP file downloads
- Local file copying
- Directory structure creation
- File recovery operations
- Batch downloading with error handling

The class is generic and not tied to any specific domain (datasets, images, etc.).
"""

import asyncio
import logging
import os
import shutil
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from urllib.parse import urlparse

import aiofiles
import aiohttp

logger = logging.getLogger(__name__)


@dataclass
class DownloadConfig:
    """Configuration for download operations."""

    url: str
    output_dir: Path
    filename: Optional[str] = None
    session: Optional[aiohttp.ClientSession] = None
    timeout: Optional[int] = None


@dataclass
class FileDownloadConfig:
    """Configuration for single file download operations."""

    url: str
    output_dir: Path
    filename: str
    session: Optional[aiohttp.ClientSession] = None
    timeout: int = 60


@dataclass
class ImageDownloadConfig:
    """Configuration for image download operations."""

    image_url: str
    output_dir: Path
    filename: str
    session: Optional[aiohttp.ClientSession] = None
    timeout: int = 60


class DownloadError(Exception):
    """Exception raised when download operations fail."""


class Downloader:
    """
    Centralized downloader for handling all image download operations.

    This class consolidates download functionality that was previously scattered
    across DatasetService and file_utils.py, providing a single responsibility
    class for all download-related operations.
    """

    # Maximum recovery attempts for missing images
    MAX_RECOVERY_ATTEMPTS = 2

    def __init__(self, timeout: int = 300):
        """
        Initialize the Downloader.

        Args:
            timeout: Default timeout for download operations in seconds
        """
        self.timeout = timeout

    async def download_file(
        self,
        config: DownloadConfig,
    ) -> Tuple[bool, str]:
        """
        Download a file from a URL to the specified directory, or copy from local path.

        This function handles both HTTP URLs and local file paths:
        - HTTP URLs (http://, https://): Downloads using aiohttp
        - Local file paths (absolute paths): Copies the file using shutil

        Args:
            config: DownloadConfig containing all download parameters

        Returns:
            Tuple of (success: bool, message: str)
        """
        timeout = config.timeout if config.timeout is not None else self.timeout

        filename = config.filename
        if not filename:
            # Extract filename from URL if not provided
            parsed_url = urlparse(config.url)
            filename = os.path.basename(parsed_url.path)
            if not filename:
                return False, f"Could not determine filename from URL: {config.url}"

        # Ensure output directory exists
        config.output_dir.mkdir(parents=True, exist_ok=True)
        output_path = config.output_dir / filename

        # Skip if file already exists
        if output_path.exists():
            return True, f"File already exists: {output_path}"

        # Check if this is a local file path or HTTP URL
        parsed_url = urlparse(config.url)
        is_local_path = not parsed_url.scheme or parsed_url.scheme == "file"

        if is_local_path:
            # Handle local file path - copy the file
            return await self._copy_local_file(config.url, output_path)

        # Handle HTTP URL - download the file
        return await self._download_http_file(
            config.url, output_path, config.session, timeout
        )

    async def _copy_local_file(
        self, source_path: str, output_path: Path
    ) -> Tuple[bool, str]:
        """
        Copy a local file to the output path.

        Args:
            source_path: Path to the source file
            output_path: Path where the file should be copied

        Returns:
            Tuple of (success: bool, message: str)
        """
        try:
            # Handle file:// URLs
            if source_path.startswith("file://"):
                source_path = source_path[7:]  # Remove file:// prefix

            source_file = Path(source_path)
            if not source_file.exists():
                error_msg = f"Source file does not exist: {source_path}"
                logger.warning(error_msg)
                return False, error_msg

            # Use asyncio to run the blocking copy operation
            await asyncio.to_thread(shutil.copy2, source_file, output_path)

            success_msg = f"Copied {source_path} to {output_path}"
            logger.debug(success_msg)
            return True, success_msg

        except Exception as e:
            error_msg = f"Error copying file {source_path}: {str(e)}"
            logger.warning(error_msg)
            return False, error_msg

    async def _download_http_file(
        self,
        url: str,
        output_path: Path,
        session: Optional[aiohttp.ClientSession],
        timeout: int,
    ) -> Tuple[bool, str]:
        """
        Download a file from an HTTP URL.

        Args:
            url: HTTP URL to download from
            output_path: Path where the file should be saved
            session: Optional aiohttp ClientSession for connection pooling
            timeout: Request timeout in seconds

        Returns:
            Tuple of (success: bool, message: str)
        """
        # Create session if not provided
        close_session = session is None
        if session is None:
            session = aiohttp.ClientSession()

        try:
            async with session.get(url, timeout=timeout) as response:
                if response.status != 200:
                    error_msg = f"Failed to download {url}: HTTP {response.status}"
                    logger.warning(error_msg)
                    return False, error_msg

                # Stream the download to handle large files
                async with aiofiles.open(output_path, "wb") as f:
                    async for chunk in response.content.iter_chunked(
                        1024 * 16
                    ):  # 16KB chunks
                        await f.write(chunk)

                success_msg = f"Downloaded {url} to {output_path}"
                logger.debug(success_msg)
                return True, success_msg

        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            # Clean up partially downloaded file if it exists
            if output_path.exists():
                output_path.unlink()
            error_msg = f"Error downloading {url}: {str(e)}"
            logger.warning(error_msg)
            return False, error_msg
        finally:
            if close_session:
                await session.close()

    async def download_single_file(
        self,
        config: FileDownloadConfig,
    ) -> Optional[Path]:
        """
        Download a single file with simplified interface.

        Args:
            config: FileDownloadConfig containing all download parameters

        Returns:
            Path of the downloaded file or None if failed
        """
        try:
            download_config = DownloadConfig(
                url=config.url,
                output_dir=config.output_dir,
                filename=config.filename,
                session=config.session,
                timeout=config.timeout,
            )
            success, message = await self.download_file(download_config)
            if success:
                return config.output_dir / config.filename
            logger.warning("Failed to download file %s: %s", config.url, message)
            return None
        except Exception as e:
            logger.warning("Failed to download file %s: %s", config.url, str(e))
            return None

    async def download_multiple_files(
        self,
        download_items: List[Dict[str, Union[str, Path]]],
    ) -> Tuple[int, List[str]]:
        """
        Download multiple files in batch.

        Args:
            download_items: List of dictionaries with keys:
                - 'url': URL to download from
                - 'output_dir': Directory to save the file
                - 'filename': Name of the output file
                - 'timeout': Optional timeout (defaults to instance timeout)

        Returns:
            Tuple of (downloaded_count, error_messages)
        """
        downloaded_count = 0
        error_messages = []

        # Create a single session for all downloads
        async with aiohttp.ClientSession() as session:
            for item in download_items:
                if not item.get("url") or not item.get("filename"):
                    continue

                try:
                    output_dir = Path(item["output_dir"])
                    output_dir.mkdir(parents=True, exist_ok=True)

                    # Download file
                    file_config = FileDownloadConfig(
                        url=item["url"],
                        output_dir=output_dir,
                        filename=item["filename"],
                        session=session,
                        timeout=item.get("timeout", 60),
                    )
                    result = await self.download_single_file(file_config)

                    if result and result.exists():
                        downloaded_count += 1
                    else:
                        error_messages.append(
                            f"Failed to download file {item['filename']} from {item['url']}"
                        )

                except Exception as e:
                    error_messages.append(
                        f"Error processing file {item.get('filename', 'unknown')}: {str(e)}"
                    )

        return downloaded_count, error_messages

    async def download_image(
        self,
        config: ImageDownloadConfig,
    ) -> Optional[Path]:
        """
        Download a single image with simplified interface.

        Args:
            config: ImageDownloadConfig containing all download parameters

        Returns:
            Path to the downloaded file if successful, None otherwise
        """
        try:
            download_config = DownloadConfig(
                url=config.image_url,
                output_dir=config.output_dir,
                filename=config.filename,
                session=config.session,
                timeout=config.timeout,
            )
            success, message = await self.download_file(download_config)

            if success:
                return config.output_dir / config.filename

            logger.warning("Failed to download image: %s", message)
            return None
        except Exception as e:
            logger.warning("Exception during image download: %s", e)
            return None

    async def recover_missing_files(
        self,
        missing_files: Dict[str, Dict],
    ) -> Dict[str, any]:
        """
        Attempt to recover missing files with automatic retry.

        Args:
            missing_files: Dictionary of missing files with file info.
                          Each entry should have keys:
                          - 'url': URL to download from
                          - 'output_dir': Directory to save the file
                          - 'filename': Name of the output file

        Returns:
            Dictionary containing recovery statistics and results
        """
        recovered_count = 0
        still_missing = missing_files.copy()

        logger.info(
            "Starting recovery for %d missing files with up to %d attempts",
            len(missing_files),
            self.MAX_RECOVERY_ATTEMPTS,
        )

        # Attempt recovery with retries
        for attempt in range(1, self.MAX_RECOVERY_ATTEMPTS + 1):
            if not still_missing:
                break

            logger.info(
                "Recovery attempt %d/%d for %d files",
                attempt,
                self.MAX_RECOVERY_ATTEMPTS,
                len(still_missing),
            )

            # Try to download each missing file
            attempt_still_missing = {}
            async with aiohttp.ClientSession() as session:
                for file_id, file_info in still_missing.items():
                    if not file_info.get("url"):
                        # Can't recover without URL
                        attempt_still_missing[file_id] = file_info
                        continue

                    # Create directory if it doesn't exist
                    output_dir = Path(file_info["output_dir"])
                    output_dir.mkdir(parents=True, exist_ok=True)

                    # Attempt to download
                    file_config = FileDownloadConfig(
                        url=file_info["url"],
                        output_dir=output_dir,
                        filename=file_info["filename"],
                        session=session,
                    )
                    result = await self.download_single_file(file_config)

                    if result and result.exists():
                        recovered_count += 1
                        logger.debug("Successfully recovered file %s", file_id)
                    else:
                        attempt_still_missing[file_id] = file_info

            # Update still missing for next attempt
            still_missing = attempt_still_missing

            # Log progress
            if attempt < self.MAX_RECOVERY_ATTEMPTS and still_missing:
                logger.info(
                    "Attempt %d completed. Recovered: %d, Still missing: %d",
                    attempt,
                    recovered_count,
                    len(still_missing),
                )

        # Final logging
        if recovered_count > 0:
            logger.info("Successfully recovered %d files", recovered_count)
        if still_missing:
            logger.warning(
                "Failed to recover %d files after %d attempts",
                len(still_missing),
                self.MAX_RECOVERY_ATTEMPTS,
            )

        return {
            "recovered_count": recovered_count,
            "still_missing_count": len(still_missing),
            "still_missing_files": still_missing,
        }
