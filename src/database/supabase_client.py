"""
Supabase client configuration and management module.
Provides functionality for creating and managing Supabase client instances,
with support for connection pooling and profile-based credentials management.
Includes utilities for executing database queries and managing client lifecycle.
"""

from functools import lru_cache
from typing import Dict, List, Optional

from supabase import Client, create_client

from database.config import db_settings


@lru_cache(maxsize=128)
def get_supabase_client(profile: str = None) -> Client:
    """
    Get or create a Supabase client using the specified profile's credentials.
    Uses LRU cache to maintain connection pool.

    Args:
        profile: Optional profile name to use.
        If None, uses the current profile from settings.
    """
    # Use the provided profile or get the current one from db_settings
    if profile:
        # Temporarily update db_settings for this request
        current_settings = db_settings.model_copy(update={"SUPABASE_PROFILE": profile})
    else:
        current_settings = db_settings

    # Get credentials from db_settings
    credentials = current_settings.active_supabase_credentials

    return create_client(credentials.url, credentials.anon_key)


def get_fresh_supabase_client(profile: str = None) -> Client:
    """
    Create a new Supabase client using the specified profile's credentials.
    Use this when you need to ensure you're using the latest credentials.

    Args:
        profile: Optional profile name to use.
        If None, uses the current profile from settings.
    """
    get_supabase_client.cache_clear()
    return get_supabase_client(profile)


def _build_select_statement(select, foreign_tables):
    """Helper function to build the select statement with foreign tables."""
    if not foreign_tables:
        return select

    foreign_tables_str = ", ".join(foreign_tables)

    if isinstance(select, str) and select == "*":
        return f"{select}, {foreign_tables_str}"
    if isinstance(select, str):
        return f"{select}, {foreign_tables_str}"
    if isinstance(select, list):
        return f"{', '.join(select)}, {foreign_tables_str}"
    return f"*, {foreign_tables_str}"


def _process_foreign_tables(foreign_tables_config):
    """Helper function to process foreign tables configuration.

    Supports nested foreign tables with the following formats:
    1. Simple string: "table_name"
    2. Dict with name and select: {"name": "table_name", "select": "*"}
    3. Dict with name and nested tables: {
        "name": "table_name",
        "select": "*",
        "nested": [{"name": "nested_table", "select": "*"}]
    }
    """
    foreign_tables = []

    if not foreign_tables_config:
        return foreign_tables

    for foreign_table in foreign_tables_config:
        if isinstance(foreign_table, str):
            foreign_tables.append(f"{foreign_table}(*)")
        elif isinstance(foreign_table, dict):
            table_name = foreign_table["name"]
            table_select = foreign_table.get("select", "*")

            # Check for nested tables
            nested_tables = foreign_table.get("nested", [])
            if nested_tables:
                # Process nested tables recursively
                nested_query = _process_foreign_tables(nested_tables)
                if nested_query:
                    nested_str = ", ".join(nested_query)
                    foreign_tables.append(f"{table_name}({table_select}, {nested_str})")
                else:
                    foreign_tables.append(f"{table_name}({table_select})")
            else:
                foreign_tables.append(f"{table_name}({table_select})")

    return foreign_tables


def _apply_filters(query, filters):
    """Helper function to apply filters to the query."""
    if not filters:
        return query

    # Define mapping of operator strings to query methods
    operator_methods = {
        "eq": "eq",
        "neq": "neq",
        "gt": "gt",
        "gte": "gte",
        "lt": "lt",
        "lte": "lte",
        "like": "like",
        "ilike": "ilike",
        "in": "in_",  # Note the underscore in method name
        "is": "is_",  # Note the underscore in method name
    }

    for filter_item in filters:
        column = filter_item["column"]
        value = filter_item["value"]
        operator = filter_item.get("operator", "eq")

        # Handle special case for is_not
        if operator == "is_not":
            query = query.not_.is_(column, value)
        elif operator in operator_methods:
            # Dynamically call the appropriate method on the query object
            method_name = operator_methods[operator]
            query = getattr(query, method_name)(column, value)
        else:
            raise ValueError(f"Unsupported operator: {operator}")

    return query


def _apply_ordering(query, order_by):
    """Helper function to apply ordering to the query."""
    if not order_by:
        return query

    for order in order_by:
        query = query.order(order["column"], desc=not order.get("ascending", True))

    return query


def _apply_pagination(query, limit, offset):
    """Helper function to apply pagination to the query."""
    if not limit:
        return query

    query = query.limit(limit)
    if offset:
        query = query.range(
            offset,
            offset + limit - 1,
        )

    return query


def fetch_data(
    table: str, query_params: Optional[Dict] = None, profile: str = None
) -> List[Dict]:
    """
    Fetch data from a Supabase table with optional query parameters.

    Args:
        table: The name of the table to query
        query_params: Dictionary of query parameters. Supported keys:
            - select: String or list of columns to select (default: "*")
            - filters: List of filter dictionaries with keys:
                - column: Column name
                - value: Value to match
                - operator: Optional operator (default: "eq")
            - limit: Maximum number of records to return
            - offset: Number of records to skip
            - order_by: List of dicts with "column" and "ascending" (bool) keys
            - count: If "exact", returns the total count of matching records
            - foreign_tables: List of foreign tables to include in the query
                Each item can be either a string (table name) or a dict with keys:
                - name: Name of the foreign table
                - select: Columns to select from the foreign table (default: "*")
        profile: Optional profile name to use.
        If None, uses the current profile from settings.

    Returns:
        List of records as dictionaries, or dict with "count" if count was requested
    """
    supabase = get_supabase_client(profile)
    query_params = query_params or {}

    # Set up the base query
    # Use the select parameter
    select = query_params.get("select", "*")

    # Process foreign tables
    foreign_tables = _process_foreign_tables(query_params.get("foreign_tables"))

    # Build the select statement
    select_str = _build_select_statement(select, foreign_tables)

    # Create the base query
    query = supabase.table(table).select(select_str, count=query_params.get("count"))

    # Apply filters, ordering, and pagination
    query = _apply_filters(query, query_params.get("filters"))
    query = _apply_ordering(query, query_params.get("order_by"))
    query = _apply_pagination(
        query, query_params.get("limit"), query_params.get("offset")
    )

    # Execute the query
    result = query.execute()

    # Handle count query
    if query_params.get("count") == "exact":
        return [{"count": result.count}] if hasattr(result, "count") else []

    return result.data


def fetch_model(model_uuid: str, profile: str = None) -> Optional[Dict]:
    """
    Fetch a model by its UUID.

    Args:
        model_uuid: UUID of the model to fetch
        profile: Optional profile name to use.
        If None, uses the current profile from settings.

    Returns:
        Dictionary containing model information or None if not found
    """
    models = fetch_data(
        "models",
        {
            "filters": [{"column": "uuid", "value": model_uuid}],
            "limit": 1,
        },
        profile=profile,
    )
    return models[0] if models else None


def fetch_model_version(model_version_uuid: str, profile: str = None) -> Optional[Dict]:
    """
    Fetch a model version by its UUID.

    Args:
        model_version_uuid: UUID of the model version to fetch
        profile: Optional profile name to use.
        If None, uses the current profile from settings.

    Returns:
        Dictionary containing model version information or None if not found
    """
    versions = fetch_data(
        "model_versions",
        {
            "filters": [{"column": "uuid", "value": model_version_uuid}],
            "limit": 1,
        },
        profile=profile,
    )
    return versions[0] if versions else None


def fetch_model_run(model_run_uuid: str, profile: str = None) -> Optional[Dict]:
    """
    Fetch a model run by its UUID.

    Args:
        model_run_uuid: UUID of the model run to fetch
        profile: Optional profile name to use.
        If None, uses the current profile from settings.

    Returns:
        Dictionary containing model run information or None if not found
    """
    runs = fetch_data(
        "model_runs",
        {
            "filters": [{"column": "uuid", "value": model_run_uuid}],
            "limit": 1,
        },
        profile=profile,
    )
    return runs[0] if runs else None


def update_data(
    table: str, data: Dict, filters: List[Dict], profile: str = None
) -> Dict:
    """
    Update data in a Supabase table.

    Args:
        table: The name of the table to update
        data: Dictionary containing the data to update
        filters: List of filter dictionaries with keys:
            - column: Column name
            - value: Value to match
            - operator: Optional operator (default: "eq")
        profile: Optional profile name to use.
        If None, uses the current profile from settings.

    Returns:
        Dictionary containing the updated record(s)

    Raises:
        ValueError: If no filters are provided or update fails
    """
    if not filters:
        raise ValueError("Filters are required for update operations")

    supabase = get_supabase_client(profile)

    # Create the base update query
    query = supabase.table(table).update(data)

    # Apply filters to specify which records to update
    query = _apply_filters(query, filters)

    # Execute the update
    result = query.execute()

    return result.data


def fetch_model_and_version(
    model_version_uuid: str, profile: str = None
) -> Optional[Dict]:
    """
    Fetch model and its version information based on model_version_uuid.

    Args:
        model_version_uuid: UUID of the model version to fetch
        profile: Optional profile name to use.
        If None, uses the current profile from settings.

    Returns:
        Dictionary containing model and version information or None if not found
    """
    model_version = fetch_model_version(model_version_uuid, profile=profile)
    if not model_version:
        return None

    model = fetch_model(model_version["model_uuid"], profile=profile)
    if not model:
        return None

    return {
        "model": model,
        "model_version": model_version,
    }
