"""
Model Factory for validating database data and creating model instances.

This module provides utilities for validating incoming database data
and creating model instances in a generic way.
"""

import logging
from typing import Any, Dict, List, Optional, Type, TypeVar, get_type_hints

from pydantic import BaseModel, ValidationError, create_model

# Configure logger
logger = logging.getLogger(__name__)

# Generic type for Pydantic models
T = TypeVar("T", bound=BaseModel)


class ModelValidationError(Exception):
    """Exception raised when model validation fails."""

    def __init__(self, message: str, errors: Optional[List[Dict]] = None):
        self.errors = errors or []
        super().__init__(message)


class ModelFactory:
    """Factory class for creating and validating model instances."""

    @classmethod
    def create_model(
        cls, model_class: Type[T], data: Dict[str, Any], partial: bool = False
    ) -> T:
        """
        Create a model instance from dictionary data with validation.

        Args:
            model_class: The Pydantic model class to instantiate
            data: Dictionary containing the data
            partial: If True, allows partial data (missing non-required fields)

        Returns:
            An instance of the specified model class

        Raises:
            ModelValidationError: If validation fails
        """
        try:
            # If partial is True, create a dynamic model with all fields optional
            if partial:
                return cls._create_partial_model(model_class, data)

            # Otherwise, use the model class directly
            return model_class(**data)
        except ValidationError as e:
            error_details = e.errors()
            error_message = f"Validation failed for {model_class.__name__}: {str(e)}"
            logger.error(error_message)
            raise ModelValidationError(error_message, error_details) from e
        except Exception as e:
            error_message = f"Error creating {model_class.__name__}: {str(e)}"
            logger.error(error_message)
            raise ModelValidationError(error_message) from e

    @classmethod
    def _create_partial_model(cls, model_class: Type[T], data: Dict[str, Any]) -> T:
        """
        Create a model instance allowing partial data.

        Args:
            model_class: The Pydantic model class to base the dynamic model on
            data: Dictionary containing the data

        Returns:
            An instance of the specified model class
        """
        # Create a dynamic model with all fields optional
        fields = {}
        type_hints = get_type_hints(model_class)

        for field_name, field_type in type_hints.items():
            # Skip private fields and Config
            if field_name.startswith("_") or field_name == "Config":
                continue

            # Make the field optional by wrapping it in Optional
            fields[field_name] = (Optional[field_type], None)

        # Create dynamic model
        dynamic_model = create_model(
            f"Partial{model_class.__name__}", __base__=model_class, **fields
        )

        # Create instance with the data
        instance = dynamic_model(**data)

        # Convert back to the original model class
        return model_class(**instance.model_dump(exclude_unset=True))

    @classmethod
    def validate_data(
        cls,
        data: Dict[str, Any],
        required_fields: List[str] = None,
        field_validators: Dict[str, callable] = None,
    ) -> Dict[str, Any]:
        """
        Validate dictionary data against required fields and custom validators.

        Args:
            data: Dictionary containing the data to validate
            required_fields: List of field names that must be present
            field_validators: Dictionary mapping field names to validator functions

        Returns:
            The validated data dictionary

        Raises:
            ModelValidationError: If validation fails
        """
        errors = []

        # Check if data is empty
        if not data:
            raise ModelValidationError("Data is empty")

        # Check required fields
        if required_fields:
            for field in required_fields:
                if field not in data:
                    errors.append(
                        {
                            "loc": [field],
                            "msg": f"Field '{field}' is required",
                            "type": "value_error.missing",
                        }
                    )

        # Apply field-specific validators
        if field_validators:
            for field, validator in field_validators.items():
                if field in data:
                    try:
                        # Apply the validator function
                        validator(data[field])
                    except Exception as e:
                        errors.append(
                            {
                                "loc": [field],
                                "msg": str(e),
                                "type": "value_error.custom",
                            }
                        )

        # Raise exception if any errors were found
        if errors:
            raise ModelValidationError(
                f"Validation failed with {len(errors)} errors", errors
            )

        return data

    @classmethod
    def create_models_from_list(
        cls,
        model_class: Type[T],
        data_list: List[Dict[str, Any]],
        partial: bool = False,
        skip_invalid: bool = False,
    ) -> List[T]:
        """
        Create multiple model instances from a list of dictionaries.

        Args:
            model_class: The Pydantic model class to instantiate
            data_list: List of dictionaries containing the data
            partial: If True, allows partial data (missing non-required fields)
            skip_invalid: If True, skip invalid items instead of raising an exception

        Returns:
            List of model instances

        Raises:
            ModelValidationError: If validation fails and skip_invalid is False
        """
        models = []
        errors = []

        for i, data in enumerate(data_list):
            try:
                model = cls.create_model(model_class, data, partial)
                models.append(model)
            except ModelValidationError as e:
                if skip_invalid:
                    logger.warning("Skipping invalid item at index %d: %s", i, str(e))
                    errors.append({"index": i, "message": str(e), "errors": e.errors})
                else:
                    raise ModelValidationError(
                        f"Validation failed at index {i}: {str(e)}", e.errors
                    ) from e

        # Log summary if items were skipped
        if skip_invalid and errors:
            logger.info(
                "Created %d valid models, skipped %d invalid items",
                len(models),
                len(errors),
            )

        return models
