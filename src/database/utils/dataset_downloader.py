"""
Dataset-specific downloader that uses the generic Downloader class.

This module provides dataset-specific download functionality while keeping
the core Downloader class generic and reusable.
"""

import logging
from pathlib import Path
from typing import Dict, List, Tuple, Union

from common.downloader import Downloader
from config.paths import get_background_dir_name
from database.models.dataset_set import DatasetSet
from utils.image_utils import build_image_path

logger = logging.getLogger(__name__)


class DatasetDownloader:
    """
    Dataset-specific downloader that wraps the generic Downloader class.

    This class handles dataset-specific logic like directory structure,
    filename generation, and image extension detection while delegating
    the actual download operations to the generic Downloader.
    """

    def __init__(self, timeout: int = 300):
        """
        Initialize the DatasetDownloader.

        Args:
            timeout: Default timeout for download operations in seconds
        """
        self.downloader = Downloader(timeout=timeout)

    async def download_dataset_images(
        self,
        dataset_sets: List[DatasetSet],
        base_output_dir: Union[str, Path],
    ) -> Tuple[int, List[str]]:
        """
        Download all images for a list of dataset sets to the local filesystem.

        Args:
            dataset_sets: List of DatasetSet objects containing image information
            base_output_dir: Base directory to save the downloaded images

        Returns:
            Tuple of (downloaded_count, error_messages)
        """
        base_output_dir = Path(base_output_dir)

        # Convert dataset sets to generic download items
        download_items = []
        for dataset_set in dataset_sets:
            # Handle both dict and object formats for backward compatibility
            if hasattr(dataset_set, "image_url"):
                # DatasetSet object
                image_url = dataset_set.image_url
                image_uuid = dataset_set.image_uuid
                coin_side_uuid = dataset_set.coin_side_uuid
            else:
                # Dictionary format
                images_reviews = dataset_set.get("images_reviews", {})
                if isinstance(images_reviews, list) and images_reviews:
                    images_reviews = images_reviews[0]
                image_url = images_reviews.get("image_url") if images_reviews else None
                image_uuid = dataset_set.get("image_uuid")
                coin_side_uuid = dataset_set.get("coin_side_uuid")

            if not image_url or not image_uuid:
                continue

            # Use centralized path building
            try:
                image_path = build_image_path(
                    base_dir=base_output_dir,
                    image_uuid=str(image_uuid),
                    coin_side_uuid=str(coin_side_uuid) if coin_side_uuid else None,
                    image_url=image_url,
                )
            except ValueError as e:
                logger.warning("Skipping image with invalid URL: %s", e)
                continue

            download_items.append(
                {
                    "url": image_url,
                    "output_dir": image_path.parent,
                    "filename": image_path.name,
                    "timeout": 60,  # Use shorter timeout for individual images
                }
            )

        # Use the generic downloader
        return await self.downloader.download_multiple_files(download_items)

    async def recover_missing_dataset_images(
        self,
        missing_images: Dict[str, Dict],
        base_output_dir: Union[str, Path],
    ) -> Dict[str, any]:
        """
        Attempt to recover missing dataset images with automatic retry.

        Args:
            missing_images: Dictionary of missing images from verify_dataset_images
            base_output_dir: Base directory containing the downloaded images

        Returns:
            Dictionary containing recovery statistics and results
        """
        base_output_dir = Path(base_output_dir)

        # Convert missing images to generic format
        missing_files = {}
        for image_uuid, image_info in missing_images.items():
            if not image_info.get("image_url"):
                # Skip images without URL
                continue

            # Create directory path
            output_dir = base_output_dir / image_info["coin_side_uuid"]

            missing_files[image_uuid] = {
                "url": image_info["image_url"],
                "output_dir": str(output_dir),
                "filename": image_info["filename"],
            }

        # Use the generic recovery method
        result = await self.downloader.recover_missing_files(missing_files)

        # Convert result back to dataset-specific format
        return {
            "recovered_count": result["recovered_count"],
            "still_missing_count": result["still_missing_count"],
            "still_missing_images": result[
                "still_missing_files"
            ],  # Rename for compatibility
        }

    def get_expected_image_info(
        self,
        dataset_set,
        base_output_dir: Union[str, Path],
    ) -> Dict[str, Union[str, Path]]:
        """
        Get expected image information for a dataset set.

        Args:
            dataset_set: DatasetSet object
            base_output_dir: Base directory for images

        Returns:
            Dictionary with image information including path, filename, etc.
        """
        base_output_dir = Path(base_output_dir)

        # Validate that we have a valid image URL
        if not dataset_set.image_url:
            raise ValueError(
                f"Cannot get image info for {dataset_set.image_uuid}: image_url is required"
            )

        # Use centralized path building
        image_path = build_image_path(
            base_dir=base_output_dir,
            image_uuid=str(dataset_set.image_uuid),
            coin_side_uuid=(
                str(dataset_set.coin_side_uuid) if dataset_set.coin_side_uuid else None
            ),
            image_url=dataset_set.image_url,
        )

        # Determine coin_side_id for compatibility
        if dataset_set.coin_side_uuid:
            coin_side_id = str(dataset_set.coin_side_uuid)
        else:
            coin_side_id = get_background_dir_name()

        return {
            "coin_side_uuid": coin_side_id,
            "image_url": dataset_set.image_url,
            "filename": image_path.name,
            "path": image_path,
        }
