"""
File utility functions for handling dataset files.
"""

import os
from pathlib import Path


def ensure_directory(path: Path) -> Path:
    """
    Ensure a directory exists and return its path.

    Args:
        path: Directory path

    Returns:
        Path to the directory
    """
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_unique_filename(directory: Path, filename: str) -> Path:
    """
    Get a unique filename in the specified directory.

    Args:
        directory: Directory path
        filename: Desired filename

    Returns:
        Path to a unique filename
    """
    directory.mkdir(parents=True, exist_ok=True)

    # Split filename and extension
    base, ext = os.path.splitext(filename)
    counter = 1
    new_path = directory / filename

    # Find a unique filename by appending a counter if needed
    while new_path.exists():
        new_path = directory / f"{base}_{counter}{ext}"
        counter += 1

    return new_path
