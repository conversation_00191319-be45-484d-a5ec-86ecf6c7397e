"""
Base Service for database services.

This module provides a base class that all database services should inherit from.
It implements common functionality for data validation and model creation.
"""

from typing import Any, Dict, Generic, List, Optional, Type, TypeVar

from pydantic import BaseModel

from database.utils.model_factory import ModelFactory

# Generic type for Pydantic models
T = TypeVar("T", bound=BaseModel)


class BaseService(Generic[T]):
    """Base service class with common functionality for all database services."""

    # The model class that this service works with
    model_class: Type[T] = None

    # Required fields for data validation
    required_fields: List[str] = []

    @classmethod
    def validate_data(
        cls,
        data: Dict[str, Any],
        required_fields: Optional[List[str]] = None,
        field_validators: Optional[Dict[str, callable]] = None,
    ) -> Dict[str, Any]:
        """
        Validate data against required fields and custom validators.

        Args:
            data: Dictionary containing the data to validate
            required_fields: Optional list of required fields (overrides class default)
            field_validators: Optional dictionary of field validators

        Returns:
            The validated data dictionary

        Raises:
            ModelValidationError: If validation fails
        """
        # Use class required_fields if none provided
        if required_fields is None:
            required_fields = cls.required_fields

        return ModelFactory.validate_data(
            data=data,
            required_fields=required_fields,
            field_validators=field_validators,
        )

    @classmethod
    def create_model(
        cls,
        data: Dict[str, Any],
        model_class: Optional[Type[BaseModel]] = None,
        partial: bool = False,
    ) -> BaseModel:
        """
        Create a model instance from dictionary data with validation.

        Args:
            data: Dictionary containing the data
            model_class: Optional model class (overrides class default)
            partial: If True, allows partial data (missing non-required fields)

        Returns:
            An instance of the specified model class

        Raises:
            ModelValidationError: If validation fails
            ValueError: If no model_class is specified or available
        """
        # Use class model_class if none provided
        if model_class is None:
            model_class = cls.model_class

        if model_class is None:
            raise ValueError(
                "No model class specified. Either provide model_class parameter "
                "or set model_class class attribute."
            )

        return ModelFactory.create_model(
            model_class=model_class, data=data, partial=partial
        )

    @classmethod
    def create_models_from_list(
        cls,
        data_list: List[Dict[str, Any]],
        model_class: Optional[Type[BaseModel]] = None,
        partial: bool = False,
        skip_invalid: bool = False,
    ) -> List[BaseModel]:
        """
        Create multiple model instances from a list of dictionaries.

        Args:
            data_list: List of dictionaries containing the data
            model_class: Optional model class (overrides class default)
            partial: If True, allows partial data (missing non-required fields)
            skip_invalid: If True, skip invalid items instead of raising an exception

        Returns:
            List of model instances

        Raises:
            ModelValidationError: If validation fails and skip_invalid is False
            ValueError: If no model_class is specified or available
        """
        # Use class model_class if none provided
        if model_class is None:
            model_class = cls.model_class

        if model_class is None:
            raise ValueError(
                "No model class specified. Either provide model_class parameter "
                "or set model_class class attribute."
            )

        return ModelFactory.create_models_from_list(
            model_class=model_class,
            data_list=data_list,
            partial=partial,
            skip_invalid=skip_invalid,
        )
