"""
Training Data Service for preparing and transforming data for model training.
"""

from datetime import datetime
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>, TypedDict, Union
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from database.models.dataset import Dataset
from database.models.version import ModelVersion
from database.services.base_service import BaseService
from database.services.dataset_service import DatasetError, DatasetService
from database.services.dataset_state_cache import DatasetStateCacheService
from database.services.model_metadata_service import (
    ModelMetadataError,
    ModelMetadataService,
)
from utils.async_utils import maybe_await


class TrainingDataError(Exception):
    """Base exception for training data related errors."""


class TrainingDataModel(BaseModel):
    """Structured training data model."""

    model: Dict
    model_version: Dict
    dataset: Dict
    model_run: Dict
    training_parameters: Dict
    dataset_metadata: Dict
    cache_state: Optional[Dict] = None

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "model": {"uuid": "123", "name": "Example Model"},
                "model_version": {"uuid": "456", "version": "1.0.0"},
                "dataset": {"uuid": "789", "name": "Example Dataset"},
                "model_run": {"uuid": "abc", "status": "pending"},
                "training_parameters": {"batch_size": 32, "epochs": 10},
                "dataset_metadata": {"images_count": 1000},
                "cache_state": {
                    "cache_key": "789_2025-02-27T18:28:18.77991+00:00",
                    "is_cache_hit": True,
                    "dataset_preparation_skipped": True,
                },
            }
        }
    )


# For backward compatibility
TrainingData = TypedDict(
    "TrainingData",
    {
        "model": Dict,
        "model_version": Dict,
        "dataset": Dict,
        "model_run": Dict,
        "training_parameters": Dict,
        "dataset_metadata": Dict,
        "cache_state": Optional[Dict],
    },
)


class TrainingDataService(BaseService[TrainingDataModel]):
    """Service for preparing and transforming data for model training."""

    # Set the model class for this service
    model_class = TrainingDataModel

    # Define required fields for validation
    required_fields = ["model", "model_version", "dataset", "model_run"]

    @classmethod
    async def get_training_data(
        cls,
        model_run_uuid: str | UUID,
        profile: Optional[str] = None,
    ) -> TrainingData:
        """
        Prepare all required data for model training using model run UUID.

        Args:
            model_run_uuid: UUID of the model run containing training configuration
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing all required training data

        Raises:
            TrainingDataError: If required data is missing or invalid
        """
        try:
            # Get model metadata using ModelMetadataService
            model_metadata_result = ModelMetadataService.get_model_metadata(
                model_run_uuid=model_run_uuid,
                profile=profile,
            )

            # Safely await the result
            model_metadata = await maybe_await(model_metadata_result)

            # Extract dataset UUID from model run
            dataset_uuid = model_metadata["model_run"].get("dataset_uuid")
            if not dataset_uuid:
                raise TrainingDataError(
                    f"Dataset UUID not found in model run {model_run_uuid}"
                )

            # Fetch and validate dataset
            try:
                dataset_result = DatasetService.get_dataset(dataset_uuid, profile)
                dataset_data = await maybe_await(dataset_result)
            except DatasetError as e:
                raise TrainingDataError(f"Failed to fetch dataset: {str(e)}") from e

            # Check cache state for dataset preparation optimization
            content_updated_at = dataset_data.get("content_updated_at")
            cache_state_info = cls.check_dataset_cache_state(
                dataset_uuid, content_updated_at
            )

            # Combine training parameters from model version and model run
            training_parameters = cls.combine_training_parameters(
                model_version_data=model_metadata.get("model_version", {}),
                model_run_data=model_metadata.get("model_run", {}),
            )

            # Prepare dataset metadata
            dataset_metadata = cls._prepare_dataset_metadata(dataset_data)

            # Construct the training data dictionary
            training_data = {
                "model": model_metadata["model"],
                "model_version": model_metadata["model_version"],
                "dataset": dataset_data,
                "model_run": model_metadata["model_run"],
                "training_parameters": training_parameters,
                "dataset_metadata": dataset_metadata,
                "cache_state": cache_state_info,
            }

            # Validate the training data
            try:
                model = cls.create_model(data=training_data)
                return model.model_dump()
            except Exception as e:
                raise TrainingDataError(f"Invalid training data: {str(e)}") from e
        except ModelMetadataError as e:
            raise TrainingDataError(str(e)) from e
        except ConnectionError:
            # ConnectionError is propagated directly for proper API error handling
            # This allows the API route handler to catch it and return a 503 response
            raise
        except Exception as e:
            raise TrainingDataError(f"An error occurred: {str(e)}") from e

    @classmethod
    def _prepare_dataset_metadata(cls, dataset_data: Dict) -> Dict:
        """
        Prepare dataset metadata for training.

        Args:
            dataset_data: Raw dataset data from the database

        Returns:
            Dictionary containing prepared dataset metadata

        Note:
            The dataset_data is already validated by DatasetService
        """
        return {
            "name": dataset_data["name"],
            "description": dataset_data.get("description", ""),
            "images_count": dataset_data["images_count"],
            "created_at": dataset_data.get("created_at"),
            "content_updated_at": dataset_data.get("content_updated_at"),
        }

    @classmethod
    def combine_training_parameters(
        cls, *, model_version_data: Dict = None, model_run_data: Dict = None
    ) -> Dict:
        """
        This method merges the parameters from model_version and model_run, with
        model_run parameters taking precedence in case of conflicts. It also preserves
        the original model_version parameters in a nested dictionary for reference.

        Args:
            model_version_data: Dictionary containing model version data with parameters
            model_run_data: Dictionary containing model run data with parameters

        Returns:
            Dictionary containing combined training parameters
        """
        # Handle None inputs gracefully
        model_version_data = model_version_data or {}
        model_run_data = model_run_data or {}

        # Get parameters from model version with fallback to empty dict
        model_version_parameters = model_version_data.get("parameters", {})
        if not isinstance(model_version_parameters, dict):
            model_version_parameters = {}

        # Create a copy of model_version_parameters as the base for combined parameters
        combined_parameters = model_version_parameters.copy()

        # Get parameters from model run if they exist
        model_run_parameters = model_run_data.get("parameters", {})
        if isinstance(model_run_parameters, dict):
            # Update combined parameters with model run parameters (overriding any conflicts)
            combined_parameters.update(model_run_parameters)

        # Store original model_version_parameters for reference
        combined_parameters["model_version_parameters"] = (
            model_version_parameters.copy()
        )

        return combined_parameters

    @classmethod
    def check_dataset_cache_state(
        cls,
        dataset_uuid: str | UUID,
        content_updated_at: Optional[Union[datetime, str]] = None,
    ) -> Dict:
        """
        Check the cache state for a dataset and return cache information.

        Args:
            dataset_uuid: UUID of the dataset
            content_updated_at: Timestamp when dataset content was last updated

        Returns:
            Dictionary containing cache state information
        """
        is_cache_valid = DatasetStateCacheService.is_cache_valid(
            dataset_uuid, content_updated_at
        )

        cache_key = DatasetStateCacheService.generate_cache_key(
            dataset_uuid, content_updated_at
        )

        cached_state = DatasetStateCacheService.get_cached_state(
            dataset_uuid, content_updated_at
        )

        # If cache is not valid, mark the dataset as needing preparation
        if not is_cache_valid:
            DatasetStateCacheService.set_cache_state(
                dataset_uuid=dataset_uuid,
                content_updated_at=content_updated_at,
                is_prepared=True,  # Mark as prepared after this call
            )

        return {
            "cache_key": cache_key,
            "is_cache_hit": is_cache_valid,
            "dataset_preparation_skipped": is_cache_valid,
            "cached_at": cached_state.cached_at.isoformat() if cached_state else None,
        }

    @classmethod
    async def update_model_run_status(
        cls,
        model_run_uuid: str | UUID,
        status: str,
        metrics: Optional[Dict] = None,
        profile: Optional[str] = None,
    ) -> bool:
        """
        Update the status of a model run.

        Args:
            model_run_uuid: UUID of the model run to update
            status: New status for the model run
            metrics: Optional metrics to include in the update
            profile: Optional Supabase profile name

        Returns:
            bool: True if the update was successful

        Note:
            This is a convenience method that delegates to ModelMetadataService.
        """
        return await ModelMetadataService.update_model_run_status(
            model_run_uuid=model_run_uuid,
            status=status,
            metrics=metrics,
            profile=profile,
        )

    @classmethod
    async def prepare_training_data(
        cls, training_data: TrainingData
    ) -> Tuple[ModelVersion, Dataset, str]:
        """
        Prepare the training data for model training.

        Transforms raw dictionary data into proper model objects and performs validation.
        This centralizes the object creation logic that was previously in the API routes.

        Args:
            training_data: The raw training data from get_training_data()

        Returns:
            Tuple containing (model_version, dataset, experiment_uuid)

        Raises:
            TrainingDataError: If validation fails or required data is missing
        """
        try:
            # Extract data from the training data
            model_version_data = training_data["model_version"]
            dataset_data = training_data["dataset"]
            model_run_data = training_data["model_run"]

            # Clean up parameters to ensure they're valid for Pydantic validation
            # Convert empty strings to None for numeric fields
            if "parameters" in model_version_data:
                params = model_version_data["parameters"]
                numeric_fields = [
                    "residual_blocks",
                    "dense_blocks",
                    "transition_layers",
                    "resolution_multiplier",
                    "width_multiplier",
                    "growth_rate",
                ]

                for field in numeric_fields:
                    if field in params and params[field] == "":
                        params[field] = None

            # Create model version and dataset objects
            model_version = ModelVersion(**model_version_data)
            dataset = Dataset(**dataset_data)

            # Ensure experiment_uuid is a valid string, defaulting to empty string if None
            experiment_uuid = model_run_data.get("experiment_uuid") or ""

            return model_version, dataset, experiment_uuid

        except KeyError as e:
            raise TrainingDataError(
                f"Missing required data in training data: {str(e)}"
            ) from e
        except Exception as e:
            raise TrainingDataError(f"Error preparing training data: {str(e)}") from e

    @classmethod
    def should_skip_dataset_sets_fetching(cls, training_data: TrainingData) -> bool:
        """
        Determine if dataset_sets fetching should be skipped based on cache state.

        Args:
            training_data: The training data containing cache state information

        Returns:
            True if dataset_sets fetching should be skipped, False otherwise
        """
        cache_state = training_data.get("cache_state")
        if not cache_state:
            return False

        return cache_state.get("dataset_preparation_skipped", False)

    @classmethod
    def mark_dataset_preparation_complete(
        cls,
        dataset_uuid: str | UUID,
        content_updated_at: Optional[Union[datetime, str]] = None,
    ) -> None:
        """
        Mark dataset preparation as complete in the cache.

        This should be called after successfully fetching and preparing dataset_sets
        to avoid redundant work in future calls.

        Args:
            dataset_uuid: UUID of the dataset
            content_updated_at: Timestamp when dataset content was last updated
        """
        DatasetStateCacheService.set_cache_state(
            dataset_uuid=dataset_uuid,
            content_updated_at=content_updated_at,
            is_prepared=True,
        )

    @classmethod
    def invalidate_dataset_cache(cls, dataset_uuid: str | UUID) -> int:
        """
        Invalidate cache for a specific dataset.

        This should be called when you know a dataset has been updated.

        Args:
            dataset_uuid: UUID of the dataset to invalidate

        Returns:
            Number of cache entries that were invalidated
        """
        return DatasetStateCacheService.invalidate_dataset_cache(dataset_uuid)
