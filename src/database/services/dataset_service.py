"""
Dataset Service for fetching and managing dataset-related data.
"""

import logging
import traceback
from pathlib import Path
from typing import Dict, Generator, List, Optional, Tuple, Union
from uuid import UUID

# Import directly from the model files to avoid circular imports
from database.models.dataset_set import DatasetSet, SetType
from database.services.base_service import BaseService
from database.supabase_client import fetch_data
from database.utils.dataset_downloader import DatasetDownloader
from database.utils.model_factory import ModelValidationError
from utils.path_utils import build_image_path

# Configure dedicated dataset logger
logger = logging.getLogger("dataset")


class BatchConfig:  # pylint: disable=too-few-public-methods
    """Configuration class for dataset batch fetching."""

    def __init__(  # pylint: disable=too-many-arguments
        self,
        dataset_uuid: Union[str, UUID],
        *,  # Force keyword arguments for the rest
        set_type: Optional[SetType] = None,
        offset: int = 0,
        limit: int = 1000,
        profile: Optional[str] = None,
    ):
        self.dataset_uuid = dataset_uuid
        self.set_type = set_type
        self.offset = offset
        self.limit = limit
        self.profile = profile


def _get_images_base_dir() -> str:
    """Get the images base directory from database settings, avoiding circular imports."""
    try:
        # pylint: disable=import-outside-toplevel
        from database.config import db_settings

        # Check if the environment variable is set
        if db_settings.IMAGES_BASE_DIR:
            return db_settings.IMAGES_BASE_DIR
        # Log a warning if the environment variable is not set
        logger.warning(
            "COINY_CLASSIFIER_IMAGES_BASE_DIR environment variable not set. "
            "No fallback path available - images may not be accessible."
        )
        return ""
    except ImportError:
        # Fallback to default if settings can't be imported
        logger.warning(
            "Could not import database settings. "
            "No fallback path available - images may not be accessible."
        )
        return ""


class DatasetError(Exception):
    """Base exception for dataset related errors."""


class DatasetService(BaseService[DatasetSet]):
    """Service for fetching and managing dataset data."""

    # Set the model class for this service
    model_class = DatasetSet

    # Define required fields for validation
    required_fields = ["uuid", "name", "images_count"]

    # Dataset downloader instance for handling all download operations
    _dataset_downloader = DatasetDownloader()

    # Images base directory - determined once at class level
    _images_base_dir = _get_images_base_dir()

    @classmethod
    def get_dataset(
        cls,
        dataset_uuid: str | UUID,
        profile: Optional[str] = None,
    ) -> Dict:
        """
        Fetch dataset data by UUID.

        Args:
            dataset_uuid: UUID of the dataset
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing dataset data

        Raises:
            DatasetError: If dataset is not found or data is invalid
        """
        dataset = cls._fetch_dataset(dataset_uuid, profile)
        cls._validate_dataset(dataset)
        return dataset

    @classmethod
    def _fetch_dataset(
        cls, dataset_uuid: str | UUID, profile: Optional[str] = None
    ) -> Dict:
        """
        Fetch raw dataset data from the database.

        Args:
            dataset_uuid: UUID of the dataset
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing raw dataset data

        Raises:
            DatasetError: If dataset is not found
        """
        datasets = fetch_data(
            "datasets",
            {"filters": [{"column": "uuid", "value": str(dataset_uuid)}]},
            profile=profile,
        )
        if not datasets:
            raise DatasetError(f"Dataset not found with UUID: {dataset_uuid}")
        return datasets[0]

    @classmethod
    def _validate_dataset(cls, dataset_data: Dict) -> None:
        """
        Validate dataset data.

        Args:
            dataset_data: Dataset data to validate

        Raises:
            DatasetError: If dataset data is invalid
        """
        try:
            # Define custom validators
            field_validators = {
                "images_count": lambda value: (isinstance(value, int) and value > 0)
                or ValueError("Dataset must contain at least one image")
            }

            # Use the generic validation method
            cls.validate_data(data=dataset_data, field_validators=field_validators)
        except ModelValidationError as e:
            # Convert ModelValidationError to DatasetError
            raise DatasetError(str(e)) from e

    @classmethod
    def get_dataset_sets(
        cls,
        dataset_uuid: Union[str, UUID],
        set_type: Optional[SetType] = None,
        batch_size: int = 1000,
        profile: Optional[str] = None,
    ) -> Generator[List[DatasetSet], None, None]:
        """
        Fetch dataset sets with image URLs in batches.

        Args:
            dataset_uuid: UUID of the dataset
            set_type: Optional set type to filter by
            batch_size: Number of records to fetch in each batch
                (must be a positive integer)
            profile: Optional Supabase profile name

        Yields:
            List of DatasetSet objects for the current batch

        Raises:
            ValueError: If batch_size is not a positive integer
            DatasetError: If there's an error fetching the data
        """
        # Validate batch_size
        if not isinstance(batch_size, int) or batch_size <= 0:
            raise ValueError("Batch size must be a positive integer")
        # First, get the total count to handle pagination
        count_query = {
            "filters": [{"column": "dataset_uuid", "value": str(dataset_uuid)}],
            "count": "exact",
        }
        if set_type is not None:
            count_query["filters"].append(
                {"column": "set_type", "value": int(set_type)}
            )

        count_result = fetch_data(
            "dataset_sets",
            count_query,
            profile=profile,
        )

        if not count_result or "count" not in count_result[0]:
            raise DatasetError(f"Failed to get count for dataset {dataset_uuid}")

        total_count = count_result[0]["count"]
        if total_count == 0:
            return

        # Process in batches
        offset = 0

        while offset < total_count:
            # Fetch the batch
            batch_config = BatchConfig(
                dataset_uuid=dataset_uuid,
                set_type=set_type,
                offset=offset,
                limit=batch_size,
                profile=profile,
            )
            batch = cls._fetch_dataset_sets_batch(config=batch_config)
            if batch:
                yield batch
                offset += len(batch)
            else:
                break

    @classmethod
    def _fetch_dataset_sets_batch(
        cls,
        config: BatchConfig,
    ) -> List[DatasetSet]:
        """
        Fetch a single batch of dataset sets with image URLs.

        Args:
            config: BatchConfig object containing query parameters
                - dataset_uuid: UUID of the dataset
                - set_type: Optional set type to filter by
                - offset: Offset for pagination
                - limit: Maximum number of records to return
                - profile: Optional Supabase profile name

        Returns:
            List of DatasetSetWithImage objects
        """
        # Build the base query
        query = {
            "select": "*, images_reviews(image_url, location, fact_uuid, coin_side_uuid)",
            "filters": [
                {"column": "dataset_uuid", "value": str(config.dataset_uuid)},
                {"column": "image_uuid", "operator": "is_not", "value": None},
            ],
            "limit": config.limit,
            "offset": config.offset,
            "order_by": [
                {"column": "created_at", "ascending": True}
            ],  # Fixed long line
        }

        # Add set type filter if specified
        if config.set_type is not None:
            query["filters"].append(
                {"column": "set_type", "value": int(config.set_type)}
            )

        # Execute the query with join to images_reviews
        results = fetch_data(
            "dataset_sets",
            query,
            profile=config.profile,
        )

        # Process and validate results
        dataset_sets = []
        for item in results:
            try:
                # Extract data from the joined images_reviews table
                image_url = None
                location_key = None
                fact_uuid = None
                images_reviews_coin_side_uuid = None

                if "images_reviews" in item and item["images_reviews"]:
                    # Handle case where images_reviews is a list of dicts
                    if (
                        isinstance(item["images_reviews"], list)
                        and len(item["images_reviews"]) > 0
                    ):
                        # Get the first review if available
                        first_review = item["images_reviews"][0]
                        if isinstance(first_review, dict):
                            image_url = first_review.get("image_url")
                            location_key = first_review.get("location")
                            fact_uuid = first_review.get("fact_uuid")
                            images_reviews_coin_side_uuid = first_review.get(
                                "coin_side_uuid"
                            )
                    # Handle case where it's a single dict (for backward compatibility)
                    elif isinstance(item["images_reviews"], dict):
                        image_url = item["images_reviews"].get("image_url")
                        location_key = item["images_reviews"].get("location")
                        fact_uuid = item["images_reviews"].get("fact_uuid")
                        images_reviews_coin_side_uuid = item["images_reviews"].get(
                            "coin_side_uuid"
                        )

                # Build the proper image path using the path builder
                if image_url and location_key:
                    # Use coin_side_uuid from dataset_sets table or images_reviews table
                    coin_side_uuid = (
                        item.get("coin_side_uuid") or images_reviews_coin_side_uuid
                    )

                    image_url = build_image_path(
                        image_url=image_url,
                        location_key=location_key,
                        fact_uuid=fact_uuid,
                        coin_side_uuid=coin_side_uuid,
                        base_dir=cls._images_base_dir,
                    )

                # Create the dataset set with image URL using the generic model creation
                try:
                    # Prepare data dictionary
                    data_dict = {
                        "uuid": item["uuid"],
                        "dataset_uuid": item["dataset_uuid"],
                        "created_at": item.get("created_at"),
                        "coin_side_uuid": item.get("coin_side_uuid"),
                        "set_type": item.get("set_type"),
                        "image_uuid": item.get("image_uuid"),
                        "image_url": image_url,
                    }

                    # Create model instance
                    dataset_set = cls.create_model(
                        data=data_dict, model_class=DatasetSet, partial=True
                    )
                    logger.debug("Created DatasetSet: %s", dataset_set)
                    dataset_sets.append(dataset_set)
                except ModelValidationError as e:
                    # Skip invalid records but log the error
                    logger.warning("Skipping invalid dataset set record: %s", str(e))
                    traceback.print_exc()
                    continue
            except (KeyError, ValueError) as e:
                # Skip invalid records but log the error
                logger.warning("Skipping invalid dataset set record: %s", str(e))
                traceback.print_exc()
                continue

        return dataset_sets

    @classmethod
    async def download_dataset_images(
        cls,
        dataset_uuid: Union[str, UUID],
        base_output_dir: Union[str, Path],
        set_type: Optional[SetType] = None,
        profile: Optional[str] = None,
    ) -> Tuple[int, List[str]]:
        """
        Download all images for a dataset to the local filesystem.

        Args:
            dataset_uuid: UUID of the dataset
            base_output_dir: Base directory to save the downloaded images
            set_type: Optional set type to filter by
            profile: Optional Supabase profile name

        Returns:
            Tuple of (downloaded_count, error_messages)
        """
        try:
            # Get all dataset sets and delegate to DatasetDownloader
            all_dataset_sets = []
            for batch in cls.get_dataset_sets(
                dataset_uuid=dataset_uuid,
                set_type=set_type,
                batch_size=100,
                profile=profile,
            ):
                all_dataset_sets.extend(batch)

            # Download all images using the DatasetDownloader
            return await cls._dataset_downloader.download_dataset_images(
                dataset_sets=all_dataset_sets,
                base_output_dir=base_output_dir,
            )

        except Exception as e:
            return 0, [f"Error processing dataset: {str(e)}"]

    @classmethod
    def _get_dataset_metadata(
        cls, dataset_uuid: Union[str, UUID], profile: Optional[str]
    ) -> Dict:
        """Get dataset metadata and handle empty dataset case."""
        try:
            dataset = cls.get_dataset(dataset_uuid, profile)
            expected_count = dataset.get("images_count", 0)
            if expected_count == 0:
                logger.warning("Dataset %s has no expected images", dataset_uuid)
                return {
                    "expected_count": 0,
                    "available_count": 0,
                    "missing_count": 0,
                    "is_complete": True,
                    "expected_images": {},
                    "missing_images": {},
                }
            return {"dataset": dataset, "expected_count": expected_count}
        except DatasetError as e:
            logger.error("Failed to fetch dataset metadata: %s", str(e))
            raise

    @classmethod
    def _build_expected_images_mapping(
        cls,
        dataset_uuid: Union[str, UUID],
        base_output_dir: Path,
        profile: Optional[str],
    ) -> Dict[str, Dict]:
        """Build mapping of expected images from dataset sets."""
        expected_images = {}
        try:
            for batch in cls.get_dataset_sets(
                dataset_uuid=dataset_uuid,
                batch_size=1000,
                profile=profile,
            ):
                for dataset_set in batch:
                    # Skip if no image_uuid (required for all images)
                    if not dataset_set.image_uuid:
                        continue

                    image_uuid = str(dataset_set.image_uuid)

                    # Use DatasetDownloader to get expected image info
                    image_info = cls._dataset_downloader.get_expected_image_info(
                        dataset_set=dataset_set,
                        base_output_dir=base_output_dir,
                    )

                    expected_images[image_uuid] = image_info
        except Exception as e:
            logger.error("Failed to build expected images mapping: %s", str(e))
            raise
        return expected_images

    @classmethod
    async def verify_dataset_images(
        cls,
        dataset_uuid: Union[str, UUID],
        base_output_dir: Union[str, Path],
        profile: Optional[str] = None,
    ) -> Dict[str, any]:
        """
        Verify dataset completeness by checking which images are missing locally.

        This method performs the following steps:
        1. Fetches dataset metadata to get expected image count
        2. Builds a mapping of expected images from dataset sets
        3. Checks local filesystem for missing images
        4. Returns verification statistics

        Args:
            dataset_uuid: UUID of the dataset to verify
            base_output_dir: Base directory containing the downloaded images
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing verification statistics:
            {
                "expected_count": int,
                "available_count": int,
                "missing_count": int,
                "is_complete": bool,
                "expected_images": Dict[str, Dict],
                "missing_images": Dict[str, Dict]
            }
        """
        base_output_dir = Path(base_output_dir)

        # Step 1: Get dataset metadata
        metadata_result = cls._get_dataset_metadata(dataset_uuid, profile)
        if "dataset" not in metadata_result:
            # Empty dataset case
            return metadata_result

        expected_count = metadata_result["expected_count"]

        # Step 2: Build expected images mapping
        expected_images = cls._build_expected_images_mapping(
            dataset_uuid, base_output_dir, profile
        )

        # Verify that we found the expected number of images in the dataset sets
        if len(expected_images) != expected_count:
            logger.warning(
                "Dataset metadata indicates %d images, but found %d in dataset sets",
                expected_count,
                len(expected_images),
            )

        # Step 3: Check which images are missing locally
        missing_images = {
            image_uuid: image_info
            for image_uuid, image_info in expected_images.items()
            if not image_info["path"].exists()
        }

        # Calculate counts and completion status
        available_count = len(expected_images) - len(missing_images)
        missing_count = len(missing_images)
        is_complete = missing_count == 0

        logger.info(
            "Dataset verification: %d/%d images available, %d missing",
            available_count,
            len(expected_images),
            missing_count,
        )

        return {
            "expected_count": len(expected_images),
            "available_count": available_count,
            "missing_count": missing_count,
            "is_complete": is_complete,
            "expected_images": expected_images,
            "missing_images": missing_images,
        }

    @classmethod
    async def recover_missing_dataset_images(
        cls,
        missing_images: Dict[str, Dict],
        base_output_dir: Union[str, Path],
    ) -> Dict[str, any]:
        """
        Attempt to recover missing dataset images with automatic retry.

        Args:
            missing_images: Dictionary of missing images from verify_dataset_images
            base_output_dir: Base directory containing the downloaded images

        Returns:
            Dictionary containing recovery statistics:
            {
                "recovered_count": int,
                "recovery_failed_count": int,
                "recovery_attempts": int,
                "still_missing": Dict[str, Dict]
            }
        """
        # Delegate to the DatasetDownloader class
        result = await cls._dataset_downloader.recover_missing_dataset_images(
            missing_images=missing_images,
            base_output_dir=base_output_dir,
        )

        # Add recovery_attempts field for backward compatibility
        result["recovery_failed_count"] = result.pop("still_missing_count")
        result["still_missing"] = result.pop(
            "still_missing_images"
        )  # Rename for consistency
        result["recovery_attempts"] = 2  # Default max attempts
        return result

    @classmethod
    def _prepare_missing_image_details(
        cls, still_missing: Dict[str, Dict]
    ) -> List[Dict]:
        """Prepare limited list of missing images for the result."""
        missing_image_details = []
        for image_uuid, image_info in list(still_missing.items())[
            :100
        ]:  # Limit to 100 items
            missing_image_details.append(
                {
                    "image_uuid": image_uuid,
                    "coin_side_uuid": image_info["coin_side_uuid"],
                    "path": str(image_info["path"]),
                    "has_url": image_info["image_url"] is not None,
                }
            )
        return missing_image_details

    @classmethod
    async def verify_and_recover_dataset_images(
        cls,
        dataset_uuid: Union[str, UUID],
        base_output_dir: Union[str, Path],
        profile: Optional[str] = None,
        auto_recover: bool = True,
    ) -> Dict[str, any]:
        """
        Verify dataset completeness and recover missing images with automatic retry.

        This method performs the following steps:
        1. Calls verify_dataset_images to check completeness
        2. If auto_recover is True and images are missing, calls recover_missing_dataset_images
        3. Returns detailed verification and recovery statistics

        Args:
            dataset_uuid: UUID of the dataset to verify
            base_output_dir: Base directory containing the downloaded images
            profile: Optional Supabase profile name
            auto_recover: Whether to automatically attempt to recover missing images

        Returns:
            Dictionary containing verification and recovery statistics:
            {
                "expected_count": int,
                "available_count": int,
                "missing_count": int,
                "recovered_count": int,
                "recovery_failed_count": int,
                "is_complete": bool,
                "missing_images": List[Dict],
                "recovery_attempts": int
            }
        """
        # Step 1: Verify dataset completeness
        verification_result = await cls.verify_dataset_images(
            dataset_uuid=dataset_uuid,
            base_output_dir=base_output_dir,
            profile=profile,
        )

        # Extract verification data
        expected_count = verification_result["expected_count"]
        missing_images = verification_result["missing_images"]

        # Initialize recovery statistics
        recovery_stats = {
            "recovered_count": 0,
            "recovery_failed_count": len(missing_images),
            "recovery_attempts": 0,
            "still_missing": missing_images,
        }

        # Step 2: Attempt recovery if enabled and needed
        if auto_recover and missing_images:
            recovery_result = await cls.recover_missing_dataset_images(
                missing_images=missing_images,
                base_output_dir=base_output_dir,
            )
            recovery_stats.update(recovery_result)
        elif not auto_recover and missing_images:
            logger.info(
                "Auto-recovery disabled, skipping recovery of %d missing images",
                len(missing_images),
            )

        # Calculate final statistics
        final_available_count = expected_count - recovery_stats["recovery_failed_count"]
        is_complete = recovery_stats["recovery_failed_count"] == 0
        missing_image_details = cls._prepare_missing_image_details(
            recovery_stats["still_missing"]
        )

        # Prepare and return the verification result
        result = {
            "expected_count": expected_count,
            "available_count": final_available_count,
            "missing_count": recovery_stats["recovery_failed_count"],
            "recovered_count": recovery_stats["recovered_count"],
            "recovery_failed_count": recovery_stats["recovery_failed_count"],
            "is_complete": is_complete,
            "missing_images": missing_image_details,
            "recovery_attempts": recovery_stats["recovery_attempts"],
        }

        # Log summary
        logger.info(
            "Dataset verification complete: %d/%d images available, %d recovered, %d still missing",
            final_available_count,
            expected_count,
            recovery_stats["recovered_count"],
            recovery_stats["recovery_failed_count"],
        )

        return result
