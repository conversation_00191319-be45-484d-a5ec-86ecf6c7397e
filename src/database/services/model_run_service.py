"""
Model Run Service for updating model run data.

This service provides functionality for updating specific fields in model_run records,
particularly timing fields, metrics, and log paths. It follows the established patterns
from other database services in the codebase.
"""

import logging
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field, field_validator

from database.models.model_run import ModelRun
from database.services.base_service import BaseService
from database.supabase_client import fetch_data
from database.supabase_client import update_data as update_db_data
from database.utils.model_factory import ModelValidationError
from train.metrics import Metrics
from train.metrics_persistence import MetricsPersistence

# Configure logger
logger = logging.getLogger(__name__)


class ModelRunServiceError(Exception):
    """Base exception for model run service related errors."""


class ModelRunUpdateData(BaseModel):
    """Data model for model run updates."""

    end_time: Optional[datetime] = Field(None, description="End time of the run")
    start_time: Optional[datetime] = Field(None, description="Start time of the run")
    prepared_time: Optional[datetime] = Field(
        None, description="Time when the run data was prepared"
    )
    dataset_content_updated_at: Optional[datetime] = Field(
        None, description="Time when dataset content was last updated"
    )
    log_path: Optional[bool] = Field(
        None, description="Flag indicating if logs are available"
    )
    metrics: Optional[Dict] = Field(None, description="Run metrics and results")

    @field_validator("log_path")
    @classmethod
    def validate_log_path(cls, v):
        """Validate log path format."""
        if v is not None and not isinstance(v, bool):
            raise ValueError("log_path must be a boolean or None")
        return v

    @field_validator("metrics")
    @classmethod
    def validate_metrics(cls, v):
        """Validate metrics structure."""
        if v is not None and not isinstance(v, dict):
            raise ValueError("metrics must be a dictionary or None")
        return v


@dataclass
class ModelRunTimingUpdate:
    """Data class for model run timing updates."""

    model_run_uuid: Union[str, UUID]
    end_time: Optional[datetime] = None
    start_time: Optional[datetime] = None
    prepared_time: Optional[datetime] = None
    dataset_content_updated_at: Optional[datetime] = None
    profile: Optional[str] = None


@dataclass
class ModelRunMetricsUpdate:
    """Data class for model run metrics updates."""

    model_run_uuid: Union[str, UUID]
    metrics: Optional[Dict] = None
    metrics_file_path: Optional[Union[str, Path]] = None
    log_path: Optional[bool] = None
    profile: Optional[str] = None


@dataclass
class ModelRunCompleteUpdate:
    """Data class for comprehensive model run updates."""

    model_run_uuid: Union[str, UUID]
    end_time: Optional[datetime] = None
    start_time: Optional[datetime] = None
    prepared_time: Optional[datetime] = None
    dataset_content_updated_at: Optional[datetime] = None
    log_path: Optional[bool] = None
    metrics: Optional[Dict] = None
    metrics_file_path: Optional[Union[str, Path]] = None
    profile: Optional[str] = None


class ModelRunService(BaseService[ModelRun]):
    """Service for updating model run data."""

    # Set the model class for this service
    model_class = ModelRun

    # Define required fields for validation (minimal for updates)
    required_fields = ["uuid"]

    @classmethod
    async def _prepare_datetime_fields(cls, update_data, field_names):
        """Helper method to prepare datetime fields for update.

        Args:
            update_data: The update data object containing datetime fields
            field_names: List of datetime field names to process

        Returns:
            Dictionary with processed datetime fields
        """
        update_fields = {}
        for field_name in field_names:
            value = getattr(update_data, field_name, None)
            if value is not None:
                update_fields[field_name] = value.isoformat()
        return update_fields

    @classmethod
    async def _perform_model_run_update(
        cls, model_run_uuid, update_fields, profile=None, context="fields"
    ):
        """Helper method to perform the actual database update.

        Args:
            model_run_uuid: UUID of the model run to update
            update_fields: Dictionary of fields to update
            profile: Optional Supabase profile name
            context: Context description for logging (e.g., "timing fields")

        Returns:
            Dictionary containing the updated model run data

        Raises:
            ModelRunServiceError: If the update fails
        """
        # Validate the model run exists
        await cls._validate_model_run_exists(model_run_uuid, profile)

        # Perform the update
        result = update_db_data(
            table="model_runs",
            data=update_fields,
            filters=[{"column": "uuid", "value": str(model_run_uuid)}],
            profile=profile,
        )

        if not result:
            raise ModelRunServiceError(f"Failed to update model run {model_run_uuid}")

        logger.info("Updated %s for model run %s", context, model_run_uuid)
        return result[0] if result else {}

    @classmethod
    async def update_model_run_times(cls, update_data: ModelRunTimingUpdate) -> Dict:
        """
        Update timing fields for a model run.

        Args:
            update_data: ModelRunTimingUpdate containing all update parameters

        Returns:
            Dictionary containing the updated model run data

        Raises:
            ModelRunServiceError: If the update fails or model run is not found
        """
        try:
            # Prepare update data, filtering out None values
            update_fields = await cls._prepare_datetime_fields(
                update_data,
                [
                    "end_time",
                    "start_time",
                    "prepared_time",
                    "dataset_content_updated_at",
                ],
            )

            if not update_fields:
                raise ModelRunServiceError(
                    "At least one timing field must be provided for update"
                )

            return await cls._perform_model_run_update(
                update_data.model_run_uuid,
                update_fields,
                update_data.profile,
                "timing fields",
            )

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(
                f"Error updating model run times: {str(e)}"
            ) from e

    @classmethod
    async def _prepare_metrics_fields(cls, update_data):
        """Helper method to prepare metrics fields for update.

        Args:
            update_data: The update data object containing metrics fields

        Returns:
            Dictionary with processed metrics fields and the loaded metrics
        """
        # Load metrics from file if path is provided
        metrics_dict = update_data.metrics
        if update_data.metrics_file_path:
            metrics_obj = ModelRunService.load_metrics_from_file(
                update_data.metrics_file_path
            )
            metrics_dict = metrics_obj.get_metric_summary()

        db_data = ModelRunUpdateData(
            metrics=metrics_dict,
            log_path=update_data.log_path,
        ).model_dump(exclude_unset=True)

        return db_data, metrics_dict

    @classmethod
    async def update_model_run_metrics(cls, update_data: ModelRunMetricsUpdate) -> Dict:
        """
        Update metrics and log path for a model run.

        Args:
            update_data: ModelRunMetricsUpdate containing all update parameters

        Returns:
            Dictionary containing the updated model run data

        Raises:
            ModelRunServiceError: If the update fails or model run is not found
        """
        try:
            # Prepare update data
            update_fields, _ = await cls._prepare_metrics_fields(update_data)

            if not update_fields:
                raise ModelRunServiceError(
                    "At least one field (metrics or log_path) must be provided for update"
                )

            # Validate the update data
            ModelRunUpdateData(**update_fields)

            return await cls._perform_model_run_update(
                update_data.model_run_uuid,
                update_fields,
                update_data.profile,
                "metrics/log_path",
            )

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(
                f"Error updating model run metrics: {str(e)}"
            ) from e

    @classmethod
    async def update_model_run_complete(
        cls, update_data: ModelRunCompleteUpdate
    ) -> Dict:
        """
        Comprehensive update for all supported model run fields.

        Args:
            update_data: ModelRunCompleteUpdate containing all update parameters

        Returns:
            Dictionary containing the updated model run data

        Raises:
            ModelRunServiceError: If the update fails or model run is not found
        """
        try:
            # Prepare datetime fields
            datetime_fields = await cls._prepare_datetime_fields(
                update_data,
                [
                    "end_time",
                    "start_time",
                    "prepared_time",
                    "dataset_content_updated_at",
                ],
            )

            # Prepare metrics fields
            metrics_fields, metrics = await cls._prepare_metrics_fields(update_data)

            # Combine all fields
            update_fields = {**datetime_fields, **metrics_fields}

            if not update_fields:
                raise ModelRunServiceError(
                    "At least one field must be provided for update"
                )

            # Validate the update data
            ModelRunUpdateData(
                **{
                    k: v
                    for k, v in {
                        "end_time": update_data.end_time,
                        "start_time": update_data.start_time,
                        "prepared_time": update_data.prepared_time,
                        "dataset_content_updated_at": update_data.dataset_content_updated_at,
                        "log_path": update_data.log_path,
                        "metrics": metrics,
                    }.items()
                    if v is not None
                }
            )

            return await cls._perform_model_run_update(
                update_data.model_run_uuid,
                update_fields,
                update_data.profile,
                f"model run with {len(update_fields)} fields",
            )

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(f"Error updating model run: {str(e)}") from e

    @classmethod
    def load_metrics_from_file(cls, metrics_file_path: Union[str, Path]) -> Metrics:
        """
        Load metrics from a JSON file using MetricsPersistence.

        Args:
            metrics_file_path: Path to the metrics.json file.

        Returns:
            A Metrics object containing the loaded data.

        Raises:
            ModelRunServiceError: If the file cannot be read or parsed.
        """
        try:
            persistence = (
                MetricsPersistence()
            )  # Allow MetricsPersistence to create its own logger
            return persistence.load_metrics(Path(metrics_file_path))
        except FileNotFoundError as e:
            logger.error("Metrics file not found: %s", e)
            raise ModelRunServiceError(
                f"Metrics file not found: {metrics_file_path}"
            ) from e
        except Exception as e:
            logger.error(
                "An unexpected error occurred while loading metrics from %s: %s",
                metrics_file_path,
                e,
            )
            raise ModelRunServiceError(
                f"Failed to load metrics from file: {metrics_file_path}"
            ) from e

    @classmethod
    async def _validate_model_run_exists(
        cls, model_run_uuid: Union[str, UUID], profile: Optional[str] = None
    ) -> None:
        """
        Validate that a model run exists in the database.

        Args:
            model_run_uuid: UUID of the model run to validate
            profile: Optional Supabase profile name

        Raises:
            ModelRunServiceError: If the model run is not found
        """
        try:
            model_runs = fetch_data(
                "model_runs",
                {"filters": [{"column": "uuid", "value": str(model_run_uuid)}]},
                profile=profile,
            )

            if not model_runs:
                raise ModelRunServiceError(
                    f"Model run not found with UUID: {model_run_uuid}"
                )

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(
                f"Error validating model run existence: {str(e)}"
            ) from e

    @classmethod
    async def get_model_run(
        cls, model_run_uuid: Union[str, UUID], profile: Optional[str] = None
    ) -> Dict:
        """
        Fetch model run data by UUID.

        Args:
            model_run_uuid: UUID of the model run
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing model run data

        Raises:
            ModelRunServiceError: If model run is not found
        """
        try:
            model_runs = fetch_data(
                "model_runs",
                {"filters": [{"column": "uuid", "value": str(model_run_uuid)}]},
                profile=profile,
            )

            if not model_runs:
                raise ModelRunServiceError(
                    f"Model run not found with UUID: {model_run_uuid}"
                )

            return model_runs[0]

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(f"Error fetching model run: {str(e)}") from e

    @classmethod
    def create_model_run_instance(cls, data: Dict, partial: bool = False) -> ModelRun:
        """
        Create a model run instance from dictionary data.

        Args:
            data: Dictionary containing the data
            partial: If True, allows partial data (missing non-required fields)

        Returns:
            An instance of the ModelRun class

        Raises:
            ModelRunServiceError: If validation fails
        """
        try:
            return cls.create_model(data=data, model_class=ModelRun, partial=partial)
        except ModelValidationError as e:
            raise ModelRunServiceError(f"Failed to create model run: {str(e)}") from e

    @classmethod
    async def get_scheduled_runs(cls, profile: Optional[str] = None) -> list:
        """
        Fetch model runs with 'scheduled' status based on their properties.

        This method uses the dynamic status calculation from ModelRun.get_status()
        to determine which runs are in 'scheduled' state.

        Args:
            profile: Optional Supabase profile name

        Returns:
            List of ModelRun instances with 'scheduled' status

        Raises:
            ModelRunServiceError: If there's an error fetching the data
        """
        try:
            # Query the database for model runs that meet the criteria for 'scheduled' status:
            # - schedule_time exists (required field)
            # - prepared_time has a timestamp (is not NULL)
            # - start_time is NULL
            # - end_time is NULL
            model_runs = fetch_data(
                "model_runs",
                {
                    "filters": [
                        {
                            "column": "prepared_time",
                            "value": None,
                            "operator": "is not",
                        },
                        {"column": "start_time", "value": None, "operator": "is"},
                        {"column": "end_time", "value": None, "operator": "is"},
                    ],
                    # Process oldest first
                    "order_by": [{"column": "created_at", "direction": "asc"}],
                },
                profile=profile,
            )
            # Convert the raw data to ModelRun instances
            run_instances = [cls.create_model_run_instance(run) for run in model_runs]

            # Double-check status using the get_status method (defensive programming)
            return [run for run in run_instances if run.get_status() == "scheduled"]

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            logger.error("Error fetching scheduled model runs: %s", str(e))
            raise ModelRunServiceError(
                f"Error fetching scheduled model runs: {str(e)}"
            ) from e
