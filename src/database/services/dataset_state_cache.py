"""
Dataset State Cache Service for managing dataset cache invalidation.

This service provides cache invalidation functionality based on dataset UUID
and content_updated_at timestamp to avoid unnecessary database calls when
dataset content hasn't changed.
"""

from datetime import datetime, timezone
from typing import Dict, Optional, Union
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


def _utc_now() -> datetime:
    """Get current UTC datetime using timezone-aware method."""
    return datetime.now(timezone.utc)


class DatasetCacheState(BaseModel):
    """Model representing cached dataset state information."""

    dataset_uuid: str | UUID = Field(..., description="UUID of the dataset")
    content_updated_at: Optional[datetime] = Field(
        None, description="Timestamp when dataset content was last updated"
    )
    cache_key: str = Field(..., description="Cache key for this dataset state")
    cached_at: datetime = Field(
        default_factory=_utc_now, description="When this state was cached"
    )
    is_prepared: bool = Field(
        default=False, description="Whether dataset preparation is complete"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "dataset_uuid": "b9dd3efa-cbe2-4a97-b895-ebb7a8e38fb5",
                "content_updated_at": "2025-02-27T18:28:18.77991+00:00",
                "cache_key": (
                    "b9dd3efa-cbe2-4a97-b895-ebb7a8e38fb5_"
                    "2025-02-27T18:28:18.77991+00:00"
                ),
                "cached_at": "2025-06-28T10:00:00.000000+00:00",
                "is_prepared": True,
            }
        }
    )


class DatasetStateCacheService:
    """Service for managing dataset state cache invalidation."""

    # In-memory cache for dataset states
    # In production, this could be replaced with Redis or another cache store
    _cache: Dict[str, DatasetCacheState] = {}

    @classmethod
    def generate_cache_key(
        cls,
        dataset_uuid: str | UUID,
        content_updated_at: Optional[Union[datetime, str]] = None,
    ) -> str:
        """
        Generate a cache key based on dataset UUID and content_updated_at.

        Args:
            dataset_uuid: UUID of the dataset
            content_updated_at: Timestamp when dataset content was last updated

        Returns:
            Cache key string in format: {dataset_uuid}_{content_updated_at}
        """
        dataset_uuid_str = str(dataset_uuid)
        if content_updated_at is None:
            return f"{dataset_uuid_str}_none"

        # Handle both datetime objects and string representations
        if isinstance(content_updated_at, str):
            timestamp_str = content_updated_at
        else:
            # Convert datetime to ISO format for consistent cache keys
            timestamp_str = content_updated_at.isoformat()
        return f"{dataset_uuid_str}_{timestamp_str}"

    @classmethod
    def is_cache_valid(
        cls,
        dataset_uuid: str | UUID,
        content_updated_at: Optional[Union[datetime, str]] = None,
    ) -> bool:
        """
        Check if the cached dataset state is still valid.

        Args:
            dataset_uuid: UUID of the dataset
            content_updated_at: Current timestamp when dataset content was last updated

        Returns:
            True if cache is valid and dataset preparation can be skipped
        """
        cache_key = cls.generate_cache_key(dataset_uuid, content_updated_at)
        cached_state = cls._cache.get(cache_key)

        if cached_state is None:
            return False

        # Check if the cached state indicates preparation is complete
        return cached_state.is_prepared

    @classmethod
    def get_cached_state(
        cls,
        dataset_uuid: str | UUID,
        content_updated_at: Optional[Union[datetime, str]] = None,
    ) -> Optional[DatasetCacheState]:
        """
        Get the cached dataset state if it exists.

        Args:
            dataset_uuid: UUID of the dataset
            content_updated_at: Timestamp when dataset content was last updated

        Returns:
            DatasetCacheState if cached, None otherwise
        """
        cache_key = cls.generate_cache_key(dataset_uuid, content_updated_at)
        return cls._cache.get(cache_key)

    @classmethod
    def set_cache_state(
        cls,
        dataset_uuid: str | UUID,
        content_updated_at: Optional[Union[datetime, str]] = None,
        is_prepared: bool = False,
    ) -> DatasetCacheState:
        """
        Set the cache state for a dataset.

        Args:
            dataset_uuid: UUID of the dataset
            content_updated_at: Timestamp when dataset content was last updated
            is_prepared: Whether dataset preparation is complete

        Returns:
            The created DatasetCacheState
        """
        cache_key = cls.generate_cache_key(dataset_uuid, content_updated_at)

        cache_state = DatasetCacheState(
            dataset_uuid=dataset_uuid,
            content_updated_at=content_updated_at,
            cache_key=cache_key,
            is_prepared=is_prepared,
        )

        cls._cache[cache_key] = cache_state
        return cache_state

    @classmethod
    def invalidate_dataset_cache(cls, dataset_uuid: str | UUID) -> int:
        """
        Invalidate all cache entries for a specific dataset UUID.

        This is useful when you know a dataset has been updated but don't have
        the specific content_updated_at timestamp.

        Args:
            dataset_uuid: UUID of the dataset to invalidate

        Returns:
            Number of cache entries that were invalidated
        """
        dataset_uuid_str = str(dataset_uuid)
        keys_to_remove = [
            key for key in cls._cache if key.startswith(f"{dataset_uuid_str}_")
        ]

        for key in keys_to_remove:
            del cls._cache[key]

        return len(keys_to_remove)

    @classmethod
    def clear_cache(cls) -> int:
        """
        Clear all cached dataset states.

        Returns:
            Number of cache entries that were cleared
        """
        count = len(cls._cache)
        cls._cache.clear()
        return count

    @classmethod
    def get_cache_stats(cls) -> Dict[str, int]:
        """
        Get statistics about the current cache state.

        Returns:
            Dictionary with cache statistics
        """
        total_entries = len(cls._cache)
        prepared_entries = sum(1 for state in cls._cache.values() if state.is_prepared)

        return {
            "total_entries": total_entries,
            "prepared_entries": prepared_entries,
            "unprepared_entries": total_entries - prepared_entries,
        }
