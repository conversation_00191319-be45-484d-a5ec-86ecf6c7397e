"""
DatasetSet model representing the dataset_sets table.
"""

from datetime import datetime
from enum import IntEnum
from typing import Dict, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, ConfigDict, Field


class SetType(IntEnum):
    """Enum for dataset set types."""

    TRAIN = 1
    VALIDATION = 2
    TEST = 3


class DatasetSet(BaseModel):
    """Model representing a dataset set record."""

    uuid: str | UUID = Field(default_factory=uuid4, description="Unique identifier")
    dataset_uuid: str | UUID = Field(
        ..., description="UUID of the dataset this set belongs to"
    )
    created_at: Optional[datetime] = Field(
        default=None, description="Timestamp of creation"
    )
    coin_side_uuid: Optional[str | UUID] = Field(
        None, description="UUID of the coin side"
    )
    set_type: SetType = Field(
        ..., description="Type of dataset set (TRAIN=1, VALIDATION=2, TEST=3)"
    )
    image_uuid: Optional[str | UUID] = Field(None, description="UUID of the image")
    images_reviews: Optional[Dict] = Field(
        None, description="Joined data from images_reviews table"
    )
    image_url: Optional[str] = Field(None, description="URL of the image")

    model_config = ConfigDict(
        use_enum_values=True,
        json_schema_extra={
            "example": {
                "uuid": "f1561add-6a2c-48ca-9911-69b10bf3f983",
                "dataset_uuid": "8b1003f7-c430-4fdc-999b-7c5fec055269",
                "created_at": "2025-03-13T20:25:20.798573+00:00",
                "coin_side_uuid": "d57d52e4-093a-4c26-96b6-6fdecb7e7191",
                "set_type": SetType.TRAIN,
                "image_uuid": "b03f9948-e219-40a4-bf10-c21e9ac9927a",
                "images_reviews": {
                    "image_url": "/images/coins/4428.jpg",
                    "location": "CS",
                    "fact_uuid": None,
                    "dataset_uuid": "8b1003f7-c430-4fdc-999b-7c5fec055269",
                },
                "image_path": "static/coins/d57/d52/4428.jpg",
            },
        },
    )
