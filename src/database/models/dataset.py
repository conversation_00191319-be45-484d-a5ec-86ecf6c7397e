"""
Dataset model definition.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class Dataset(BaseModel):
    """Dataset model representing a collection of training data."""

    uuid: str | UUID = Field(default=None, description="Unique identifier")
    name: str = Field(..., description="Name of the dataset")
    description: str | None = Field(None, description="Dataset description")
    user_id: str | UUID | None = Field(
        None, description="ID of the user who owns the dataset"
    )
    cover: str | None = Field(None, description="Cover image filename")
    images_count: Optional[int] = Field(0, description="Number of images in dataset")
    created_at: Optional[datetime] = Field(
        default=None, description="Timestamp of creation"
    )
    content_updated_at: Optional[datetime] = Field(
        default=None, description="Timestamp of content update"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "uuid": "b9dd3efa-cbe2-4a97-b895-ebb7a8e38fb5",
                "name": "Landscape",
                "description": "The coin side includes a complicated illustration "
                "of natural scene with wildlife in any landscape.",
                "user_id": "c368cec9-9875-435c-8e1c-cca131b4faad",
                "cover": "e3f77acefb06d8c1899f4c0e1fdfa1d943119ca8.png",
                "images_count": 534003,
                "created_at": "2025-02-27T18:28:18.77991+00:00",
                "content_updated_at": None,
            },
        }
    )
