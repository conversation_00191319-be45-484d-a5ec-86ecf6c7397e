"""
Model class definition.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator

from models.architectures import ModelArchitecture, get_architecture_info


class Model(BaseModel):
    """Model representing a coin classification model with given architecture type."""

    uuid: str | UUID = Field(default=None, description="Unique identifier")
    name: str = Field(..., description="Name of the model")
    description: str | None = Field(None, description="Model description")
    architecture: str = Field(..., description="Model architecture type")
    user_id: str | UUID | None = Field(
        None, description="ID of the user who created the model"
    )
    created_at: Optional[datetime] = Field(
        default=None, description="Timestamp of creation"
    )

    @field_validator("architecture")
    @classmethod
    def validate_architecture(cls, v):
        """Validate that the architecture is supported."""
        try:
            # This will raise ValueError if architecture is not supported
            get_architecture_info(v)
            return v
        except ValueError as e:
            # List all valid architectures in the error message
            valid_architectures = ", ".join([a.value for a in ModelArchitecture])
            raise ValueError(
                f"Invalid architecture: {v}. "
                f"Valid architectures are: {valid_architectures}"
            ) from e

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "uuid": "123e4567-e89b-12d3-a456-************",
                "name": "Euro Coin Classifier",
                "description": "Model for classifying Euro coins",
                "architecture": "ResNet50",
                "user_id": "98765432-e89b-12d3-a456-************",
                "created_at": "2024-01-20T12:00:00Z",
            }
        }
    )
