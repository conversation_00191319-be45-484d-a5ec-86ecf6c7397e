"""
Base classes for the job dispatcher system.

This module provides the foundation for creating job dispatchers
that can schedule, monitor, and manage asynchronous jobs.
"""

from __future__ import annotations

import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, Generic, List, Optional, TypeVar

from src.common.callbacks.base import CallbackHandler
from src.common.callbacks.job import JobCallback

logger = logging.getLogger(__name__)


class JobStatus(Enum):
    """Status of a job in the dispatcher system."""

    PENDING = "pending"  # Job is waiting to be scheduled
    SCHEDULED = "scheduled"  # Job is scheduled but not yet running
    RUNNING = "running"  # Job is currently running
    COMPLETED = "completed"  # Job completed successfully
    FAILED = "failed"  # Job failed with an error
    CANCELLED = "cancelled"  # Job was cancelled before completion


@dataclass
class JobPriority:
    """Priority levels for jobs."""

    LOW = 0
    NORMAL = 50
    HIGH = 100
    CRITICAL = 200


@dataclass
class JobResources:
    """Resource requirements for a job."""

    cpu_cores: float = 1.0  # Number of CPU cores needed
    memory_gb: float = 2.0  # Memory in GB
    gpu_required: bool = False  # Whether GPU is required
    gpu_memory_gb: float = 0.0  # GPU memory in GB (if gpu_required is True)


# Generic type for job data
T = TypeVar("T")


@dataclass
class Job(Generic[T]):
    # pylint: disable=too-many-instance-attributes
    """
    Base class representing a job to be executed by a dispatcher.

    A job contains all the information needed to execute a specific task,
    including its unique identifier, status, priority, and resource requirements.
    """

    # Required fields
    job_id: str
    data: T

    # Optional fields with defaults
    priority: int = JobPriority.NORMAL
    resources: JobResources = field(
        default=None
    )  # Will be initialized in __post_init__
    status: JobStatus = JobStatus.PENDING
    error: Optional[Exception] = field(default=None)
    result: Any = field(default=None)
    created_at: float = field(default=None)  # Will be initialized in __post_init__
    started_at: Optional[float] = field(default=None)
    completed_at: Optional[float] = field(default=None)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Initialize default values that can't be directly set in dataclass fields."""
        if self.resources is None:
            self.resources = JobResources()
        if self.created_at is None:
            self.created_at = asyncio.get_event_loop().time()

    @property
    def duration(self) -> Optional[float]:
        """Return the job duration in seconds, or None if not completed."""
        if self.started_at is None:
            return None
        if self.completed_at is None:
            # Return current duration if job is still running
            return asyncio.get_event_loop().time() - self.started_at
        return self.completed_at - self.started_at

    def __lt__(self, other: Job) -> bool:
        """Compare jobs by priority (for priority queue)."""
        if not isinstance(other, Job):
            return NotImplemented
        # Higher priority values come first, then older jobs (lower created_at)
        return self.priority > other.priority or (
            self.priority == other.priority and self.created_at < other.created_at
        )


class JobDispatcher(ABC):
    """
    Abstract base class for job dispatchers.

    A job dispatcher is responsible for scheduling, executing, and monitoring jobs.
    It manages a queue of jobs and executes them according to their priority
    and resource requirements.
    """

    def __init__(self, callbacks: Optional[List[JobCallback]] = None):
        """
        Initialize the job dispatcher.

        Args:
            callbacks: List of callbacks to be notified of job events
        """
        self.callback_handler = CallbackHandler(callbacks, self)
        self.jobs: Dict[str, Job] = {}
        self._running = False

    def add_callback(self, callback: JobCallback) -> None:
        """Add a callback to the dispatcher."""
        self.callback_handler.add_callback(callback)

    def _notify_callbacks(self, method_name: str, *args, **kwargs) -> None:
        """Notify all callbacks by calling the specified method."""
        self.callback_handler.call_method(method_name, *args, **kwargs)

    @abstractmethod
    async def start(self) -> None:
        """Start the dispatcher."""
        self._running = True
        self._notify_callbacks("on_dispatcher_start")

    @abstractmethod
    async def stop(self) -> None:
        """Stop the dispatcher."""
        self._running = False
        self._notify_callbacks("on_dispatcher_stop")

    @abstractmethod
    async def schedule_job(self, job: Job) -> None:
        """
        Schedule a job for execution.

        Args:
            job: The job to schedule
        """

    @abstractmethod
    async def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a scheduled or running job.

        Args:
            job_id: ID of the job to cancel

        Returns:
            True if the job was cancelled, False otherwise
        """

    @abstractmethod
    async def get_job_status(self, job_id: str) -> Optional[JobStatus]:
        """
        Get the current status of a job.

        Args:
            job_id: ID of the job to check

        Returns:
            The job's status, or None if the job doesn't exist
        """
