"""
Resource monitoring callback for job dispatching.

This module provides a callback for monitoring system resources during job execution.
"""

import asyncio
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional, Union

import psutil
import torch

from src.jobs.base import Job, JobCallback
from src.train.metrics import Metrics
from src.train.metrics_persistence import MetricsPersistence

logger = logging.getLogger(__name__)


@dataclass
class ResourceMonitorConfig:
    """Configuration for resource monitoring."""

    monitoring_interval: float = 30.0
    cpu_threshold: float = 90.0
    memory_threshold: float = 90.0
    gpu_memory_threshold: float = 90.0
    save_metrics: bool = False
    metrics_output_dir: Optional[Union[str, Path]] = None


class ResourceMonitorCallback(JobCallback):
    """
    Callback for monitoring system resources during job execution.

    This callback collects information about CPU, memory, and GPU usage
    during job execution and can trigger alerts if resources are constrained.
    Uses the Metrics class for structured storage and optional persistence.
    """

    def __init__(self, config: Optional[ResourceMonitorConfig] = None):
        """
        Initialize the resource monitor callback.

        Args:
            config: Configuration for resource monitoring. If None, uses default values.
        """
        super().__init__()

        # Use provided config or create default
        self.config = config or ResourceMonitorConfig()

        # Set up essential properties
        self.metrics_output_dir = (
            Path(self.config.metrics_output_dir)
            if self.config.metrics_output_dir
            else None
        )

        self._monitoring_tasks: Dict[str, asyncio.Task] = {}
        self._job_metrics: Dict[str, Metrics] = {}

        # Initialize metrics persistence if needed
        if self.config.save_metrics:
            self._metrics_persistence = MetricsPersistence()
        else:
            self._metrics_persistence = None

    def on_job_start(self, job: Job, logs: Dict[str, Any] | None = None) -> None:
        """Called when a job starts execution."""
        # Initialize metrics for this job
        self._job_metrics[job.job_id] = Metrics()

        # Start monitoring resources for this job
        task = asyncio.create_task(self._monitor_resources(job))
        self._monitoring_tasks[job.job_id] = task

    def on_job_complete(self, job: Job, logs: Dict[str, Any] | None = None) -> None:
        """Called when a job completes successfully."""
        # Stop monitoring resources for this job and calculate summary metrics
        self._stop_monitoring(job)

    def on_job_failed(
        self, job: Job, error: Exception, logs: Dict[str, Any] | None = None
    ) -> None:
        """Called when a job fails."""
        # Stop monitoring resources for this job and calculate summary metrics
        self._stop_monitoring(job)

    def on_job_cancelled(self, job: Job, logs: Dict[str, Any] | None = None) -> None:
        """Called when a job is cancelled."""
        # Stop monitoring resources for this job and calculate summary metrics
        self._stop_monitoring(job)

    def _stop_monitoring(self, job: Job) -> None:
        """Stop monitoring resources for a job and record summary metrics."""
        task = self._monitoring_tasks.pop(job.job_id, None)
        if task:
            task.cancel()

        # Get the metrics for this job
        metrics = self._job_metrics.get(job.job_id)
        if metrics:
            # Save metrics to file if enabled
            if (
                self.config.save_metrics
                and self._metrics_persistence
                and self.metrics_output_dir
            ):
                try:
                    self._metrics_persistence.save_metrics(
                        metrics=metrics,
                        output_dir=self.metrics_output_dir,
                    )
                    logger.info("Saved resource metrics for job %s", job.job_id)
                except Exception as e:
                    logger.error(
                        "Failed to save resource metrics for job %s: %s", job.job_id, e
                    )

            # Clean up metrics from memory
            self._job_metrics.pop(job.job_id, None)

    async def _monitor_resources(self, job: Job) -> None:
        """Monitor system resources during job execution."""
        try:
            while True:
                try:
                    # Get the metrics instance for this job
                    metrics_instance = self._job_metrics.get(job.job_id)
                    if not metrics_instance:
                        logger.warning(
                            "No metrics instance found for job %s", job.job_id
                        )
                        break

                    # Collect CPU and memory metrics
                    cpu_percent = psutil.cpu_percent(interval=0.1)
                    memory = psutil.virtual_memory()

                    # Store metrics in the Metrics instance
                    metrics_instance.storage.resources["cpu_percent"].append(
                        cpu_percent
                    )
                    metrics_instance.storage.resources["memory_percent"].append(
                        memory.percent
                    )
                    metrics_instance.storage.resources["memory_available_gb"].append(
                        memory.available / (1024**3)
                    )

                    # Collect GPU metrics if available
                    if torch.cuda.is_available():
                        gpu_memory_allocated = torch.cuda.memory_allocated() / (1024**3)
                        gpu_memory_reserved = torch.cuda.memory_reserved() / (1024**3)
                        gpu_memory_percent = 0.0

                        # Calculate percentage if we can determine total memory
                        if gpu_memory_reserved > 0:
                            gpu_memory_percent = (
                                gpu_memory_allocated / gpu_memory_reserved
                            ) * 100

                        metrics_instance.storage.resources["gpu_memory_used_gb"].append(
                            gpu_memory_allocated
                        )
                        metrics_instance.storage.resources[
                            "gpu_memory_reserved_gb"
                        ].append(gpu_memory_reserved)
                        metrics_instance.storage.resources["gpu_memory_percent"].append(
                            gpu_memory_percent
                        )

                    # Log resource usage periodically for monitoring
                    if len(metrics_instance.storage.resources["cpu_percent"]) % 10 == 0:
                        if torch.cuda.is_available():
                            logger.debug(
                                "Resource usage for job %s - CPU: %.1f%%, Memory: %.1f%%, "
                                "GPU Memory: %.2fGB",
                                job.job_id,
                                cpu_percent,
                                memory.percent,
                                gpu_memory_allocated,
                            )
                        else:
                            logger.debug(
                                "Resource usage for job %s - CPU: %.1f%%, Memory: %.1f%%",
                                job.job_id,
                                cpu_percent,
                                memory.percent,
                            )
                except Exception as e:
                    logger.error(
                        "Error collecting resource metrics for job %s: %s",
                        job.job_id,
                        e,
                    )

                # Wait for next monitoring interval
                await asyncio.sleep(self.config.monitoring_interval)

        except asyncio.CancelledError:
            # Task was cancelled, clean up
            pass
