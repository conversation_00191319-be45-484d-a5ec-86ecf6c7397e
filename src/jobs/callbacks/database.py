"""
Database update callback for job dispatching.

This module provides a callback for updating job status in the database.
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict

from src.config.paths import get_run_paths
from src.database.services.model_run_service import (
    ModelRunCompleteUpdate,
    ModelRunMetricsUpdate,
    ModelRunService,
    ModelRunTimingUpdate,
)
from src.jobs.base import Job, JobCallback
from src.jobs.training_data import TrainingJob
from src.train.metrics import Metrics
from src.train.metrics_persistence import MetricsPersistence
from src.utils.async_utils import maybe_await

logger = logging.getLogger(__name__)


class DatabaseUpdateCallback(JobCallback):
    """
    Callback for updating job status in the database.

    This callback integrates with database services to update job status,
    timing information, and results.
    """

    def __init__(self, update_frequency: int = 1, verbose: bool = True):
        """
        Initialize the database update callback.

        Args:
            update_frequency: How often to update status (every N jobs)
            verbose: Whether to log database operations
        """
        super().__init__()
        self.update_frequency = update_frequency
        self.verbose = verbose
        self.job_count = 0

    def on_job_start(self, job: Job, logs: Dict[str, Any] | None = None) -> None:
        """Called when a job starts execution."""
        if isinstance(job, TrainingJob):
            asyncio.create_task(self._update_training_job_start(job))

    def on_job_complete(self, job: Job, logs: Dict[str, Any] | None = None) -> None:
        """Called when a job completes successfully."""
        if isinstance(job, TrainingJob):
            asyncio.create_task(self._update_training_job_complete(job, logs=logs))

    def on_job_failed(
        self, job: Job, error: Exception, logs: Dict[str, Any] | None = None
    ) -> None:
        """Called when a job fails."""
        if isinstance(job, TrainingJob):
            asyncio.create_task(self._update_training_job_failed(job, error))

    async def _update_training_job_start(self, job: TrainingJob) -> None:
        """Update the database when a training job starts."""
        try:
            if self.verbose:
                logger.info("Updating database for training job start: %s", job.job_id)

            model_run_service = ModelRunService()

            # Create timing update
            update = ModelRunTimingUpdate(
                model_run_uuid=job.data.model_run_uuid,
                start_time=datetime.now(),
            )

            # Update the database for timing
            await maybe_await(model_run_service.update_model_run_times(update))

            # Create metrics update for job_id
            metrics_update = ModelRunMetricsUpdate(
                model_run_uuid=job.data.model_run_uuid, metrics={"job_id": job.job_id}
            )
            await maybe_await(
                model_run_service.update_model_run_metrics(metrics_update)
            )

        except Exception as e:
            logger.error("Error updating training job start: %s", e)

    async def _update_training_job_complete(
        self, job: TrainingJob, logs: Dict[str, Any] | None = None
    ) -> None:
        """Update the database when a training job completes."""
        try:
            if self.verbose:
                logger.info(
                    "Updating database for training job completion: %s", job.job_id
                )

            model_run_service = ModelRunService()
            metrics_summary = None
            metrics_file_path = None

            # Check for a Metrics object in the job result
            if job.result and isinstance(job.result, Metrics):
                metrics = job.result
                # Use the centralized path helper to determine the save directory
                run_paths = get_run_paths(job.data.model_run_uuid)
                save_dir = run_paths.metrics
                save_dir.mkdir(parents=True, exist_ok=True)

                # Save the full metrics object using an instance of MetricsPersistence
                persistence = MetricsPersistence(logger=logger)
                saved_files = persistence.save_metrics(
                    metrics=metrics, output_dir=save_dir
                )
                # Store the path to the detailed history file
                metrics_file_path = saved_files.get("history")

                # Get the summary for database storage
                metrics_summary = metrics.get_metric_summary()

            # Fallback for logs if no Metrics object is present
            elif logs:
                metrics_summary = logs

            # Create complete update
            update = ModelRunCompleteUpdate(
                model_run_uuid=job.data.model_run_uuid,
                end_time=datetime.now(),
                metrics=metrics_summary,
                metrics_file_path=str(metrics_file_path) if metrics_file_path else None,
            )

            # Update the database
            await maybe_await(model_run_service.update_model_run_complete(update))

        except Exception as e:
            logger.error("Error updating training job completion: %s", e)

    async def _update_training_job_failed(
        self, job: TrainingJob, error: Exception
    ) -> None:
        """Update the database when a training job fails."""
        try:
            if self.verbose:
                logger.info(
                    "Updating database for training job failure: %s", job.job_id
                )

            model_run_service = ModelRunService()

            # Create complete update with error in metrics
            update = ModelRunCompleteUpdate(
                model_run_uuid=job.data.model_run_uuid,
                end_time=datetime.now(),
                metrics={"error": str(error), "error_type": type(error).__name__},
            )

            # Update the database
            await maybe_await(model_run_service.update_model_run_complete(update))

        except Exception as e:
            logger.error("Error updating training job failure: %s", e)
