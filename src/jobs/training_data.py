"""
Data classes for training jobs.

This module defines the data structures used by the training job dispatcher.
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from src.jobs.base import Job, JobResources
from src.train.callbacks import Callback


@dataclass
class TrainingJobData:
    """
    Data required for a training job.

    This class encapsulates all the data needed to initialize and run
    a ModelTrainer instance for a specific model run.
    """

    # Database identifiers
    model_run_uuid: Union[str, UUID]
    profile: Optional[str] = None

    # Training configuration
    model_components: Dict[str, Any] = field(default_factory=dict)
    data_loaders: Dict[str, Any] = field(default_factory=dict)
    training_config: Dict[str, Any] = field(default_factory=dict)

    # Optional callbacks for the ModelTrainer
    callbacks: List[Callback] = field(default_factory=list)

    # Resource requirements
    resources: JobResources = field(
        default_factory=lambda: JobResources(
            cpu_cores=2.0, memory_gb=4.0, gpu_required=True, gpu_memory_gb=2.0
        )
    )


class TrainingJob(Job[TrainingJobData]):
    """
    A job for training a machine learning model.

    This class extends the base Job class with training-specific functionality.
    """

    def __init__(
        self,
        job_id: str,
        data: TrainingJobData,
        priority: int = 50,
    ):
        """
        Initialize a training job.

        Args:
            job_id: Unique identifier for the job
            data: Training job data
            priority: Job priority
        """
        super().__init__(
            job_id=job_id, data=data, priority=priority, resources=data.resources
        )

        # Training-specific metadata
        self.metadata.update(
            {
                "model_run_uuid": str(data.model_run_uuid),
                "profile": data.profile,
            }
        )
