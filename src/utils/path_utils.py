"""
Utilities for path manipulation.
"""

import os
from pathlib import Path
from typing import List, Optional

from config.locations import (
    FACT_IMAGE_LOCATION_KEYS,
    IMAGE_LOCATIONS,
    ImageLocationKeys,
)


def get_folders_structure_from_uuid(uuid: str, depth: int = 2) -> List[str]:
    """
    Generate a list of folder names from a UUID string.

    This function replicates the behavior of the TypeScript function
    getFoldersStructureFromUuid.

    Args:
        uuid: The UUID string.
        depth: The desired depth of the folder structure.

    Returns:
        A list of strings representing the folder structure.
    """
    if not uuid:
        return []

    normalized_uuid = uuid.replace("-", "")
    parts = [normalized_uuid[i : i + 3] for i in range(0, len(normalized_uuid), 3)]
    return parts[:depth]


def is_image_location_key(location_key: str) -> bool:
    """
    Check if a location key is a valid image location key.

    Args:
        location_key: The location key to check.

    Returns:
        True if the location key is valid, False otherwise.
    """
    try:
        ImageLocationKeys(location_key)
        return True
    except ValueError:
        return False


def is_facts_image_location_key(location_key: str) -> bool:
    """
    Check if a location key is a facts-related image location key.

    Args:
        location_key: The location key to check.

    Returns:
        True if the location key is facts-related, False otherwise.
    """
    try:
        return ImageLocationKeys(location_key) in FACT_IMAGE_LOCATION_KEYS
    except ValueError:
        return False


def get_filename(image_url: str) -> str:
    """
    Extract the filename from an image URL.

    Args:
        image_url: The image URL.

    Returns:
        The filename extracted from the URL.
    """
    return os.path.basename(image_url)


def build_image_path(
    image_url: str,
    location_key: str,
    fact_uuid: Optional[str] = None,
    coin_side_uuid: Optional[str] = None,
    base_dir: Optional[str] = None,
) -> str:
    """
    Build the complete image path based on location key and UUIDs.

    This function replicates the behavior of the TypeScript ImageUrl function.

    Args:
        image_url: The original image URL.
        location_key: The location key indicating where the image should be stored.
        fact_uuid: The fact UUID (required for fact-related images).
        coin_side_uuid: The coin side UUID (required for coin-related images).
        base_dir: Optional absolute base directory to prepend to the relative path.

    Returns:
        The complete image path (absolute if base_dir provided, relative otherwise).
    """
    # Early validation checks
    if not is_image_location_key(location_key) or (
        not fact_uuid and not coin_side_uuid
    ):
        return image_url

    # Determine the directory structure based on location key
    dir_parts = _get_directory_parts(location_key, fact_uuid, coin_side_uuid)
    if not dir_parts:
        return image_url

    # Get the base location path
    base_location = IMAGE_LOCATIONS.get(ImageLocationKeys(location_key))
    if not base_location:
        return image_url

    # Build the complete path
    filename = get_filename(image_url)
    relative_path = Path(base_location) / Path(*dir_parts) / filename

    # If base_dir is provided, prepend it to create an absolute path
    if base_dir:
        complete_path = Path(base_dir) / relative_path
    else:
        complete_path = relative_path

    return str(complete_path)


def _get_directory_parts(
    location_key: str, fact_uuid: Optional[str], coin_side_uuid: Optional[str]
) -> List[str]:
    """
    Get directory parts based on location key and UUIDs.

    Args:
        location_key: The location key.
        fact_uuid: The fact UUID.
        coin_side_uuid: The coin side UUID.

    Returns:
        List of directory parts or empty list if invalid.
    """
    if location_key == ImageLocationKeys.SCRAPES:
        return [fact_uuid] if fact_uuid else []

    if is_facts_image_location_key(location_key):
        return get_folders_structure_from_uuid(fact_uuid) if fact_uuid else []

    return get_folders_structure_from_uuid(coin_side_uuid) if coin_side_uuid else []
