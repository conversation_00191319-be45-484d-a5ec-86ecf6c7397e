"""
This module provides utility functions for image manipulation and previewing datasets.
"""

import matplotlib.pyplot as plt
import matplotlib_inline.backend_inline
import numpy as np

matplotlib_inline.backend_inline.set_matplotlib_formats("svg")


def dataset_preview(images, labels, number_per_class=10):
    """Displays a preview of images from the dataset with their labels."""
    _, axs = plt.subplots(3, 7, figsize=(13, 6))

    for _, a_x in enumerate(axs.flatten()):
        which_pic = np.random.randint(2 * number_per_class)
        image_data = np.squeeze(images[which_pic, :, :])
        a_x.imshow(image_data, vmin=-1, vmax=1, cmap="jet")
        a_x.set_title(f"Class {int(labels[which_pic].item())}")
        a_x.set_xticks([])
        a_x.set_yticks([])

    plt.show()
