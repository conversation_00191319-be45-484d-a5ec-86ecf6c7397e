"""
Utility functions for visualizing data using Matplotlib.

This module provides functions to plot training/testing losses and accuracies,
Gaussian blurs with model predictions, feature map correlations, and feature map activations.
"""

# Collections of utils to visualize data

from dataclasses import dataclass
from typing import Optional

import matplotlib
import matplotlib.pyplot as plt
import matplotlib_inline.backend_inline
import numpy as np
import torch

from src.config.paths import get_plot_path

# Set matplotlib to use a non-interactive backend for better memory management
matplotlib.use("Agg")  # Use Anti-Grain Geometry backend (no GUI, less memory)

matplotlib_inline.backend_inline.set_matplotlib_formats("svg")

# Configure matplotlib to be more aggressive about memory management
plt.rcParams["figure.max_open_warning"] = 5  # Very low threshold for warnings
plt.rcParams["figure.raise_window"] = False  # Don't raise windows automatically
plt.rcParams["figure.figsize"] = [
    6.4,
    4.8,
]  # Smaller default figure size to save memory


def _ensure_figure_cleanup():
    """Ensure all matplotlib figures are properly closed to prevent memory leaks."""
    # Close all figures that might be lingering
    plt.close("all")


def _save_or_show_plot(fig, output_path, dpi=150, bbox_inches="tight"):
    """
    Common utility to save or show a matplotlib plot.

    Args:
        fig: Matplotlib figure object
        output_path: Path to save the plot, or None to show it
        dpi: DPI for saved plots (default: 150)
        bbox_inches: Bbox setting for saved plots (default: "tight")
    """
    if output_path:
        plt.savefig(output_path, dpi=dpi, bbox_inches=bbox_inches)
        plt.close(fig)
    else:
        plt.show()


@dataclass
class AugmentationPlotData:
    """Data container for augmentation visualization plots."""

    y_true: torch.Tensor
    y_pred: torch.Tensor
    augmented_images: torch.Tensor
    original_images: torch.Tensor
    original_labels: torch.Tensor


# Loss
def plot_losses(train_loss, test_loss, output_path=None, model_run_uuid=None):
    """
    Plots training and testing losses over epochs.

    Args:
        train_loss (list or np.ndarray): List of training losses per epoch.
        test_loss (list or np.ndarray): List of testing losses per epoch.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
        model_run_uuid (str, optional): Model run UUID for centralized path generation.
    """
    # Ensure clean state before creating new figure
    _ensure_figure_cleanup()

    fig, a_x = plt.subplots(1, 1, figsize=(16, 5))

    a_x.plot(train_loss, "s-", label="Train")
    a_x.plot(test_loss, "o-", label="Test")
    a_x.set_xlabel("Epochs")
    a_x.set_ylabel("Loss (MSE)")
    a_x.set_title("Model loss")
    a_x.legend()

    final_output_path = get_plot_path("losses.png", output_path, model_run_uuid)
    if final_output_path:
        plt.savefig(final_output_path)
        plt.close(fig)
        _ensure_figure_cleanup()  # Extra cleanup after saving
    else:
        plt.show()


# Accuracy
def plot_accuracy(train_accuracy, test_accuracy, output_path=None, model_run_uuid=None):
    """
    Plots training and testing accuracies over epochs.

    Args:
        train_accuracy (list or np.ndarray): List of training accuracies per epoch.
        test_accuracy (list or np.ndarray): List of testing accuracies per epoch.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
        model_run_uuid (str, optional): Model run UUID for centralized path generation.
    """
    # Ensure clean state before creating new figure
    _ensure_figure_cleanup()

    fig, a_x = plt.subplots(1, 1, figsize=(16, 5))
    a_x.plot(train_accuracy, "s-", label="Train")
    a_x.plot(test_accuracy, "o-", label="Test")
    a_x.set_xlabel("Epochs")
    a_x.set_ylabel("Accuracy (%)")
    a_x.set_title(f"Final model test accuracy: {test_accuracy[-1]:.2f}%")
    a_x.legend()

    final_output_path = get_plot_path("accuracy.png", output_path, model_run_uuid)
    if final_output_path:
        plt.savefig(final_output_path)
        plt.close(fig)
        _ensure_figure_cleanup()  # Extra cleanup after saving
    else:
        plt.show()


def plot_gaussian_blurs(y_true, y_pred, images, output_path=None):
    """
    Displays a grid of images with their true and predicted labels.

    Typically used to show original images and their model predictions, for example,
    in a binary classification task (e.g., original vs. blurred).

    Args:
        y_true (torch.Tensor): True labels for the images.
        y_pred (torch.Tensor): Predicted labels or raw model outputs for the images.
        images (torch.Tensor): Batch of images to display.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
    """
    fig, axs = plt.subplots(2, 10, figsize=(15, 4))

    for i, a_x in enumerate(axs.flatten()):
        image_tensor = torch.squeeze(images[i, 0, :, :]).detach()
        a_x.imshow(image_tensor, vmin=-1, vmax=1, cmap="jet")
        true_label = int(y_true[i].item())
        pred_label = int(y_pred[i].item() > 0.5)
        a_x.set_title(f"T:{true_label}, P:{pred_label}")
        a_x.set_xticks([])
        a_x.set_yticks([])

    if output_path:
        plt.savefig(output_path)
        plt.close(fig)
    else:
        plt.show()


def _plot_single_augmentation_column(
    plot_data: AugmentationPlotData, axs, col_idx: int
):
    """Plot a single column of the augmentation visualization."""
    # Row 1: Original images
    orig_tensor = torch.squeeze(plot_data.original_images[col_idx, 0, :, :]).detach()
    axs[0, col_idx].imshow(orig_tensor, vmin=-1, vmax=1, cmap="jet")
    orig_label = int(plot_data.original_labels[col_idx].item())
    axs[0, col_idx].set_title(f"Original\nLabel: {orig_label}", fontsize=10)
    axs[0, col_idx].set_xticks([])
    axs[0, col_idx].set_yticks([])

    # Row 2: Augmented images
    aug_tensor = torch.squeeze(plot_data.augmented_images[col_idx, 0, :, :]).detach()
    axs[1, col_idx].imshow(aug_tensor, vmin=-1, vmax=1, cmap="jet")
    true_label = int(plot_data.y_true[col_idx].item())
    pred_label = int(plot_data.y_pred[col_idx].item() > 0.5)
    confidence = torch.sigmoid(plot_data.y_pred[col_idx]).item()
    axs[1, col_idx].set_title(f"Augmented\nT:{true_label}, P:{pred_label}", fontsize=10)
    axs[1, col_idx].set_xticks([])
    axs[1, col_idx].set_yticks([])

    # Row 3: Difference visualization (augmented - original)
    diff_tensor = aug_tensor - orig_tensor
    im_diff = axs[2, col_idx].imshow(diff_tensor, cmap="RdBu", vmin=-1, vmax=1)
    axs[2, col_idx].set_title("Difference\n(Aug - Orig)", fontsize=10)
    axs[2, col_idx].set_xticks([])
    axs[2, col_idx].set_yticks([])

    # Row 4: Prediction confidence visualization
    conf_color = plt.cm.get_cmap("RdYlGn")(confidence)
    axs[3, col_idx].imshow(aug_tensor, vmin=-1, vmax=1, cmap="gray", alpha=0.7)
    axs[3, col_idx].add_patch(
        plt.Rectangle(
            (0, 0),
            aug_tensor.shape[1],
            aug_tensor.shape[0],
            facecolor=conf_color,
            alpha=0.3,
        )
    )
    axs[3, col_idx].set_title(f"Confidence\n{confidence:.3f}", fontsize=10)
    axs[3, col_idx].set_xticks([])
    axs[3, col_idx].set_yticks([])

    return im_diff


def plot_gaussian_blurs_with_augmentations(
    plot_data: AugmentationPlotData, *, output_path: Optional[str] = None
):
    """
    Enhanced version that displays both original and augmented images side by side.

    Shows a 4-row grid:
    - Row 1: Original images
    - Row 2: Augmented images (used for training/testing)
    - Row 3: Difference visualization (augmented - original)
    - Row 4: Prediction confidence visualization

    Args:
        plot_data (AugmentationPlotData): Container with all required tensors.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
    """
    fig, axs = plt.subplots(4, 10, figsize=(20, 10))
    fig.suptitle(
        "Augmentation Effects: Original vs Augmented Data", fontsize=16, y=0.98
    )

    # Plot each column
    im_diff = None
    for i in range(10):
        im_diff = _plot_single_augmentation_column(plot_data, axs, i)

    # Add row labels
    row_labels = ["Original", "Augmented", "Difference", "Confidence"]
    for i, label in enumerate(row_labels):
        axs[i, 0].set_ylabel(label, fontsize=12, fontweight="bold")

    # Add a colorbar for the difference plot
    cbar_ax = fig.add_axes([0.92, 0.35, 0.02, 0.15])
    cbar = fig.colorbar(im_diff, cax=cbar_ax)
    cbar.set_label("Pixel Difference", fontsize=10)

    plt.tight_layout()
    plt.subplots_adjust(top=0.93, right=0.9)

    _save_or_show_plot(fig, output_path)


def _prepare_feature_maps_for_correlation(feature_maps_s):
    """
    Prepare feature maps for correlation calculation by handling zero variance.

    Args:
        feature_maps_s: Feature maps tensor for a single stimulus

    Returns:
        np.ndarray: Prepared feature maps array
    """
    feature_maps_np = feature_maps_s.cpu().numpy()
    number_maps = feature_maps_np.shape[0]

    # Handle edge case: check for zero variance (constant feature maps)
    # Add small noise to prevent correlation calculation issues
    for j in range(number_maps):
        if np.var(feature_maps_np[j]) == 0:
            feature_maps_np[j] += np.random.normal(0, 1e-8, feature_maps_np[j].shape)

    return feature_maps_np


def _compute_correlation_matrix(feature_maps_np, number_maps, stimulus_idx):
    """
    Compute correlation matrix with error handling.

    Args:
        feature_maps_np: Prepared feature maps array
        number_maps: Number of feature maps
        stimulus_idx: Index of current stimulus for error reporting

    Returns:
        np.ndarray: Correlation matrix
    """
    try:
        correlation_matrix = np.corrcoef(feature_maps_np)
        # Handle NaN values that might still occur
        correlation_matrix = np.nan_to_num(
            correlation_matrix, nan=0.0, posinf=1.0, neginf=-1.0
        )
    except Exception as e:
        print(
            f"Warning: Error calculating correlations for stimulus {stimulus_idx}: {e}"
        )
        # Create identity matrix as fallback
        correlation_matrix = np.eye(number_maps)

    return correlation_matrix


def _extract_upper_triangle_values(
    correlation_matrix, number_maps, number_correlations
):
    """
    Extract upper triangle values from correlation matrix.

    Args:
        correlation_matrix: Correlation matrix
        number_maps: Number of feature maps
        number_correlations: Expected number of correlations

    Returns:
        np.ndarray: Upper triangle correlation values
    """
    idx = np.nonzero(np.triu(correlation_matrix, 1))

    # Handle edge case where correlation matrix might have unexpected shape
    if len(idx[0]) == number_correlations:
        return correlation_matrix[idx]

    # Fallback: extract upper triangle manually
    upper_triangle_values = []
    for row in range(number_maps):
        for col in range(row + 1, number_maps):
            upper_triangle_values.append(correlation_matrix[row, col])
    return upper_triangle_values[:number_correlations]


def _calculate_feature_map_correlations(feature_maps):
    """
    Calculate correlation data from feature maps with robust error handling.

    Args:
        feature_maps (torch.Tensor): A tensor of feature maps.

    Returns:
        tuple: A tuple containing:
            - allrs (np.ndarray): All correlation values.
            - call (np.ndarray): Sum of correlation matrices.
            - xlab (list): Labels for the x-axis.
            - number_stimulus (int): Number of stimuli (images).
            - number_correlations (int): Number of unique correlations.
    """
    number_stimulus = feature_maps.shape[0]
    number_maps = feature_maps.shape[1]

    # Handle edge case: need at least 2 feature maps for correlation
    if number_maps < 2:
        raise ValueError(
            f"Need at least 2 feature maps for correlation analysis, got {number_maps}"
        )

    number_correlations = (number_maps * (number_maps - 1)) // 2
    allrs = np.zeros((number_stimulus, number_correlations))
    call = np.zeros((number_maps, number_maps))

    for i in range(number_stimulus):
        feature_maps_s = feature_maps[i, :, :, :].view(number_maps, -1).detach()

        # Prepare feature maps for correlation calculation
        feature_maps_np = _prepare_feature_maps_for_correlation(feature_maps_s)

        # Compute correlation matrix
        correlation_matrix = _compute_correlation_matrix(
            feature_maps_np, number_maps, i
        )

        call += correlation_matrix

        # Extract upper triangle values
        allrs[i, :] = _extract_upper_triangle_values(
            correlation_matrix, number_maps, number_correlations
        )

    # Create labels for x-axis
    xlab = []
    for row in range(number_maps):
        for col in range(row + 1, number_maps):
            xlab.append(f"{row}-{col}")

    return allrs, call, xlab, number_stimulus, number_correlations


def _create_correlation_scatter_plot(ax0, allrs, xlab, num_stimulus, num_correlations):
    """
    Create the scatter plot showing correlations for each feature map pair.

    Args:
        ax0: Matplotlib axis for the scatter plot
        allrs: Array of correlation values
        xlab: Labels for x-axis
        num_stimulus: Number of stimuli
        num_correlations: Number of correlations
    """
    # Plot correlation values for each feature map pair
    for i in range(num_correlations):
        # Add small random jitter for better visualization
        jitter = np.random.randn(num_stimulus) / 30
        ax0.plot(
            i + jitter,
            allrs[:, i],
            "o",
            markerfacecolor="w",
            markersize=10,
        )

    # Make the plot more interpretable
    ax0.set_xlim([-0.5, num_correlations - 0.5])
    ax0.set_ylim([-1.05, 1.05])
    ax0.set_xticks(range(num_correlations))
    ax0.set_xticklabels(xlab, rotation=45 if num_correlations > 10 else 0)
    ax0.set_xlabel("Feature map pair")
    ax0.set_ylabel("Correlation coefficient")
    ax0.set_title("Correlations for each image")
    ax0.grid(True, alpha=0.3)


def _create_correlation_heatmap(ax1, cax, fig, call, num_stimulus):
    """
    Create the heatmap showing average correlation matrix.

    Args:
        ax1: Matplotlib axis for the heatmap
        cax: Matplotlib axis for the colorbar
        fig: Matplotlib figure
        call: Sum of correlation matrices
        num_stimulus: Number of stimuli

    Returns:
        float: Mean correlation coefficient
    """
    # Show the average correlation matrix
    avg_correlation_matrix = call / num_stimulus
    h = ax1.imshow(avg_correlation_matrix, vmin=-1, vmax=1, cmap="RdBu_r")
    ax1.set_title("Average correlation matrix")
    ax1.set_xlabel("Feature map")
    ax1.set_ylabel("Feature map")

    # Add colorbar
    fig.colorbar(h, cax=cax, label="Correlation coefficient")

    # Calculate mean correlation
    mean_corr = np.mean(
        avg_correlation_matrix[np.triu_indices_from(avg_correlation_matrix, k=1)]
    )

    return mean_corr


def plot_correlation_values(feature_maps, output_path=None):
    """
    Visualizes the correlation coefficients between feature maps for each image
    and the average correlation matrix across all images.

    This function handles edge cases like constant feature maps and provides robust
    error handling to prevent visualization failures.

    Args:
        feature_maps (torch.Tensor): A tensor of feature maps, typically from a convolutional layer,
                                     with shape (num_stimulus, num_maps, height, width).
                                     Requires at least 2 feature maps for correlation analysis.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.

    Raises:
        ValueError: If feature_maps has fewer than 2 feature maps.
    """
    try:
        # Calculate correlation data
        correlation_data = _calculate_feature_map_correlations(feature_maps)
        allrs, call, xlab, num_stimulus, num_correlations = correlation_data

        # Create the visualization
        fig = plt.figure(figsize=(14, 5))
        ax0 = fig.add_axes([0.1, 0.1, 0.55, 0.9])  # [left, bottom, width, height]
        ax1 = fig.add_axes([0.68, 0.1, 0.3, 0.9])
        cax = fig.add_axes([0.99, 0.1, 0.01, 0.9])

        # Create scatter plot
        _create_correlation_scatter_plot(
            ax0, allrs, xlab, num_stimulus, num_correlations
        )

        # Create heatmap
        mean_corr = _create_correlation_heatmap(ax1, cax, fig, call, num_stimulus)

        # Add title with summary
        fig.suptitle(f"Feature Map Correlations (Mean: {mean_corr:.3f})", y=0.95)

        if output_path:
            plt.savefig(output_path, bbox_inches="tight", dpi=150)
            plt.close(fig)
        else:
            plt.show()

    except ValueError:
        # Re-raise ValueError for proper error handling (e.g., too few feature maps)
        raise
    except Exception as e:
        print(f"Error creating correlation plot: {e}")
        if output_path:
            # Create a simple error plot
            fig, ax = plt.subplots(figsize=(8, 6))
            ax.text(
                0.5,
                0.5,
                f"Error creating correlation plot:\n{str(e)}",
                ha="center",
                va="center",
                transform=ax.transAxes,
                bbox={"boxstyle": "round", "facecolor": "lightcoral", "alpha": 0.8},
            )
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis("off")
            plt.savefig(output_path, bbox_inches="tight", dpi=150)
            plt.close(fig)
        else:
            raise


def plot_feature_maps(feature_maps, images, true_labels, output_path=None):
    """
    Displays original images and their corresponding feature map activations from a model layer.

    This function handles edge cases like uniform feature maps (where min == max) that could
    cause matplotlib normalization errors.

    Args:
        feature_maps (torch.Tensor): Feature maps from a model layer,
                                     shape (batch_size, num_feature_maps, height, width).
        images (torch.Tensor): Original input images, shape (batch_size, channels, height, width).
        true_labels (torch.Tensor): True labels for the images.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
    """
    # Draw the feature maps
    fig, axs = plt.subplots(feature_maps.shape[1] + 1, 10, figsize=(12, 6))

    for pici in range(10):
        # Show the original picture
        img = images[pici, 0, :, :].detach()
        axs[0, pici].imshow(img, cmap="jet", vmin=0, vmax=1)
        axs[0, pici].axis("off")
        axs[0, pici].text(
            2,
            2,
            f"T:{int(true_labels[pici].item())}",
            ha="left",
            va="top",
            color="w",
            fontweight="bold",
        )

        for feati in range(feature_maps.shape[1]):
            # Extract the feature map from this image
            img = feature_maps[pici, feati, :, :].detach()

            # Handle edge cases for normalization to prevent matplotlib errors
            img_min = torch.min(img).item()
            img_max = torch.max(img).item()

            if img_min == img_max:
                # All values are the same, use a small range around the value
                if img_min == 0:
                    vmin, vmax = 0, 1
                else:
                    vmin, vmax = img_min - 0.1, img_min + 0.1
            else:
                # Normal case
                vmin, vmax = img_min, img_max * 0.9 if img_max > 0 else img_max

            axs[feati + 1, pici].imshow(img, cmap="inferno", vmin=vmin, vmax=vmax)
            axs[feati + 1, pici].axis("off")
            if pici == 0:
                axs[feati + 1, pici].text(
                    -5, feature_maps.shape[2] / 2, feati, ha="right"
                )

    plt.tight_layout()
    plt.suptitle("Set of feature map activations for 10 test images", x=0.5, y=1.01)

    if output_path:
        plt.savefig(output_path)
        plt.close(fig)
    else:
        plt.show()


def plot_resource_usage(resources_dict, output_path=None, model_run_uuid=None):
    """
    Plots resource usage metrics over time.

    Args:
        resources_dict (dict): Dictionary containing resource metrics with lists of values.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
        model_run_uuid (str, optional): Model run UUID for centralized path generation.
    """
    # Determine which metrics to plot based on what's available
    metrics_to_plot = []
    if "cpu_percent" in resources_dict:
        metrics_to_plot.append(("cpu_percent", "CPU Usage (%)"))
    if "memory_percent" in resources_dict:
        metrics_to_plot.append(("memory_percent", "Memory Usage (%)"))
    if "gpu_memory_used" in resources_dict:
        metrics_to_plot.append(("gpu_memory_used", "GPU Memory (GB)"))

    if not metrics_to_plot:
        return  # No metrics to plot

    n_plots = len(metrics_to_plot)
    fig, axs = plt.subplots(n_plots, 1, figsize=(12, 4 * n_plots))

    # Handle case with only one subplot
    if n_plots == 1:
        axs = [axs]

    for i, (metric_key, metric_label) in enumerate(metrics_to_plot):
        if metric_key in resources_dict and resources_dict[metric_key]:
            values = resources_dict[metric_key]
            axs[i].plot(values, "o-")
            axs[i].set_title(f"{metric_label} over Training")
            axs[i].set_xlabel("Measurement Index")
            axs[i].set_ylabel(metric_label)

            # Add some stats
            if values:
                axs[i].axhline(
                    max(values),
                    color="r",
                    linestyle="--",
                    alpha=0.5,
                    label=f"Max: {max(values):.2f}",
                )
                axs[i].axhline(
                    np.mean(values),
                    color="g",
                    linestyle="--",
                    alpha=0.5,
                    label=f"Mean: {np.mean(values):.2f}",
                )
                axs[i].legend()

    plt.tight_layout()

    final_output_path = get_plot_path("resources.png", output_path, model_run_uuid)
    if final_output_path:
        plt.savefig(final_output_path)
        plt.close(fig)
    else:
        plt.show()


def plot_timing_metrics(timing_dict, output_path=None, model_run_uuid=None):
    """
    Plots timing metrics for different phases of training.

    Args:
        timing_dict (dict): Dictionary containing timing metrics with lists of values.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
        model_run_uuid (str, optional): Model run UUID for centralized path generation.
    """
    # Determine which metrics to plot based on what's available
    metrics_to_plot = []
    if "train_times" in timing_dict:
        metrics_to_plot.append(("train_times", "Training Time (s)"))
    if "validation_times" in timing_dict:
        metrics_to_plot.append(("validation_times", "Validation Time (s)"))

    if not metrics_to_plot:
        return  # No metrics to plot

    fig, ax = plt.subplots(figsize=(12, 6))

    for metric_key, metric_label in metrics_to_plot:
        if metric_key in timing_dict and timing_dict[metric_key]:
            values = timing_dict[metric_key]
            epochs = range(1, len(values) + 1)
            ax.plot(epochs, values, "o-", label=metric_label)

    ax.set_title("Training and Validation Times per Epoch")
    ax.set_xlabel("Epoch")
    ax.set_ylabel("Time (seconds)")
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()

    final_output_path = get_plot_path("timing.png", output_path, model_run_uuid)
    if final_output_path:
        plt.savefig(final_output_path)
        plt.close(fig)
    else:
        plt.show()


# Plot classification metrics
def plot_classification_metrics(
    *, precision, recall, f1_score, output_path=None, model_run_uuid=None
):
    """Plot classification metrics (precision, recall, F1 score)."""
    # Ensure clean state before creating new figure
    _ensure_figure_cleanup()

    fig = plt.figure(figsize=(10, 6))

    epochs = range(1, len(precision) + 1)

    plt.plot(epochs, precision, "o-", label="Precision")
    plt.plot(epochs, recall, "s-", label="Recall")
    plt.plot(epochs, f1_score, "^-", label="F1 Score")

    plt.title("Classification Metrics")
    plt.xlabel("Epoch")
    plt.ylabel("Score")
    plt.legend()
    plt.grid(True, linestyle="--", alpha=0.7)

    # Set y-axis limits for better visualization
    plt.ylim(0.5, 1.05)

    final_output_path = get_plot_path(
        "classification_metrics.png", output_path, model_run_uuid
    )
    if final_output_path:
        plt.savefig(final_output_path)
        plt.close(fig)
        _ensure_figure_cleanup()  # Extra cleanup after saving
    else:
        plt.show()
