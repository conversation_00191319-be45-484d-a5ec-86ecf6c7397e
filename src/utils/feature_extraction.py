# src/utils/feature_extraction.py
"""
Feature extraction utilities for CNN models.

This module provides utilities to detect if a model supports feature extraction
and extract feature maps from convolutional layers.
"""

import logging
from typing import Optional, Tuple

import torch
from torch import nn

from .model_utils import has_conv2d_layers

logger = logging.getLogger(__name__)


def can_extract_feature_maps(model: nn.Module) -> bool:
    """
    Check if a model supports feature map extraction.

    Args:
        model: PyTorch model to check

    Returns:
        bool: True if the model has convolutional layers that support feature extraction
    """
    return has_conv2d_layers(model)


def find_first_conv_layer(model: nn.Module) -> Optional[nn.Conv2d]:
    """
    Find the first convolutional layer in a model.

    Args:
        model: PyTorch model to search

    Returns:
        Optional[nn.Conv2d]: First Conv2d layer found, or None if no Conv2d layers exist
    """
    try:
        for module in model.modules():
            if isinstance(module, nn.Conv2d):
                return module
        return None
    except Exception as e:
        logger.warning("Error finding first convolutional layer: %s", str(e))
        return None


def extract_feature_maps(
    model: nn.Module, inputs: torch.Tensor, layer_name: Optional[str] = None
) -> Optional[torch.Tensor]:
    """
    Extract feature maps from a model's convolutional layer.

    Args:
        model: PyTorch model
        inputs: Input tensor to pass through the model
        layer_name: Optional specific layer name to extract from.
                   If None, extracts from first Conv2d layer.

    Returns:
        Optional[torch.Tensor]: Feature maps tensor or None if extraction failed
    """
    if not can_extract_feature_maps(model):
        logger.warning("Model does not support feature map extraction")
        return None

    feature_maps = None
    hook_handle = None

    def hook_fn(module, input_tensor, output):  # pylint: disable=unused-argument
        nonlocal feature_maps
        feature_maps = output.clone()
        logger.debug("Feature maps extracted with shape: %s", output.shape)

    try:
        # Find target layer
        target_layer = None
        if layer_name:
            # Look for specific named layer
            for name, module in model.named_modules():
                if name == layer_name and isinstance(module, nn.Conv2d):
                    target_layer = module
                    break
        else:
            # Use first Conv2d layer
            target_layer = find_first_conv_layer(model)

        if target_layer is None:
            logger.warning(
                "No suitable convolutional layer found for feature extraction"
            )
            return None

        # Register hook
        hook_handle = target_layer.register_forward_hook(hook_fn)

        # Set model to evaluation mode
        model.eval()

        # Forward pass to trigger hook
        with torch.no_grad():
            _ = model(inputs)

        return feature_maps

    except Exception as e:
        logger.error("Error during feature map extraction: %s", str(e))
        return None
    finally:
        # Clean up hook
        if hook_handle is not None:
            hook_handle.remove()


def get_model_conv_info(model: nn.Module) -> Tuple[int, list]:
    """
    Get information about convolutional layers in a model.

    Args:
        model: PyTorch model to analyze

    Returns:
        Tuple[int, list]: Number of Conv2d layers and list of layer names
    """
    conv_count = 0
    conv_layers = []

    try:
        for name, module in model.named_modules():
            if isinstance(module, nn.Conv2d):
                conv_count += 1
                conv_layers.append(name)
    except Exception as e:
        logger.warning("Error analyzing model convolutional layers: %s", str(e))

    return conv_count, conv_layers
