"""
Utility functions for image processing and handling.
"""

from pathlib import Path
from typing import List, Optional
from urllib.parse import urlparse

from config.paths import get_background_dir_name

# Centralized list of supported image extensions
SUPPORTED_IMAGE_EXTENSIONS = [
    "jpg",
    "jpeg",
    "png",
    "webp",
    "heic",
    "heif",
    "hevc",
    "gif",
]


def get_image_extension_from_url(image_url: str, fallback: str = "jpg") -> str:
    """
    Extract file extension from image URL.

    Args:
        image_url: URL of the image
        fallback: Fallback extension if extraction fails

    Returns:
        File extension (e.g., 'jpg', 'png', 'webp') or fallback
    """
    if not image_url:
        return fallback

    try:
        # Parse the URL and get the path
        parsed_url = urlparse(image_url)
        path = parsed_url.path

        # Extract extension from the path
        if path and "." in path:
            extension = path.split(".")[-1].lower()
            # Validate that it's a reasonable image extension
            if extension in SUPPORTED_IMAGE_EXTENSIONS:
                return extension
    except Exception:
        # If parsing fails, fall back to default
        pass

    # Default fallback
    return fallback


def build_image_path(
    base_dir: Path,
    image_uuid: str,
    coin_side_uuid: Optional[str] = None,
    image_url: Optional[str] = None,
    background_class_name: Optional[str] = None,
) -> Path:
    """
    Build image file path using standardized directory structure.

    Args:
        base_dir: Base directory for images
        image_uuid: UUID of the image
        coin_side_uuid: UUID of the coin side (None for background images)
        image_url: Optional image URL for extension detection
        background_class_name: Custom background directory name (defaults to standard name)

    Returns:
        Path to the image file

    Raises:
        ValueError: If image_url is None or cannot extract extension
    """
    # Determine directory structure
    if coin_side_uuid:
        # Labeled image: base_dir / coin_side_uuid / image_uuid.ext
        image_dir = base_dir / str(coin_side_uuid)
    else:
        # Background image: base_dir / background_class_name / image_uuid.ext
        bg_dir_name = background_class_name or get_background_dir_name()
        image_dir = base_dir / bg_dir_name

    # Determine file extension from URL
    if not image_url:
        raise ValueError(
            f"Cannot build image path for {image_uuid}: image_url is required"
        )

    extension = get_image_extension_from_url(image_url, fallback="jpg")

    # Build full path
    filename = f"{image_uuid}.{extension}"
    return image_dir / filename


def find_image_file_with_extensions(
    base_dir: Path,
    image_uuid: str,
    coin_side_uuid: Optional[str] = None,
    supported_extensions: Optional[List[str]] = None,
    background_class_name: Optional[str] = None,
) -> Optional[Path]:
    """
    Find image file with any supported extension using smart detection.

    Args:
        base_dir: Base directory to search in
        image_uuid: UUID of the image to find
        coin_side_uuid: UUID of the coin side (None for background images)
        supported_extensions: List of extensions to try (defaults to all supported)
        background_class_name: Custom background directory name (defaults to standard name)

    Returns:
        Path to the found image file, or None if not found
    """
    if supported_extensions is None:
        supported_extensions = SUPPORTED_IMAGE_EXTENSIONS

    # Determine directory
    if coin_side_uuid:
        search_dir = base_dir / str(coin_side_uuid)
    else:
        bg_dir_name = background_class_name or get_background_dir_name()
        search_dir = base_dir / bg_dir_name

    if not search_dir.exists():
        return None

    # Try all supported extensions
    for ext in supported_extensions:
        image_path = search_dir / f"{image_uuid}.{ext}"
        if image_path.exists():
            return image_path

    return None
