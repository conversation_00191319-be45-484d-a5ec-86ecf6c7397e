"""
Utility functions for async operations.
"""

import asyncio
from typing import Awaitable, TypeVar, Union, cast

T = TypeVar("T")


async def maybe_await(obj: Union[Awaitable[T], T]) -> T:
    """
    Await the given object if it's awaitable, otherwise return it directly.

    This is more efficient and idiomatic than using inspect.isawaitable as it
    specifically handles coroutines which is what we need in async functions.

    Args:
        obj: An object that might be awaitable or a direct value

    Returns:
        The awaited result if awaitable, otherwise the original object
    """
    if asyncio.iscoroutine(obj):
        return await obj
    return cast(T, obj)
