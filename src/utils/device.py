"""
This module provides utility functions for device selection in PyTorch.
"""

# pylint: disable=no-member
import torch


def select_device():
    """Selects the best available device for PyTorch operations."""
    if torch.cuda.is_available():
        device = torch.device("cuda")  # NVIDIA GPU
    elif torch.backends.mps.is_available():
        device = torch.device("mps")  # Apple Silicon
    elif torch.backends.rocm.is_available():
        device = torch.device("rocm")  # AMD GPU
    else:
        device = torch.device("cpu")  # CPU
    return device
