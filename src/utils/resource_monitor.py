"""
Enhanced resource monitoring for modern multi-core systems.

This module provides sophisticated resource checking that's more suitable
for modern systems like Apple M2, with multiple cores and advanced GPU architectures.
"""

import os
import platform
from dataclasses import dataclass
from typing import Dict, Optional, Tuple

import psutil
import torch


@dataclass
class SystemInfo:
    """System information for resource monitoring."""

    cpu_count_logical: int
    cpu_count_physical: int
    total_memory_gb: float
    platform_name: str
    has_mps: bool
    has_cuda: bool
    has_load_avg: bool


@dataclass
class ResourceStatus:
    """Current resource status."""

    cpu_percent: float
    load_avg_5min: Optional[float]
    available_memory_gb: float
    memory_percent: float
    gpu_available: bool
    gpu_memory_percent: Optional[float]
    can_schedule: bool
    reason: Optional[str] = None


@dataclass
class SchedulingParams:
    """Parameters for job scheduling decisions."""

    cpu_percent: float
    load_avg_5min: Optional[float]
    available_memory_gb: float
    gpu_available: bool
    gpu_memory_percent: Optional[float]
    job_memory_gb: float
    job_gpu_required: bool


class EnhancedResourceMonitor:
    """
    Enhanced resource monitoring for modern multi-core systems.

    This class provides more sophisticated resource checking than the basic
    psutil approach, taking into account:
    - Load averages for sustained workload assessment
    - Multi-core CPU scaling
    - Available memory vs percentage
    - Apple Silicon MPS GPU support
    - Job-specific resource requirements
    """

    def __init__(self):
        """Initialize the resource monitor."""
        self.system_info = self._get_system_info()

    def _get_system_info(self) -> SystemInfo:
        """Get system information once at initialization."""
        return SystemInfo(
            cpu_count_logical=psutil.cpu_count(logical=True),
            cpu_count_physical=psutil.cpu_count(logical=False),
            total_memory_gb=psutil.virtual_memory().total / (1024**3),
            platform_name=platform.system(),
            has_mps=torch.backends.mps.is_available(),
            has_cuda=torch.cuda.is_available(),
            has_load_avg=hasattr(os, "getloadavg"),
        )

    def get_resource_status(
        self,
        job_memory_gb: float = 2.0,
        job_gpu_required: bool = False,
    ) -> ResourceStatus:
        """
        Get current resource status and determine if a job can be scheduled.

        Args:
            job_memory_gb: Amount of memory the job needs in GB
            job_gpu_required: Whether the job requires GPU

        Returns:
            ResourceStatus with current status and scheduling decision
        """
        try:
            # Get current resource usage
            cpu_percent = psutil.cpu_percent(interval=1.0)  # 1 second for accuracy
            memory = psutil.virtual_memory()
            available_memory_gb = memory.available / (1024**3)

            # Get load average if available (Unix-like systems)
            load_avg_5min = None
            if self.system_info.has_load_avg:
                _, load_avg_5min, _ = os.getloadavg()

            # Check GPU availability
            gpu_available = False
            gpu_memory_percent = None

            if job_gpu_required:
                if self.system_info.has_mps:
                    gpu_available = self._check_mps_availability()
                elif self.system_info.has_cuda:
                    gpu_available, gpu_memory_percent = self._check_cuda_availability()
            else:
                gpu_available = True  # Not required, so considered available

            # Determine if we can schedule the job
            scheduling_params = SchedulingParams(
                cpu_percent=cpu_percent,
                load_avg_5min=load_avg_5min,
                available_memory_gb=available_memory_gb,
                gpu_available=gpu_available,
                gpu_memory_percent=gpu_memory_percent,
                job_memory_gb=job_memory_gb,
                job_gpu_required=job_gpu_required,
            )
            can_schedule, reason = self._can_schedule_job(scheduling_params)

            return ResourceStatus(
                cpu_percent=cpu_percent,
                load_avg_5min=load_avg_5min,
                available_memory_gb=available_memory_gb,
                memory_percent=memory.percent,
                gpu_available=gpu_available,
                gpu_memory_percent=gpu_memory_percent,
                can_schedule=can_schedule,
                reason=reason,
            )

        except Exception as e:
            # Fail-open: if we can't check resources, allow scheduling
            return ResourceStatus(
                cpu_percent=0.0,
                load_avg_5min=None,
                available_memory_gb=float("inf"),
                memory_percent=0.0,
                gpu_available=True,
                gpu_memory_percent=None,
                can_schedule=True,
                reason=f"Resource check failed: {e}",
            )

    def _check_mps_availability(self) -> bool:
        """Check if MPS (Apple Silicon GPU) is available."""
        try:
            # Try to allocate a small tensor to test MPS availability
            test_tensor = torch.randn(10, 10, device="mps")
            del test_tensor
            torch.mps.empty_cache()
            return True
        except Exception:
            return False

    def _check_cuda_availability(self) -> Tuple[bool, Optional[float]]:
        """Check CUDA GPU availability and memory usage."""
        try:
            if not torch.cuda.is_available():
                return False, None

            gpu_memory_allocated = torch.cuda.memory_allocated() / (1024**3)
            gpu_memory_reserved = torch.cuda.memory_reserved() / (1024**3)

            if gpu_memory_reserved > 0:
                gpu_memory_percent = (gpu_memory_allocated / gpu_memory_reserved) * 100
                # Allow scheduling if GPU memory usage is below 80%
                return gpu_memory_percent < 80.0, gpu_memory_percent

            return True, 0.0

        except Exception:
            return False, None

    def _can_schedule_job(self, params: SchedulingParams) -> Tuple[bool, Optional[str]]:
        """
        Determine if a job can be scheduled based on current resources.

        Args:
            params: SchedulingParams containing all resource and job requirements

        Returns:
            Tuple of (can_schedule, reason_if_not)
        """
        # Check CPU load using load average (more reliable for sustained workload)
        if params.load_avg_5min is not None:
            # For development environments, be very permissive with load average
            # Modern multi-core systems can handle high loads efficiently
            # Use 300% of logical CPU count as threshold (allows significant oversubscription)
            max_load = self.system_info.cpu_count_logical * 3.0
            if params.load_avg_5min > max_load:
                return (
                    False,
                    f"Load average too high: {params.load_avg_5min:.2f} > {max_load:.2f}",
                )

        # Check CPU percentage with scaling for multi-core systems
        # For development environments, be very permissive with CPU usage
        # Modern systems can handle high CPU usage while still being responsive
        cpu_threshold = min(95.0, 80.0 + (self.system_info.cpu_count_logical * 1.5))
        if params.cpu_percent > cpu_threshold:
            return (
                False,
                f"CPU usage too high: {params.cpu_percent:.1f}% > {cpu_threshold:.1f}%",
            )

        # Check memory availability (absolute amount more important than percentage)
        # For development environments, be more permissive with memory usage
        # Use a smaller buffer and allow using more of available memory
        memory_buffer = max(
            0.5, params.job_memory_gb * 0.2
        )  # 20% buffer or 0.5GB minimum
        memory_needed = params.job_memory_gb + memory_buffer

        # Only check if we have at least 1GB available (very conservative)
        min_memory_needed = max(1.0, memory_needed * 0.5)
        if params.available_memory_gb < min_memory_needed:
            return (
                False,
                f"Insufficient memory: {params.available_memory_gb:.1f}GB available, "
                f"need at least {min_memory_needed:.1f}GB",
            )

        # Check GPU if required
        if params.job_gpu_required and not params.gpu_available:
            return False, "GPU required but not available"

        if params.gpu_memory_percent is not None and params.gpu_memory_percent > 80.0:
            return False, f"GPU memory usage too high: {params.gpu_memory_percent:.1f}%"

        return True, None

    def get_system_summary(self) -> Dict[str, any]:
        """Get a summary of system capabilities."""
        return {
            "cpu_cores_logical": self.system_info.cpu_count_logical,
            "cpu_cores_physical": self.system_info.cpu_count_physical,
            "total_memory_gb": self.system_info.total_memory_gb,
            "platform": self.system_info.platform_name,
            "gpu_mps_available": self.system_info.has_mps,
            "gpu_cuda_available": self.system_info.has_cuda,
            "load_avg_supported": self.system_info.has_load_avg,
        }
