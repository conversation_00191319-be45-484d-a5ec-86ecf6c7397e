"""
Utility functions for working with PyTorch models.
"""

import logging
from typing import Type

from torch import nn

logger = logging.getLogger(__name__)


def has_layer_type(model: nn.Module, layer_type: Type[nn.Module]) -> bool:
    """
    Check if a model contains layers of a specific type.

    Args:
        model: PyTorch model to check
        layer_type: Type of layer to look for (e.g., nn.Conv2d, nn.Linear)

    Returns:
        bool: True if model has layers of the specified type
    """
    try:
        for module in model.modules():
            if isinstance(module, layer_type):
                return True
        return False
    except Exception as e:
        logger.warning(
            "Error checking model for %s layers: %s", layer_type.__name__, str(e)
        )
        return False


def has_conv2d_layers(model: nn.Module) -> bool:
    """
    Check if a model has Conv2d layers.

    Args:
        model: PyTorch model to check

    Returns:
        bool: True if model has Conv2d layers
    """
    return has_layer_type(model, nn.Conv2d)


def has_linear_layers(model: nn.Module) -> bool:
    """
    Check if a model has Linear layers.

    Args:
        model: PyTorch model to check

    Returns:
        bool: True if model has Linear layers
    """
    return has_layer_type(model, nn.Linear)
