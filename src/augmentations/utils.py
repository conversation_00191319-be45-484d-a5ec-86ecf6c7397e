"""
Augmentation utilities and helper functions.

This module provides utility functions for working with augmentations,
including conversion between formats and common operations.
"""

from typing import Any, Dict, List

from .schemas import (
    AugmentationItemType,
    HorizontalFlip,
    NormalizationMean,
    NormalizationStd,
    ResizeDimensions,
    RotationRange,
    ZoomRange,
)


class AugmentationUtils:
    """
    Utility functions for augmentation operations.

    Provides conversion, filtering, and manipulation utilities
    for augmentation configurations.
    """

    # Mapping from dictionary keys to augmentation classes
    AUGMENTATION_TYPE_MAP = {
        "horizontal_flip": HorizontalFlip,
        "rotation_range": RotationRange,
        "zoom_range": ZoomRange,
        "resize_dimensions": ResizeDimensions,
        "normalization_mean": NormalizationMean,
        "normalization_std": NormalizationStd,
    }

    @classmethod
    def from_dict_list(
        cls, aug_dicts: List[Dict[str, Any]]
    ) -> List[AugmentationItemType]:
        """
        Convert a list of dictionaries to augmentation objects.

        This is useful for converting database records or JSON configurations
        to typed augmentation objects.

        Args:
            aug_dicts: List of dictionaries containing augmentation parameters

        Returns:
            List of typed augmentation objects

        Raises:
            ValueError: If an unknown augmentation type is encountered
        """
        augmentations = []

        for aug_dict in aug_dicts:
            # Find the augmentation type based on the keys in the dictionary
            aug_type = None
            aug_key = None

            for key in aug_dict.keys():
                if key in cls.AUGMENTATION_TYPE_MAP:
                    aug_type = cls.AUGMENTATION_TYPE_MAP[key]
                    aug_key = key
                    break

            if aug_type is None:
                raise ValueError(f"Unknown augmentation type in dictionary: {aug_dict}")

            # Create the augmentation object
            augmentation = aug_type(**{aug_key: aug_dict[aug_key]})
            augmentations.append(augmentation)

        return augmentations

    @classmethod
    def count_augmentation_types(
        cls, augmentations: List[AugmentationItemType]
    ) -> Dict[str, int]:
        """
        Count augmentations by type.

        Args:
            augmentations: List of augmentation objects

        Returns:
            Dictionary mapping augmentation type names to counts
        """
        type_counts = {}
        for aug in augmentations:
            aug_type = type(aug).__name__
            type_counts[aug_type] = type_counts.get(aug_type, 0) + 1
        return type_counts
