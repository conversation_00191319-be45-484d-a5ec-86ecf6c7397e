"""
Augmentation serialization utilities for inference compatibility.

This module provides functionality to save and load augmentation pipelines
for consistent preprocessing between training and inference.
"""

import json
import pickle
from pathlib import Path
from typing import Any, Dict, List, Union

from torchvision import transforms

from .schemas import AugmentationItemType


class AugmentationSerializer:
    """
    Utilities for serializing and deserializing augmentation pipelines.

    Provides both pickle-based serialization for PyTorch transforms and
    JSON-based serialization for augmentation configurations.
    """

    @classmethod
    def save_pipeline(
        cls, pipeline: transforms.Compose, path: Union[str, Path]
    ) -> None:
        """
        Save a PyTorch transform pipeline to disk.

        Args:
            pipeline: PyTorch Compose transform pipeline
            path: Path to save the serialized pipeline
        """
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)

        with open(path, "wb") as f:
            pickle.dump(pipeline, f)

    @classmethod
    def load_pipeline(cls, path: Union[str, Path]) -> transforms.Compose:
        """
        Load a PyTorch transform pipeline from disk.

        Args:
            path: Path to the serialized pipeline file

        Returns:
            PyTorch Compose transform pipeline

        Raises:
            FileNotFoundError: If the pipeline file doesn't exist
        """
        path = Path(path)

        if not path.exists():
            raise FileNotFoundError(f"Transform pipeline not found at {path}")

        with open(path, "rb") as f:
            return pickle.load(f)

    @classmethod
    def save_augmentation_config(
        cls, augmentations: List[AugmentationItemType], path: Union[str, Path]
    ) -> None:
        """
        Save augmentation configuration as JSON for human readability.

        Args:
            augmentations: List of augmentation configurations
            path: Path to save the JSON configuration
        """
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)

        # Convert Pydantic models to dictionaries
        config_data = {
            "augmentations": [aug.model_dump() for aug in augmentations],
            "version": "1.0",
            "total_count": len(augmentations),
        }

        with open(path, "w", encoding="utf-8") as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

    @classmethod
    def load_augmentation_config(cls, path: Union[str, Path]) -> Dict[str, Any]:
        """
        Load augmentation configuration from JSON.

        Args:
            path: Path to the JSON configuration file

        Returns:
            Dictionary containing augmentation configuration

        Raises:
            FileNotFoundError: If the configuration file doesn't exist
        """
        path = Path(path)

        if not path.exists():
            raise FileNotFoundError(f"Augmentation config not found at {path}")

        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)

    @classmethod
    def load_inference_artifacts(cls, model_dir: Union[str, Path]) -> Dict[str, Any]:
        """
        Load inference artifacts for a trained model.

        Args:
            model_dir: Directory containing model artifacts

        Returns:
            Dictionary containing loaded artifacts

        Raises:
            FileNotFoundError: If required artifacts are missing
        """
        model_dir = Path(model_dir)

        artifacts = {}

        # Load inference pipeline (required)
        inference_path = model_dir / "inference_transforms.pkl"
        if inference_path.exists():
            artifacts["inference_pipeline"] = cls.load_pipeline(inference_path)
        else:
            raise FileNotFoundError(f"Inference pipeline not found at {inference_path}")

        # Load configuration (optional but recommended)
        config_path = model_dir / "augmentation_config.json"
        if config_path.exists():
            artifacts["config"] = cls.load_augmentation_config(config_path)

        # Load metadata (optional)
        metadata_path = model_dir / "augmentation_metadata.json"
        if metadata_path.exists():
            with open(metadata_path, "r", encoding="utf-8") as f:
                artifacts["metadata"] = json.load(f)

        return artifacts
