"""
Base classes and utilities for augmentations.

This module provides the base AugmentationItem class and common utilities
used across all augmentation implementations.
"""

from typing import Any, Optional

from pydantic import BaseModel


class AugmentationItem(BaseModel):
    """Base class for augmentation items."""

    def to_tensor(self) -> Optional[Any]:
        """
        Convert this augmentation to a tensor-based transform.

        Returns:
            A callable tensor transform, or None if not implemented.

        Note:
            Subclasses should override this method to provide tensor-based implementations.
            If not implemented, the augmentation will be skipped in tensor pipelines.
        """
        return None
