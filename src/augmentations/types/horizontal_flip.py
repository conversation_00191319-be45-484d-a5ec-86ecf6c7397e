"""
Horizontal flip augmentation implementation.

This module provides the HorizontalFlip schema and its tensor-based implementation
for applying horizontal flip transformations to image data.
"""

import torch

from .base import AugmentationItem


class TensorHorizontalFlip:
    """Horizontal flip for tensor data."""

    def __init__(self, p=0.5):
        self.p = p

    def __call__(self, img):
        if torch.rand(1) < self.p:
            return torch.flip(img, dims=[-1])  # Flip along width dimension
        return img


class HorizontalFlip(AugmentationItem):
    """Horizontal flip augmentation."""

    horizontal_flip: bool

    def to_tensor(self):
        """Create tensor-based horizontal flip transform."""
        if not self.horizontal_flip:
            return None
        return TensorHorizontalFlip(p=0.5)
