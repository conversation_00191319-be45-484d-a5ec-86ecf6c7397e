"""
Normalization augmentation implementation.

This module provides the NormalizationMean and NormalizationStd schemas and their
tensor-based implementations for applying normalization transformations to image data.
"""

from typing import List

import torch
from pydantic import field_validator

from .base import AugmentationItem


class TensorNormalize:
    """Normalization for tensor data."""

    def __init__(self, mean, std):
        self.mean = torch.tensor(mean, dtype=torch.float32)
        self.std = torch.tensor(std, dtype=torch.float32)

    def __call__(self, img):
        """
        Normalize a tensor image with mean and standard deviation.

        Args:
            img: Tensor of shape (C, H, W) to be normalized

        Returns:
            Normalized tensor
        """
        # Ensure mean and std have the right shape for broadcasting
        # img is (C, H, W), so we need mean and std to be (C, 1, 1)
        if self.mean.dim() == 1:
            mean = self.mean.view(-1, 1, 1)
        else:
            mean = self.mean

        if self.std.dim() == 1:
            std = self.std.view(-1, 1, 1)
        else:
            std = self.std

        # Move to same device as input tensor
        mean = mean.to(img.device)
        std = std.to(img.device)

        return (img - mean) / std


class NormalizationMean(AugmentationItem):
    """Normalization mean augmentation."""

    normalization_mean: List[float]

    @field_validator("normalization_mean")
    @classmethod
    def validate_mean(cls, v):
        """Validate that normalization_mean is a list of 3 numbers."""
        if not (
            isinstance(v, list)
            and len(v) == 3
            and all(isinstance(x, (int, float)) for x in v)
        ):
            raise ValueError("normalization_mean must be a list of 3 numbers")
        return v

    def to_tensor(self):
        """
        Create tensor-based normalization transform.

        Note: This requires both mean and std to be available. In practice,
        normalization should be handled by combining both NormalizationMean
        and NormalizationStd instances.
        """
        # Return None as normalization requires both mean and std
        # The factory handles combining these properly
        return None


class NormalizationStd(AugmentationItem):
    """Normalization standard deviation augmentation."""

    normalization_std: List[float]

    @field_validator("normalization_std")
    @classmethod
    def validate_std(cls, v):
        """Validate that normalization_std is a list of 3 numbers."""
        if not (
            isinstance(v, list)
            and len(v) == 3
            and all(isinstance(x, (int, float)) for x in v)
        ):
            raise ValueError("normalization_std must be a list of 3 numbers")
        return v

    def to_tensor(self):
        """
        Create tensor-based normalization transform.

        Note: This requires both mean and std to be available. In practice,
        normalization should be handled by combining both NormalizationMean
        and NormalizationStd instances.
        """
        # Return None as normalization requires both mean and std
        # The factory handles combining these properly
        return None
