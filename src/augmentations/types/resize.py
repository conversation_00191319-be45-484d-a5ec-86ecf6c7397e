"""
Resize augmentation implementation.

This module provides the ResizeDimensions schema and its tensor-based implementation
for applying resize transformations to image data.
"""

from typing import List

import torch.nn.functional as F
from pydantic import field_validator

from .base import AugmentationItem


class TensorResize:
    """Resize for tensor data."""

    def __init__(self, size):
        self.size = size if isinstance(size, (list, tuple)) else [size, size]

    def __call__(self, img):
        # img should be (C, H, W), add batch dimension for interpolate
        if img.dim() == 3:
            img_batch = img.unsqueeze(0)  # (1, C, H, W)
        else:
            img_batch = img

        # Resize using bilinear interpolation
        resized = F.interpolate(
            img_batch, size=self.size, mode="bilinear", align_corners=False
        )

        # Remove batch dimension if we added it
        if img.dim() == 3:
            resized = resized.squeeze(0)

        return resized


class ResizeDimensions(AugmentationItem):
    """Resize dimensions augmentation."""

    resize_dimensions: List[int]

    @field_validator("resize_dimensions")
    @classmethod
    def validate_dimensions(cls, v):
        """Validate that resize_dimensions is a list of 2 integers."""
        if not (
            isinstance(v, list) and len(v) == 2 and all(isinstance(x, int) for x in v)
        ):
            raise ValueError("resize_dimensions must be a list of 2 integers")
        return v

    def to_tensor(self):
        """Create tensor-based resize transform."""
        return TensorResize(size=self.resize_dimensions)
