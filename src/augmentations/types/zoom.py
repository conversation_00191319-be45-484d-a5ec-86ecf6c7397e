"""
Zoom augmentation implementation.

This module provides the ZoomRange schema and its tensor-based implementation
for applying zoom transformations to image data.
"""

import torch

from .base import AugmentationItem
from .rotation import apply_affine_transform


class TensorZoom:
    """Zoom for tensor data using affine transformation."""

    def __init__(self, zoom_range):
        self.zoom_range = zoom_range

    def __call__(self, img):
        """
        Apply random zoom to a tensor image.

        Args:
            img: Tensor of shape (C, H, W) to be zoomed

        Returns:
            Zoomed tensor
        """
        # Random zoom factor: 1.0 means no zoom, >1.0 means zoom in, <1.0 means zoom out
        # zoom_range of 0.1 means zoom factor will be in range [0.9, 1.1]
        zoom_factor = 1.0 + (torch.rand(1) * 2 - 1) * self.zoom_range

        # Create scaling matrix
        # For zoom in: scale > 1, for zoom out: scale < 1
        scale = (
            1.0 / zoom_factor
        )  # Invert because we're scaling the grid, not the image

        # Create affine transformation matrix for scaling around center
        # Format: [[scale, 0, 0], [0, scale, 0]]
        theta = torch.tensor(
            [[scale, 0, 0], [0, scale, 0]], dtype=torch.float32
        ).unsqueeze(
            0
        )  # Add batch dimension

        return apply_affine_transform(img, theta)


class ZoomRange(AugmentationItem):
    """Zoom range augmentation."""

    zoom_range: float

    def to_tensor(self):
        """Create tensor-based zoom transform."""
        if self.zoom_range <= 0:
            return None
        return TensorZoom(zoom_range=self.zoom_range)
