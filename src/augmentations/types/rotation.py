"""
Rotation augmentation implementation.

This module provides the RotationRange schema and its tensor-based implementation
for applying rotation transformations to image data.
"""

import torch
import torch.nn.functional as F

from .base import AugmentationItem


def apply_affine_transform(img: torch.Tensor, theta: torch.Tensor) -> torch.Tensor:
    """Apply affine transformation to an image tensor.

    Args:
        img: Input image tensor (C, H, W) or (B, C, H, W)
        theta: Affine transformation matrix (1, 2, 3) or (B, 2, 3)

    Returns:
        Transformed image tensor with same shape as input
    """
    # Add batch dimension to image if needed
    if img.dim() == 3:
        img_batch = img.unsqueeze(0)  # (1, C, H, W)
        remove_batch = True
    else:
        img_batch = img
        remove_batch = False

    # Create grid and apply affine transformation
    grid = F.affine_grid(theta, img_batch.size(), align_corners=False)
    transformed = F.grid_sample(
        img_batch, grid, align_corners=False, padding_mode="border"
    )

    # Remove batch dimension if it was added
    if remove_batch:
        transformed = transformed.squeeze(0)

    return transformed


class TensorRotation:
    """Rotation for tensor data using affine transformation."""

    def __init__(self, degrees):
        self.degrees = degrees

    def __call__(self, img):
        # Random rotation angle
        angle = (
            torch.rand(1) * 2 - 1
        ) * self.degrees  # Random angle in [-degrees, degrees]

        # Convert angle to radians
        angle_rad = angle * torch.pi / 180.0

        # Create rotation matrix
        cos_a = torch.cos(angle_rad)
        sin_a = torch.sin(angle_rad)

        # Create affine transformation matrix for rotation around center
        # Format: [[cos, -sin, 0], [sin, cos, 0]]
        theta = torch.tensor(
            [[cos_a, -sin_a, 0], [sin_a, cos_a, 0]], dtype=torch.float32
        ).unsqueeze(
            0
        )  # Add batch dimension

        return apply_affine_transform(img, theta)


class RotationRange(AugmentationItem):
    """Rotation range augmentation."""

    rotation_range: float

    def to_tensor(self):
        """Create tensor-based rotation transform."""
        if self.rotation_range <= 0:
            return None
        return TensorRotation(degrees=self.rotation_range)
