"""
Augmentation schemas and data models.

This module defines the data models for different types of augmentations
that can be applied during training and inference.

Note: This module now imports from individual augmentation modules organized
in the types/ subdirectory for better code organization. Each augmentation
type has its own dedicated module.
"""

from typing import Union

# Import base class and individual augmentation types from types subdirectory
from .types import (
    AugmentationItem,
    HorizontalFlip,
    NormalizationMean,
    NormalizationStd,
    ResizeDimensions,
    RotationRange,
    ZoomRange,
)

# Define the AugmentationItem type as a Union of all augmentation types
AugmentationItemType = Union[
    HorizontalFlip,
    RotationRange,
    ZoomRange,
    ResizeDimensions,
    NormalizationMean,
    NormalizationStd,
]

# Explicitly declare exports to avoid unused import warnings
__all__ = [
    "AugmentationItem",
    "AugmentationItemType",
    "HorizontalFlip",
    "NormalizationMean",
    "NormalizationStd",
    "ResizeDimensions",
    "RotationRange",
    "ZoomRange",
]
