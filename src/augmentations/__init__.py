"""
Augmentations module - First-class citizen for data augmentation and preprocessing.

This module provides comprehensive augmentation functionality for both training and inference,
ensuring consistency across the entire ML pipeline.

Key Components:
- schemas: Augmentation data models and type definitions
- factory: Pipeline factory for creating PyTorch transform pipelines
- serialization: Save/load functionality for inference compatibility
- validation: Augmentation configuration validation
- utils: Utility functions for augmentation operations
"""

from .factory import AugmentationPipelineFactory
from .schemas import (
    AugmentationItem,
    AugmentationItemType,
    HorizontalFlip,
    NormalizationMean,
    NormalizationStd,
    ResizeDimensions,
    RotationRange,
    ZoomRange,
)
from .serialization import AugmentationSerializer
from .utils import AugmentationUtils

__all__ = [
    # Factory
    "AugmentationPipelineFactory",
    # Schemas
    "AugmentationItem",
    "AugmentationItemType",
    "HorizontalFlip",
    "NormalizationMean",
    "NormalizationStd",
    "ResizeDimensions",
    "RotationRange",
    "ZoomRange",
    # Serialization
    "AugmentationSerializer",
    # Utils
    "AugmentationUtils",
]
