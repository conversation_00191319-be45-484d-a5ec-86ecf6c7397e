"""
Augmentation Pipeline Factory for converting augmentation parameters to PyTorch transforms.

This module provides the AugmentationPipelineFactory class that converts augmentation
configurations into PyTorch transform pipelines for training and inference.
"""

from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import numpy as np
import torch
from torchvision import transforms

from .schemas import (
    AugmentationItemType,
    HorizontalFlip,
    NormalizationMean,
    NormalizationStd,
    ResizeDimensions,
    RotationRange,
    ZoomRange,
)
from .serialization import AugmentationSerializer


class ValuePreservingToTensor:
    """
    Custom transform that converts data to tensors while preserving original value ranges.

    Unlike torchvision's ToTensor which normalizes to [0,1] by dividing by 255,
    this transform preserves the original data characteristics and intensity relationships.
    This is crucial for data like Gaussian blurs where the intensity differences
    between classes are the distinguishing features.
    """

    def __call__(self, img):
        """Convert data to tensor, preserving original values."""
        # If it's already a tensor, just ensure it's float32
        if isinstance(img, torch.Tensor):
            return img.float()

        # If it's a numpy array, convert to tensor without normalization
        if isinstance(img, np.ndarray):
            return torch.from_numpy(img).float()

        # If it's a PIL image, we need to be careful about normalization
        if hasattr(img, "mode"):
            # For PIL images, convert to numpy first to avoid automatic normalization
            img_array = np.array(img, dtype=np.float32)
            # Don't divide by 255 - preserve original values
            if len(img_array.shape) == 2:  # Grayscale
                tensor = torch.from_numpy(img_array).unsqueeze(
                    0
                )  # Add channel dimension
            else:  # RGB
                tensor = torch.from_numpy(img_array).permute(2, 0, 1)  # HWC -> CHW
            return tensor

        # Fallback - try to convert directly
        return torch.tensor(img, dtype=torch.float32)


class AugmentationPipelineFactory:
    """
    Factory for creating PyTorch transform pipelines from augmentation configurations.

    This class converts augmentation parameters into PyTorch transforms
    that can be applied during training and inference.
    """

    @classmethod
    def create_training_pipeline(
        cls, augmentations: Optional[List[AugmentationItemType]] = None
    ) -> transforms.Compose:
        """
        Create a training transform pipeline with augmentations.

        Args:
            augmentations: List of augmentation configurations

        Returns:
            PyTorch Compose transform pipeline for training
        """
        transform_list = []

        # Parse augmentations if provided
        if augmentations:
            # Extract normalization parameters first (needed for proper ordering)
            norm_mean, norm_std = cls.extract_normalization_params(augmentations)

            # Add geometric transforms
            geometric_transforms = cls._create_geometric_transforms(augmentations)
            transform_list.extend(geometric_transforms)

            # Convert to tensor while preserving original value ranges
            # This is crucial for data like Gaussian blurs where
            # intensity differences are the signal
            transform_list.append(ValuePreservingToTensor())

            # Add normalization if specified
            if norm_mean is not None and norm_std is not None:
                transform_list.append(
                    transforms.Normalize(mean=norm_mean, std=norm_std)
                )
        else:
            # Default pipeline: convert to tensor while preserving original values
            # Use ValuePreservingToTensor since it handles both PIL images and tensors
            transform_list.append(ValuePreservingToTensor())

        return transforms.Compose(transform_list)

    @classmethod
    def create_inference_pipeline(
        cls, augmentations: Optional[List[AugmentationItemType]] = None
    ) -> transforms.Compose:
        """
        Create an inference transform pipeline (deterministic transforms only).

        For inference, we only apply deterministic transforms like resizing and normalization,
        but skip random augmentations like rotation and horizontal flip.

        Args:
            augmentations: List of augmentation configurations

        Returns:
            PyTorch Compose transform pipeline for inference
        """
        transform_list = []

        if augmentations:
            # Extract normalization and resize parameters
            norm_mean, norm_std = cls.extract_normalization_params(augmentations)
            resize_dims = cls.extract_resize_dimensions(augmentations)

            # Add deterministic transforms only
            if resize_dims:
                transform_list.append(transforms.Resize(resize_dims))

            # Convert to tensor while preserving original value ranges
            transform_list.append(ValuePreservingToTensor())

            # Add normalization if specified
            if norm_mean is not None and norm_std is not None:
                transform_list.append(
                    transforms.Normalize(mean=norm_mean, std=norm_std)
                )
        else:
            # Default pipeline: convert to tensor while preserving original values
            # Use ValuePreservingToTensor since it handles both PIL images and tensors
            transform_list.append(ValuePreservingToTensor())

        return transforms.Compose(transform_list)

    @classmethod
    def extract_normalization_params(
        cls, augmentations: List[AugmentationItemType]
    ) -> tuple[Optional[List[float]], Optional[List[float]]]:
        """
        Extract normalization mean and std from augmentations.

        Args:
            augmentations: List of augmentation configurations

        Returns:
            Tuple of (mean, std) lists or (None, None) if not found
        """
        norm_mean = None
        norm_std = None

        for aug in augmentations:
            if isinstance(aug, NormalizationMean):
                norm_mean = aug.normalization_mean
            elif isinstance(aug, NormalizationStd):
                norm_std = aug.normalization_std

        return norm_mean, norm_std

    @classmethod
    def extract_resize_dimensions(
        cls, augmentations: List[AugmentationItemType]
    ) -> Optional[List[int]]:
        """
        Extract resize dimensions from augmentations.

        Args:
            augmentations: List of augmentation configurations

        Returns:
            Resize dimensions as [height, width] or None if not found
        """
        for aug in augmentations:
            if isinstance(aug, ResizeDimensions):
                return aug.resize_dimensions
        return None

    @classmethod
    def _create_geometric_transforms(
        cls, augmentations: List[AugmentationItemType]
    ) -> List[Any]:
        """
        Create geometric transforms from augmentation configurations.

        Args:
            augmentations: List of augmentation configurations

        Returns:
            List of PyTorch transforms for geometric augmentations
        """
        transform_list = []

        # Add augmentation-specific transforms using schema-based approach
        for aug in augmentations:
            # Use the schema's to_tensor() method to get the transform
            tensor_transform = aug.to_tensor()
            if tensor_transform is not None:
                transform_list.append(tensor_transform)

        return transform_list

    @classmethod
    def serialize_pipeline(
        cls, pipeline: transforms.Compose, path: Union[str, Path]
    ) -> None:
        """
        Serialize a transform pipeline to disk for inference use.

        Args:
            pipeline: PyTorch Compose transform pipeline
            path: Path to save the serialized pipeline
        """
        AugmentationSerializer.save_pipeline(pipeline, path)

    @classmethod
    def load_pipeline(cls, path: Union[str, Path]) -> transforms.Compose:
        """
        Load a serialized transform pipeline from disk.

        Args:
            path: Path to the serialized pipeline file

        Returns:
            PyTorch Compose transform pipeline

        Raises:
            FileNotFoundError: If the pipeline file doesn't exist
        """
        return AugmentationSerializer.load_pipeline(path)

    @classmethod
    def validate_augmentations(
        cls, augmentations: List[AugmentationItemType]
    ) -> Dict[str, Any]:
        """
        Validate augmentation configurations and return summary.

        Args:
            augmentations: List of augmentation configurations

        Returns:
            Dictionary with validation results and augmentation summary
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "summary": {
                "total_augmentations": len(augmentations),
                "geometric_transforms": 0,
                "normalization": False,
                "resize": None,
            },
        }

        has_mean = False
        has_std = False

        for aug in augmentations:
            if isinstance(aug, (HorizontalFlip, RotationRange, ZoomRange)):
                validation_result["summary"]["geometric_transforms"] += 1
            elif isinstance(aug, ResizeDimensions):
                validation_result["summary"]["resize"] = aug.resize_dimensions
            elif isinstance(aug, NormalizationMean):
                has_mean = True
            elif isinstance(aug, NormalizationStd):
                has_std = True

        # Check normalization consistency
        if has_mean and has_std:
            validation_result["summary"]["normalization"] = True
        elif has_mean or has_std:
            validation_result["warnings"].append(
                "Incomplete normalization: both mean and std should be provided"
            )

        return validation_result
