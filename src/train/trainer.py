"""
This module defines the ModelTrainer class, which encapsulates the logic for
training and evaluating a PyTorch model.
"""

import dataclasses
import logging
import time
import traceback
from typing import Any, Dict

import numpy as np
import torch
from tqdm import tqdm

from src.config.paths import get_run_paths
from src.models.model_utils import create_loss_function
from src.models.validation import ModelValidationError, ModelValidator
from src.train.artifact_manager import TrainingArtifactManager
from src.train.callbacks.handler import CallbackHandler
from src.train.callbacks.metrics_collection import (
    BatchMetricsData,
    MetricsCollectionCallback,
    ValidationBatchData,
)
from src.train.callbacks.metrics_plotting import MetricsPlottingCallback
from src.train.callbacks.model_run import ModelRunCallback
from src.train.data_classes import TrainingComponents, TrainingConfig
from src.train.metrics import Metrics
from src.train.metrics_persistence import MetricsPersistence
from src.train.trainer_config import TrainerConfig, TrainingBatchContext
from src.utils.device import select_device
from src.utils.logging import setup_training_logger

# Feature map visualization imports - handle gracefully if not available
try:
    from src.train.callbacks.feature_map_visualization import (
        FeatureMapVisualizationCallback,
    )

    FEATURE_MAP_VISUALIZATION_AVAILABLE = True
except ImportError as e:
    # Log the import error but don't fail
    logging.getLogger(__name__).warning(
        "Feature map visualization not available: %s", str(e)
    )
    FeatureMapVisualizationCallback = None
    FEATURE_MAP_VISUALIZATION_AVAILABLE = False

# Test image visualization imports - handle gracefully if not available
try:
    from src.train.callbacks.test_image_visualization import (
        TestImageVisualizationCallback,
    )

    TEST_IMAGE_VISUALIZATION_AVAILABLE = True
except ImportError as e:
    # Log the import error but don't fail
    logging.getLogger(__name__).warning(
        "Test image visualization not available: %s", str(e)
    )
    TestImageVisualizationCallback = None
    TEST_IMAGE_VISUALIZATION_AVAILABLE = False


class ModelTrainer:  # pylint: disable=too-many-instance-attributes
    """
    A class to handle the training and evaluation of a PyTorch model.
    """

    def __init__(self, config: TrainerConfig):
        """Initialize with TrainerConfig object."""
        # Initialize all attributes first to avoid attribute-defined-outside-init warnings
        self.logger = None
        self.metrics = None
        self.metrics_persistence = None
        self.components = None
        self.data_loaders = None
        self.config = None
        self.artifact_manager = None
        self.model_capabilities = None
        self.validation_result = None
        self.device = None
        self.callback_handler = None
        self.is_training = False
        self.stop_training = False
        self.lr_epoch_map = {}

        # Now initialize with the actual configuration
        self._init_from_config(config)

    def _init_from_config(self, config: TrainerConfig):
        """
        Initializes the ModelTrainer.

        Args:
            config (TrainerConfig): Configuration object containing all trainer parameters:
                - model_components: Dictionary with model, loss_fn, optimizer
                - data_loaders: Dictionary with train, test and optional validation DataLoaders
                - training_config: Configuration for the training run
                - callbacks: List of Callback instances (optional)
                - database_config: Database integration config (optional)
        """
        # Initialize core components
        self._init_core_components(config)

        # Validate model and dataset compatibility
        self._validate_model_dataset_compatibility(config)

        # Setup device and logging
        self._setup_device_and_logging()

        # Initialize callbacks
        self._init_callbacks(config)

        # Initialize training state
        self._init_training_state()

    def _init_core_components(self, config: TrainerConfig):
        """Initialize core trainer components."""
        # Initialize logger and metrics
        self.logger = logging.getLogger(__name__)
        self.metrics = Metrics()
        self.metrics_persistence = MetricsPersistence()

        # Initialize loss function
        loss_fn = self._init_loss_function(config)

        self.components = TrainingComponents(
            model=config.model_components["model"],
            loss_fn=loss_fn,
            optimizer=config.model_components["optimizer"],
        )
        self.data_loaders = config.data_loaders
        self.config = TrainingConfig(config.training_config)

        # Initialize artifact manager
        self.artifact_manager = TrainingArtifactManager(
            config=self.config,
            components=self.components,
            data_loaders=self.data_loaders,
            metrics_persistence=self.metrics_persistence,
        )

    def _validate_model_dataset_compatibility(self, config: TrainerConfig):
        """Validate model and dataset compatibility."""
        # Analyze model capabilities
        self.model_capabilities = ModelValidator.analyze_model_capabilities(
            self.components.model
        )
        self.logger.info(
            "Model analysis: %s, supports feature extraction: %s, output size: %s",
            self.model_capabilities["model_type"],
            self.model_capabilities["supports_feature_extraction"],
            self.model_capabilities["output_size"],
        )

        # Validate model-dataset compatibility if architecture_params are provided
        self.validation_result = None
        if config.architecture_params is not None:
            try:
                self.validation_result = (
                    ModelValidator.validate_model_dataset_compatibility(
                        model=self.components.model,
                        data_loaders=self.data_loaders,
                        architecture_params=config.architecture_params,
                    )
                )
                self.logger.info(
                    "Model validation successful: %d classes detected, model output size %d",
                    self.validation_result["dataset_num_classes"],
                    self.validation_result["model_output_size"],
                )
            except ModelValidationError as e:
                self.logger.error("Model validation failed: %s", str(e))
                raise
        else:
            self.logger.warning(
                "No architecture_params provided - skipping model-dataset compatibility validation"
            )

    def _setup_device_and_logging(self):
        """Setup device and logging configuration."""
        self.device = select_device()
        self.components.model.to(self.device)

        # Use model_run_uuid for logging with centralized paths
        self.logger = setup_training_logger(
            self.config.model_run_uuid, self.config.model_id
        )

        self.logger.info("Initializing ModelTrainer for model %s", self.config.model_id)
        self.logger.info("Using device: %s", self.device)

    def _init_callbacks(self, config: TrainerConfig):
        """Initialize and configure callbacks."""
        # Prepare callbacks list, adding default callbacks as needed
        callbacks_list = list(config.callbacks) if config.callbacks else []

        # Add MetricsCollectionCallback by default (always first for proper metrics collection)
        metrics_callback = MetricsCollectionCallback(collect_resource_metrics=True)
        callbacks_list.insert(0, metrics_callback)
        self.logger.info(
            "Added MetricsCollectionCallback for comprehensive metrics tracking"
        )

        # Add database callback if configured
        self._add_database_callback_if_configured(config, callbacks_list)

        # Add visualization callbacks
        self._add_feature_map_callback_if_supported(callbacks_list)
        self._add_test_image_callback_if_supported(callbacks_list)
        self._add_metrics_plotting_callback(callbacks_list)

        self.callback_handler = CallbackHandler(callbacks_list, self)

    def _add_database_callback_if_configured(
        self, config: TrainerConfig, callbacks_list: list
    ):
        """Add ModelRunCallback if database_config is provided."""
        if (
            config.database_config is not None
            and config.database_config.model_run_uuid is not None
        ):
            try:
                db_callback = ModelRunCallback(
                    model_run_uuid=config.database_config.model_run_uuid,
                    profile=config.database_config.profile,
                    verbose=True,
                )
                callbacks_list.append(db_callback)

                self.logger.info(
                    "Added ModelRunCallback for model run %s with profile %s",
                    config.database_config.model_run_uuid,
                    config.database_config.profile or "default",
                )
            except ImportError as e:
                self.logger.warning(
                    "Failed to import ModelRunCallback, database updates will be disabled: %s",
                    str(e),
                )

    def _init_training_state(self):
        """Initialize training state and configuration."""

        # Log validation data setup
        if "validation" in self.data_loaders:
            self.logger.info(
                "Using dedicated validation data loader for epoch validation"
            )
        else:
            self.logger.info(
                "No validation data loader found, will use test data for epoch validation"
            )

        # Initialize training state attributes
        self.is_training = False
        self.stop_training = False  # Flag to allow callbacks to stop training

        # Learning rate schedule
        self.lr_epoch_map: Dict[int, float] = {}
        if self.config.learning_rate_schedule:
            for item in self.config.learning_rate_schedule:
                if isinstance(item, dict) and "epoch" in item and "rate" in item:
                    self.lr_epoch_map[item["epoch"]] = item["rate"]
                elif hasattr(item, "epoch") and hasattr(
                    item, "rate"
                ):  # Handle Pydantic model objects
                    self.lr_epoch_map[item.epoch] = item.rate
                else:
                    self.logger.warning(
                        "Invalid item in learning_rate_schedule: %s. Skipping.", item
                    )
            if self.lr_epoch_map:
                self.logger.info("Learning rate schedule loaded: %s", self.lr_epoch_map)

        # Gradient clipping logging
        if (
            self.config.gradient_clip_max_norm
            and self.config.gradient_clip_max_norm > 0
        ):
            self.logger.info(
                "Gradient clipping enabled with max_norm: %s",
                self.config.gradient_clip_max_norm,
            )

    def _init_loss_function(self, config: TrainerConfig):
        """
        Initialize the loss function based on configuration.

        This method handles creating the appropriate loss function based on:
        1. The loss_fn provided directly in model_components (if any)
        2. The structured loss_function configuration in model_version_parameters
        3. A default BCE loss if neither is provided

        Args:
            config: TrainerConfig object containing all configuration parameters

        Returns:
            PyTorch loss function instance
        """
        # Get loss function from model components with fallback
        loss_fn = config.model_components.get("loss_fn")

        # Check for loss_function in model version parameters
        if config.training_config.get(
            "model_version_parameters"
        ) and config.training_config.get("model_version_parameters").get(
            "loss_function"
        ):
            try:
                # Create a loss function using the specified configuration
                loss_function_config = config.training_config[
                    "model_version_parameters"
                ]["loss_function"]

                # Ensure we have a properly structured loss function config
                if (
                    isinstance(loss_function_config, dict)
                    and "type" in loss_function_config
                ):
                    loss_type = loss_function_config.get("type", "bce")
                    self.logger.info(
                        "Using loss function '%s' with custom config from model version parameters",
                        loss_type,
                    )
                    # Create the loss function with the appropriate parameters
                    loss_fn = create_loss_function(loss_function_config)
                else:
                    self.logger.warning(
                        "Invalid loss function configuration format. "
                        "Expected dict with 'type' key. Using default loss function."
                    )
            except Exception as e:
                self.logger.error(
                    "Error creating loss function: %s. Using default loss function.",
                    str(e),
                )

        # If no loss function was created, use a default one
        if loss_fn is None:
            default_loss_config = {"type": "bce", "config": {"reduction": "mean"}}
            loss_fn = create_loss_function(default_loss_config)
            self.logger.info("Using default BCE loss function")

        return loss_fn

    def train(
        self,
    ):
        """
        Train the model for the specified number of epochs.
        """
        try:
            start_epoch = 0
            self.is_training = True  # pylint: disable=attribute-defined-outside-init

            self.callback_handler.on_train_begin()
            epochs = self.config.epochs
            self.logger.info("Starting model training for %s epochs...", epochs)

            for epoch in range(start_epoch, epochs):
                self.callback_handler.on_epoch_begin(epoch)
                # Apply learning rate schedule
                if epoch in self.lr_epoch_map:
                    new_lr = self.lr_epoch_map[epoch]
                    for param_group in self.components.optimizer.param_groups:
                        param_group["lr"] = new_lr
                    self.logger.info(
                        "Epoch %s: Learning rate set to %s by schedule.",
                        epoch + 1,
                        new_lr,
                    )

                epoch_start = time.time()

                train_metrics = self._train_epoch(epoch)
                validation_metrics = self._validate_epoch(epoch)

                epoch_end = time.time()
                self.metrics.storage.timing["epoch_times"].append(
                    epoch_end - epoch_start
                )

                # Create a log dictionary for callbacks
                epoch_logs = {
                    "epoch": epoch,
                    **train_metrics,
                    **validation_metrics,
                }

                self.callback_handler.on_epoch_end(epoch, epoch_logs)

                if self.stop_training:
                    self.logger.info(
                        "Stopping training early at epoch %s as requested by a callback.",
                        epoch,
                    )
                    break

            # Perform final test evaluation if validation data was used during training
            self._final_test_evaluation()

            self._save_artifacts()

            # Get total training time from metrics (set by MetricsCollectionCallback)
            total_time = self.metrics.storage.timing.get("total_training_time", 0)
            self.logger.info(
                "Training finished in %.2fs. Artifacts saved to %s",
                total_time,
                self.config.run_output_dir,
            )

            self.callback_handler.on_train_end()

        except KeyboardInterrupt:
            self.logger.info("Training interrupted by user (KeyboardInterrupt).")
            self.metrics.error = {
                "type": "KeyboardInterrupt",
                "message": "Training interrupted by user.",
            }
            # Optionally save checkpoint and artifacts on interrupt
            if getattr(self.config, "save_on_interrupt", True):
                self.logger.info("Saving artifacts due to interrupt...")
                self._save_artifacts()

        except Exception as e:  # pylint: disable=broad-except
            self.logger.error("Unhandled exception during training: %s", str(e))
            self.logger.error(traceback.format_exc())
            self.metrics.error = {"type": str(type(e).__name__), "message": str(e)}
            # Optionally save checkpoint and artifacts on error
            if getattr(self.config, "save_on_error", True):
                self.logger.info("Saving artifacts due to error...")
                self._save_artifacts()
            raise
        finally:
            self.is_training = False  # pylint: disable=attribute-defined-outside-init
        return self.metrics  # Return the Metrics object

    def _restore_metrics(self, checkpoint_data):
        """Helper to restore metrics from a checkpoint using MetricsPersistence."""
        if "metrics" in checkpoint_data:
            loaded_metrics = checkpoint_data["metrics"]

            # Convert to Metrics object if it's a dictionary
            if isinstance(loaded_metrics, dict):
                self.metrics_persistence.update_metrics(self.metrics, loaded_metrics)
                self.logger.info(
                    "Restored metrics from dictionary using MetricsPersistence."
                )

            # If it's already a Metrics object, copy its data
            elif isinstance(loaded_metrics, Metrics):
                # Copy all attributes from loaded_metrics to self.metrics
                # This is more maintainable than copying each attribute individually
                for field in dataclasses.fields(Metrics):
                    field_name = field.name
                    if hasattr(loaded_metrics, field_name):
                        setattr(
                            self.metrics,
                            field_name,
                            getattr(loaded_metrics, field_name),
                        )

                self.logger.info("Restored metrics from Metrics object.")

            else:
                self.logger.warning(
                    "Metrics in checkpoint has unexpected type: %s",
                    type(loaded_metrics),
                )
        else:
            self.logger.warning("Metrics not found in checkpoint. Skipping restore.")

    def _save_artifacts(self):
        """
        Save model artifacts using the TrainingArtifactManager.
        """
        self.logger.info("Saving artifacts...")
        try:
            # Create all necessary directories using centralized system
            run_paths = get_run_paths(self.config.model_run_uuid)
            run_paths.ensure_directories()

            # Update artifact manager with current metrics
            self.artifact_manager.metrics = self.metrics

            # Use the artifact manager to save all artifacts
            self.artifact_manager.save_all_artifacts()

        except Exception as e:
            self.logger.error("Error saving artifacts: %s", str(e))
            self.logger.error(traceback.format_exc())

    def _train_epoch(self, epoch):
        """
        Runs a single training epoch.
        """
        train_start = time.time()
        self.components.model.train()
        batch_losses = []
        batch_accuracies = []

        train_loader_tqdm = tqdm(
            self.data_loaders["train"], desc=f"Epoch {epoch+1} Training", leave=False
        )

        # Get metrics callback for batch metrics collection
        metrics_callback = self.get_metrics_callback()

        for batch_idx, (X, y) in enumerate(train_loader_tqdm):
            batch_context = TrainingBatchContext(
                epoch=epoch,
                batch_idx=batch_idx,
                X=X,
                y=y,
                progress_bar=train_loader_tqdm,
                metrics_callback=metrics_callback,
            )
            batch_metrics = self._process_training_batch(batch_context)
            batch_losses.append(batch_metrics["loss"])
            batch_accuracies.append(batch_metrics["accuracy"])

        # Record training time via callback
        train_time = time.time() - train_start
        if metrics_callback:
            metrics_callback.record_epoch_timing("train", train_time)

        return {
            "train_loss": np.mean(batch_losses) if batch_losses else 0,
            "train_accuracy": np.mean(batch_accuracies) if batch_accuracies else 0,
        }

    def _process_training_batch(self, context: TrainingBatchContext):
        """Process a single training batch."""
        batch_start_time = time.time()
        X, y = context.X.to(self.device), context.y.to(self.device)

        self.components.optimizer.zero_grad()
        yHat = self.components.model(X)
        loss = self.components.loss_fn(yHat, y)
        loss.backward()

        # Gradient Clipping
        if (
            self.config.gradient_clip_max_norm
            and self.config.gradient_clip_max_norm > 0
        ):
            torch.nn.utils.clip_grad_norm_(
                self.components.model.parameters(),
                max_norm=self.config.gradient_clip_max_norm,
            )

        self.components.optimizer.step()

        batch_loss = loss.item()
        batch_accuracy = torch.mean(
            ((yHat.squeeze() > 0) == y).float()
        ).item()  # Keep as decimal (0.0-1.0)

        context.progress_bar.set_postfix(
            {"loss": f"{batch_loss:.4f}", "acc": f"{batch_accuracy * 100:.2f}%"}
        )

        # Collect batch metrics via callback
        if context.metrics_callback:
            batch_time = time.time() - batch_start_time
            batch_data = BatchMetricsData(
                epoch=context.epoch,
                batch_idx=context.batch_idx,
                loss=batch_loss,
                accuracy=batch_accuracy,
                time=batch_time,
            )
            context.metrics_callback.collect_batch_metrics(batch_data)

        return {"loss": batch_loss, "accuracy": batch_accuracy}

    def _validate_epoch(self, epoch):
        """
        Runs a validation epoch and calculates comprehensive metrics.

        Uses validation data loader if available, otherwise falls back to test data loader.
        """
        validation_start = time.time()
        self.components.model.eval()

        # Get metrics callback for validation metrics collection
        metrics_callback = self.get_metrics_callback()

        # Initialize validation via callback
        if metrics_callback:
            metrics_callback.on_validation_begin()

        # Use validation data loader if available, otherwise fall back to test data loader
        validation_loader = self.data_loaders.get(
            "validation", self.data_loaders["test"]
        )
        loader_type = "validation" if "validation" in self.data_loaders else "test"

        val_loader_tqdm = tqdm(
            validation_loader,
            desc=f"Epoch {epoch+1} Validation ({loader_type})",
            leave=False,
        )

        with torch.no_grad():
            for X, y in val_loader_tqdm:
                self._process_validation_batch(X, y, metrics_callback, val_loader_tqdm)

        # Calculate final metrics via callback
        validation_metrics = {}
        if metrics_callback:
            validation_metrics = metrics_callback.calculate_validation_metrics()
            metrics_callback.on_validation_end()

        # Record validation time via callback
        validation_time = time.time() - validation_start
        if metrics_callback:
            metrics_callback.record_epoch_timing("validation", validation_time)

        return validation_metrics

    def _final_test_evaluation(self):
        """
        Perform final evaluation on test data after training completes.

        This method only runs when validation data was used during training,
        ensuring test data remains truly held-out for unbiased evaluation.
        """
        # Only perform final test evaluation if validation data was used during training
        # This ensures test data was truly held-out and not used for validation
        if "validation" not in self.data_loaders:
            self.logger.info(
                "Skipping final test evaluation - no validation data was used during training "
                "(test data was used for validation, so it's not held-out)"
            )
            return

        self.logger.info("Performing final evaluation on held-out test data...")

        # Run the evaluation and get metrics
        test_metrics, test_time = self._run_final_test_evaluation()

        # Process and store the metrics
        final_test_metrics = self._process_final_test_metrics(test_metrics, test_time)

        # Log the results
        self._log_final_test_results(final_test_metrics, test_time)

    def _run_final_test_evaluation(self):
        """Run the actual final test evaluation and return metrics and timing."""
        test_start = time.time()
        self.components.model.eval()

        # Get metrics callback for test metrics collection
        metrics_callback = self.get_metrics_callback()

        # Initialize test evaluation via callback
        if metrics_callback:
            metrics_callback.on_validation_begin()  # Reuse validation infrastructure

        test_loader = self.data_loaders["test"]
        test_loader_tqdm = tqdm(
            test_loader,
            desc="Final Test Evaluation",
            leave=False,
        )

        with torch.no_grad():
            for X, y in test_loader_tqdm:
                self._process_validation_batch(X, y, metrics_callback, test_loader_tqdm)

        # Calculate final test metrics via callback
        test_metrics = {}
        if metrics_callback:
            test_metrics = metrics_callback.calculate_validation_metrics()
            metrics_callback.on_validation_end()

        # Record test evaluation time
        test_time = time.time() - test_start
        if metrics_callback:
            metrics_callback.record_epoch_timing("final_test_evaluation", test_time)

        return test_metrics, test_time

    def _process_final_test_metrics(self, test_metrics, test_time):
        """Process and store final test metrics."""
        # Store final test metrics with special prefix to distinguish from validation
        final_test_metrics = {}
        for key, value in test_metrics.items():
            # Convert validation metrics to final_test metrics
            if key.startswith("validation_"):
                new_key = key.replace("validation_", "final_test_")
                final_test_metrics[new_key] = value
            else:
                final_test_metrics[f"final_test_{key}"] = value

        # Add evaluation time to final test metrics
        final_test_metrics["final_test_evaluation_time"] = test_time

        # Add final test metrics to the metrics object
        self.metrics.add_epoch_metric(final_test_metrics)

        # Also store evaluation time in the final_test metrics object
        self.metrics.storage.final_test.evaluation_time = test_time

        return final_test_metrics

    def _log_final_test_results(self, final_test_metrics, test_time):
        """Log the final test evaluation results."""
        test_loss = final_test_metrics.get("final_test_loss", "N/A")
        test_accuracy = final_test_metrics.get("final_test_accuracy", "N/A")
        test_precision = final_test_metrics.get("final_test_precision", "N/A")
        test_recall = final_test_metrics.get("final_test_recall", "N/A")
        test_f1 = final_test_metrics.get("final_test_f1_score", "N/A")

        self.logger.info("=" * 60)
        self.logger.info("FINAL TEST EVALUATION RESULTS")
        self.logger.info("=" * 60)
        self.logger.info("Test Loss: %s", test_loss)
        self.logger.info("Test Accuracy: %s", test_accuracy)
        self.logger.info("Test Precision: %s", test_precision)
        self.logger.info("Test Recall: %s", test_recall)
        self.logger.info("Test F1-Score: %s", test_f1)
        self.logger.info("Evaluation Time: %.2fs", test_time)
        self.logger.info("=" * 60)

    def _process_validation_batch(self, X, y, metrics_callback, val_loader_tqdm):
        """Process a single validation batch and update metrics."""
        X, y = X.to(self.device), y.to(self.device)
        yHat = self.components.model(X)
        loss = self.components.loss_fn(yHat, y)

        batch_loss = loss.item()
        batch_accuracy = torch.mean(
            ((yHat.squeeze() > 0) == y).float()
        ).item()  # Keep as decimal (0.0-1.0)

        # Collect predictions and labels for classification metrics via callback
        if metrics_callback:
            predictions = (yHat.squeeze() > 0).cpu().numpy()
            labels = y.cpu().numpy()
            batch_data = ValidationBatchData(
                predictions=predictions,
                labels=labels,
                loss=batch_loss,
                accuracy=batch_accuracy,
            )
            metrics_callback.collect_validation_batch_data(batch_data)

        val_loader_tqdm.set_postfix(
            {
                "validation_loss": f"{batch_loss:.4f}",
                "validation_acc": f"{batch_accuracy * 100:.2f}%",
            }
        )

    def get_metrics_callback(self):
        """Get the MetricsCollectionCallback from the callback handler."""
        if not hasattr(self, "callback_handler") or not self.callback_handler:
            return None

        for callback in self.callback_handler.callbacks:
            if isinstance(callback, MetricsCollectionCallback):
                return callback
        return None

    def add_custom_metric(self, name, value):
        """
        Adds a custom metric to the metrics collection.
        """
        if name not in self.metrics.custom_metrics:
            self.metrics.custom_metrics[name] = []
        self.metrics.custom_metrics[name].append(value)

    def get_metrics(self) -> Dict[str, Any]:
        """
        Returns a dictionary of metrics.
        """
        return dataclasses.asdict(self.metrics)

    def _add_callback_if_not_present(
        self, callbacks_list: list, callback_class, callback_name: str, **kwargs
    ) -> None:
        """
        Helper method to add a callback if it's not already present.

        Args:
            callbacks_list: List of callbacks to check and potentially add to
            callback_class: The callback class to instantiate
            callback_name: Name of the callback for logging
            **kwargs: Arguments to pass to the callback constructor
        """
        # Check if callback is already in the list
        has_callback = any(
            isinstance(callback, callback_class) for callback in callbacks_list
        )

        if not has_callback:
            callback_instance = callback_class(**kwargs)
            callbacks_list.append(callback_instance)
            self.logger.info(
                "Added %s for model run %s", callback_name, self.config.model_run_uuid
            )
        else:
            self.logger.debug("%s already present", callback_name)

    def _add_feature_map_callback_if_supported(self, callbacks_list: list) -> None:
        """
        Add FeatureMapVisualizationCallback if the model supports feature extraction.

        Args:
            callbacks_list: List of callbacks to potentially add the feature map callback to
        """
        # Check if feature map visualization is available
        if not FEATURE_MAP_VISUALIZATION_AVAILABLE:
            self.logger.debug(
                "Feature map visualization not available, skipping FeatureMapVisualizationCallback"
            )
            return

        try:
            # Check if model supports feature extraction using capabilities analysis
            if self.model_capabilities["supports_feature_extraction"]:
                self._add_callback_if_not_present(
                    callbacks_list,
                    FeatureMapVisualizationCallback,
                    "FeatureMapVisualizationCallback",
                    model_run_uuid=self.config.model_run_uuid,
                )
            else:
                self.logger.debug(
                    "Model does not support feature extraction, "
                    "skipping FeatureMapVisualizationCallback"
                )

        except Exception as e:
            self.logger.warning(
                "Error checking feature extraction support, "
                "feature map visualization will be disabled: %s",
                str(e),
            )

    def _add_test_image_callback_if_supported(self, callbacks_list: list) -> None:
        """
        Add TestImageVisualizationCallback by default.

        Args:
            callbacks_list: List of callbacks to potentially add the test image callback to
        """
        # Check if test image visualization is available
        if not TEST_IMAGE_VISUALIZATION_AVAILABLE:
            self.logger.debug(
                "Test image visualization not available, skipping TestImageVisualizationCallback"
            )
            return

        try:
            self._add_callback_if_not_present(
                callbacks_list,
                TestImageVisualizationCallback,
                "TestImageVisualizationCallback",
                model_run_uuid=self.config.model_run_uuid,
            )
        except Exception as e:
            self.logger.warning(
                "Error adding test image visualization callback, "
                "test image visualization will be disabled: %s",
                str(e),
            )

    def _add_metrics_plotting_callback(self, callbacks_list: list) -> None:
        """
        Add MetricsPlottingCallback by default to generate plots at the end of training.

        Args:
            callbacks_list: List of callbacks to potentially add the metrics plotting callback to
        """
        try:
            self._add_callback_if_not_present(
                callbacks_list,
                MetricsPlottingCallback,
                "MetricsPlottingCallback",
                model_run_uuid=self.config.model_run_uuid,
            )
        except Exception as e:
            self.logger.warning(
                "Error adding metrics plotting callback, "
                "metrics plots will not be generated: %s",
                str(e),
            )
