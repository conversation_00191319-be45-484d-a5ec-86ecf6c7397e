"""
Configuration classes for the ModelTrainer.

This module contains all configuration-related dataclasses and context objects
used by the ModelTrainer.
"""

from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

import torch

from src.train.callbacks.base import Callback


@dataclass
class TrainingBatchContext:
    """Context data for processing a training batch."""

    epoch: int
    batch_idx: int
    X: torch.Tensor
    y: torch.Tensor
    progress_bar: Any
    metrics_callback: Any


@dataclass
class DatabaseConfig:
    """Configuration for database integration."""

    model_run_uuid: Optional[Union[str, UUID]] = None
    profile: Optional[str] = None


@dataclass
class TrainerConfig:
    """Configuration for ModelTrainer initialization."""

    model_components: Dict[str, Any]
    data_loaders: Dict[str, Any]
    training_config: Dict[str, Any]
    callbacks: Optional[List[Callback]] = None
    database_config: Optional[DatabaseConfig] = None
    architecture_params: Optional[Dict[str, Any]] = None

    @property
    def config(self):
        """Backward compatibility property for accessing training_config."""
        return self.training_config
