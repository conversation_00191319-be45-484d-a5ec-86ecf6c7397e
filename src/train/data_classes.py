"""
Data classes used for training, including configuration, metrics, and data loaders.
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

import torch

from src.config.paths import get_run_paths


@dataclass
# pylint: disable=too-many-instance-attributes
class TrainingConfig:
    """Configuration for a training run."""

    config: Dict[str, Any]
    learning_rate_schedule: Optional[List[Dict[str, Any]]] = field(
        init=False, default=None
    )
    gradient_clip_max_norm: Optional[float] = field(init=False, default=None)

    def __post_init__(self):
        """
        Sets attributes from the config dict with defaults.
        """
        # Core training parameters
        self.model_id: str = self.config.get("model_id", "model")
        self.epochs: int = self.config.get("epochs", 10)

        # Model run UUID is required for centralized paths
        self.model_run_uuid: str = self.config["model_run_uuid"]

        # Derive run_output_dir from model_run_uuid using centralized paths
        run_paths = get_run_paths(self.model_run_uuid)
        self.run_output_dir: str = str(run_paths.base)

        self.learning_rate_schedule: Optional[List[Dict[str, Any]]] = self.config.get(
            "learning_rate_schedule", None
        )
        self.gradient_clip_max_norm: Optional[float] = self.config.get(
            "gradient_clip_max_norm", None
        )

    def __getattr__(self, name: str) -> Any:
        """
        Fallback to getting from the original config dict.
        """
        if name in self.config:
            return self.config[name]
        raise AttributeError(f"'TrainingConfig' object has no attribute '{name}'")


@dataclass
class TrainingComponents:
    """Core components for training."""

    model: torch.nn.Module
    loss_fn: Any
    optimizer: torch.optim.Optimizer
    scheduler: Optional[Any] = None
