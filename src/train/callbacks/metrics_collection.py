"""
Metrics collection callback for comprehensive training metrics tracking.

This callback consolidates all metrics collection logic that was previously
scattered throughout the trainer, including:
- Training and validation metrics (loss, accuracy)
- Classification metrics (precision, recall, F1-score)
- Resource usage metrics (CPU, memory, GPU)
- Batch-level metrics
- Timing metrics
"""

import logging
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import numpy as np
import psutil
import torch
from sklearn.metrics import f1_score, precision_score, recall_score

from src.train.callbacks.base import Callback


@dataclass
class ValidationBatchData:
    """Data class for validation batch metrics."""

    predictions: np.ndarray
    labels: np.ndarray
    loss: float
    accuracy: float


@dataclass
class BatchMetricsData:
    """Data class for batch metrics collection."""

    epoch: int
    batch_idx: int
    loss: float
    accuracy: float
    time: float


class MetricsCollectionCallback(Callback):
    """
    Callback that handles comprehensive metrics collection during training.

    This callback consolidates metrics collection logic that was previously
    scattered throughout the trainer, making the trainer code more focused
    on the core training loop.
    """

    def __init__(self, collect_resource_metrics: bool = True):
        """
        Initialize the metrics collection callback.

        Args:
            collect_resource_metrics: Whether to collect system resource metrics
        """
        super().__init__()
        self.collect_resource_metrics = collect_resource_metrics
        self.logger = None  # Will be set to trainer's logger when trainer is set

        # Temporary storage for batch-level data during validation
        self._validation_batch_metrics: Optional[Dict[str, List]] = None

    def set_trainer(self, trainer) -> None:
        """Override to set the logger to the trainer's logger."""
        super().set_trainer(trainer)
        if trainer and hasattr(trainer, "logger"):
            self.logger = trainer.logger
        else:
            # Fallback to default logger if trainer doesn't have one
            self.logger = logging.getLogger(__name__)

    def on_train_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of training."""
        if self.trainer is None:
            return

        # Initialize timing
        self.trainer.metrics.storage.timing["start_time"] = time.time()
        self.logger.debug("Started metrics collection for training")

    def on_train_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of training."""
        if self.trainer is None:
            return

        # Finalize timing metrics
        self.trainer.metrics.storage.timing["end_time"] = time.time()
        total_time = (
            self.trainer.metrics.storage.timing["end_time"]
            - self.trainer.metrics.storage.timing["start_time"]
        )
        self.trainer.metrics.storage.timing["total_training_time"] = total_time

        self.logger.debug("Completed metrics collection for training")

    def on_epoch_begin(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of an epoch."""
        if self.trainer is None:
            return

        # Collect resource metrics at the start of each epoch
        if self.collect_resource_metrics:
            self._collect_resource_metrics()

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of an epoch."""
        if self.trainer is None or logs is None:
            return

        # Store epoch-level metrics
        self._store_epoch_metrics(logs)

        # Log comprehensive epoch summary
        self._log_epoch_summary(epoch, logs)

    def on_validation_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of validation."""
        # Initialize validation batch metrics collection
        self._validation_batch_metrics = {
            "losses": [],
            "accuracies": [],
            "predictions": [],
            "labels": [],
        }

    def on_validation_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of validation."""
        # Clear validation batch metrics
        self._validation_batch_metrics = None

    def collect_batch_metrics(self, batch_data: BatchMetricsData) -> None:
        """
        Collect metrics for a training batch.

        Args:
            batch_data: BatchMetricsData containing epoch, batch_idx, loss, accuracy, and time
        """
        if self.trainer is None:
            return

        batch_metrics = {
            "epoch": batch_data.epoch,
            "batch": batch_data.batch_idx,
            "loss": batch_data.loss,
            "accuracy": batch_data.accuracy,
            "time": batch_data.time,
        }

        self.trainer.metrics.batch_metrics.append(batch_metrics)

    def collect_validation_batch_data(self, batch_data: ValidationBatchData) -> None:
        """
        Collect data from a validation batch for later classification metrics calculation.

        Args:
            batch_data: ValidationBatchData containing predictions, labels, loss, and accuracy
        """
        if self._validation_batch_metrics is None:
            return

        self._validation_batch_metrics["predictions"].extend(batch_data.predictions)
        self._validation_batch_metrics["labels"].extend(batch_data.labels)
        self._validation_batch_metrics["losses"].append(batch_data.loss)
        self._validation_batch_metrics["accuracies"].append(batch_data.accuracy)

    def calculate_validation_metrics(self) -> Dict[str, float]:
        """
        Calculate comprehensive validation metrics from collected batch data.

        Returns:
            Dictionary containing validation loss, accuracy, and classification metrics
        """
        if self._validation_batch_metrics is None:
            return {
                "validation_loss": 0.0,
                "validation_accuracy": 0.0,
                "precision": 0.0,
                "recall": 0.0,
                "f1_score": 0.0,
            }

        # Calculate average loss and accuracy
        avg_loss = (
            np.mean(self._validation_batch_metrics["losses"])
            if self._validation_batch_metrics["losses"]
            else 0.0
        )
        avg_accuracy = (
            np.mean(self._validation_batch_metrics["accuracies"])
            if self._validation_batch_metrics["accuracies"]
            else 0.0
        )

        # Calculate classification metrics
        classification_metrics = self._calculate_classification_metrics(
            self._validation_batch_metrics["labels"],
            self._validation_batch_metrics["predictions"],
        )

        return {
            "validation_loss": avg_loss,
            "validation_accuracy": avg_accuracy,
            **classification_metrics,
        }

    def record_epoch_timing(self, phase: str, duration: float) -> None:
        """
        Record timing for a training phase.

        Args:
            phase: Phase name ('train' or 'validation')
            duration: Duration in seconds
        """
        if self.trainer is None:
            return

        if phase == "train":
            self.trainer.metrics.storage.timing["train_times"].append(duration)
        elif phase == "validation":
            self.trainer.metrics.storage.timing["validation_times"].append(duration)

    def _store_epoch_metrics(self, logs: Dict[str, Any]) -> None:
        """Store epoch-level metrics in the trainer's metrics object."""
        # Store training metrics
        if "train_loss" in logs:
            self.trainer.metrics.storage.train.losses.append(logs["train_loss"])
        if "train_accuracy" in logs:
            self.trainer.metrics.storage.train.accuracies.append(logs["train_accuracy"])

        # Store validation metrics
        if "validation_loss" in logs:
            self.trainer.metrics.storage.test.losses.append(logs["validation_loss"])
        if "validation_accuracy" in logs:
            self.trainer.metrics.storage.test.accuracies.append(
                logs["validation_accuracy"]
            )

        # Store classification metrics
        self.trainer.metrics.storage.classification.precision.append(
            logs.get("precision", 0.0)
        )
        self.trainer.metrics.storage.classification.recall.append(
            logs.get("recall", 0.0)
        )
        self.trainer.metrics.storage.classification.f1_score.append(
            logs.get("f1_score", 0.0)
        )

    def _log_epoch_summary(self, epoch: int, logs: Dict[str, Any]) -> None:
        """Log a comprehensive summary of epoch metrics."""
        epochs = getattr(self.trainer.config, "epochs", "unknown")

        self.logger.info(
            "Epoch %s/%s - Train: loss=%.4f, acc=%.2f%% | "
            "Val: loss=%.4f, acc=%.2f%%, prec=%.3f, rec=%.3f, f1=%.3f",
            epoch + 1,
            epochs,
            logs.get("train_loss", 0.0),
            logs.get("train_accuracy", 0.0) * 100,
            logs.get("validation_loss", 0.0),
            logs.get("validation_accuracy", 0.0) * 100,
            logs.get("precision", 0.0),
            logs.get("recall", 0.0),
            logs.get("f1_score", 0.0),
        )

    def _calculate_classification_metrics(
        self, true_labels: List, predictions: List
    ) -> Dict[str, float]:
        """
        Calculate classification metrics (precision, recall, F1-score).

        Args:
            true_labels: List of true labels
            predictions: List of predicted labels

        Returns:
            Dict containing precision, recall, and f1_score
        """
        if not true_labels or not predictions:
            return {"precision": 0.0, "recall": 0.0, "f1_score": 0.0}

        try:
            precision = precision_score(
                true_labels, predictions, average="macro", zero_division=0
            )
            recall = recall_score(
                true_labels, predictions, average="macro", zero_division=0
            )
            f1 = f1_score(true_labels, predictions, average="macro", zero_division=0)

            return {"precision": precision, "recall": recall, "f1_score": f1}
        except Exception as e:
            self.logger.warning(
                "Failed to calculate classification metrics: %s", str(e)
            )
            return {"precision": 0.0, "recall": 0.0, "f1_score": 0.0}

    def _collect_resource_metrics(self) -> None:
        """Collect system resource usage metrics."""
        if self.trainer is None:
            return

        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.trainer.metrics.storage.resources["cpu_percent"].append(cpu_percent)

            memory = psutil.virtual_memory()
            self.trainer.metrics.storage.resources["memory_percent"].append(
                memory.percent
            )
            self.trainer.metrics.storage.resources["memory_used_gb"].append(
                memory.used / (1024**3)
            )
            self.trainer.metrics.storage.resources["memory_available_gb"].append(
                memory.available / (1024**3)
            )

            disk = psutil.disk_usage("/")
            self.trainer.metrics.storage.resources["disk_percent"].append(disk.percent)

            if torch.cuda.is_available():
                gpu_memory_allocated = torch.cuda.memory_allocated() / (1024**3)
                gpu_memory_reserved = torch.cuda.memory_reserved() / (1024**3)
                self.trainer.metrics.storage.resources["gpu_memory_used"].append(
                    gpu_memory_allocated
                )
                self.trainer.metrics.storage.resources["gpu_memory_reserved"].append(
                    gpu_memory_reserved
                )

                # Log resource usage periodically
                if len(self.trainer.metrics.storage.resources["cpu_percent"]) % 10 == 0:
                    self.logger.debug(
                        "Resource usage - CPU: %.1f%%, Memory: %.1f%%, "
                        "GPU Memory: %.2fGB/%.2fGB",
                        cpu_percent,
                        memory.percent,
                        gpu_memory_allocated,
                        gpu_memory_reserved,
                    )
            elif len(self.trainer.metrics.storage.resources["cpu_percent"]) % 10 == 0:
                self.logger.debug(
                    "Resource usage - CPU: %.1f%%, Memory: %.1f%%",
                    cpu_percent,
                    memory.percent,
                )
        except Exception as e:
            self.logger.warning("Error collecting resource metrics: %s", str(e))
