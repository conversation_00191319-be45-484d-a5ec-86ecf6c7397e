# src/train/callbacks/__init__.py
"""
Callback system for the ModelTrainer.

This package provides a modular callback system with the following components:

- base.Callback: Abstract base class for all callbacks
- handler.CallbackHandler: Orchestrates execution of multiple callbacks
- metrics_collection.MetricsCollectionCallback: Comprehensive metrics collection
  (automatically added by trainer)
- early_stopping.EarlyStoppingCallback: Stops training when metrics stop improving
- model_checkpoint.ModelCheckpoint: Saves model weights when metrics improve
- model_run.ModelRunCallback: Integrates with database for training tracking
- feature_map_visualization.FeatureMapVisualizationCallback: Generates feature map visualizations
  (automatically added by trainer for CNN models)
- test_image_visualization.TestImageVisualizationCallback: Generates test image visualizations
  (automatically added by trainer)
- metrics_plotting.MetricsPlottingCallback: Generates comprehensive metrics plots at end of training
  (automatically added by trainer)

Only the base classes are re-exported for convenience. Import specific callbacks
from their individual modules.
"""

from .base import Callback
from .handler import Callback<PERSON>andler

__all__ = [
    "Callback",
    "CallbackHandler",
]
