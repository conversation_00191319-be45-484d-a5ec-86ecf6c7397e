# src/train/callbacks/early_stopping.py
"""
Early stopping callback for training termination based on monitored metrics.
"""

import logging
from typing import Any, Dict

from .base import Callback

logger = logging.getLogger(__name__)


class EarlyStoppingCallback(Callback):  # pylint: disable=too-many-instance-attributes
    """
    Callback to stop training when a monitored metric has stopped improving.
    """

    def __init__(  # pylint: disable=too-many-arguments
        self,
        monitor: str = "validation_loss",
        patience: int = 5,
        min_delta: float = 0.0,
        mode: str = "min",
        *,  # Mark subsequent arguments as keyword-only
        verbose: bool = True,
    ):
        super().__init__()
        self.monitor = monitor
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.verbose = verbose
        self.wait = 0
        self.best_score = float("inf") if self.mode == "min" else float("-inf")

        if self.mode not in ["min", "max"]:
            raise ValueError(
                f"EarlyStopping mode '{self.mode}' is unknown, please use 'min' or 'max'."
            )

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of an epoch to check if training should stop early."""
        logs = logs or {}
        current_score = logs.get(self.monitor)

        if current_score is None:
            logger.warning(
                "Early stopping conditioned on unavailable metric `%s`. Available metrics are: %s",
                self.monitor,
                ",".join(logs.keys()),
            )
            return

        # Check if this is the first time a score is being set
        is_first_score = self.best_score in [float("inf"), float("-inf")]

        if self.mode == "min":
            improved = current_score < self.best_score - self.min_delta
        else:  # mode == "max"
            improved = current_score > self.best_score + self.min_delta

        if improved:
            previous_best = self.best_score
            self.best_score = current_score
            self.wait = 0
            # Only log improvement if it's not the very first score being set
            if self.verbose and not is_first_score:
                logger.info(
                    "Epoch %s: %s improved from %.4f to %.4f. Resetting patience.",
                    epoch,
                    self.monitor,
                    previous_best,
                    self.best_score,
                )
        else:
            self.wait += 1
            if self.verbose:
                logger.info(
                    "Epoch %s: %s did not improve from %.4f. Patience: %s/%s",
                    epoch,
                    self.monitor,
                    self.best_score,
                    self.wait,
                    self.patience,
                )

        if self.wait >= self.patience:
            if self.verbose:
                logger.info(
                    "Epoch %s: Stopping training early as %s did not improve for %s epochs.",
                    epoch,
                    self.monitor,
                    self.patience,
                )
            if self.trainer:
                self.trainer.stop_training = True
