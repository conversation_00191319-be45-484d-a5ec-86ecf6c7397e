# src/train/callbacks/test_image_visualization.py
"""
Test image visualization callback for the ModelTrainer.

This callback generates and saves test image visualizations during training,
showing random samples from the test set with their predictions.
"""

import logging
import os
from typing import Any, Dict, Optional

import matplotlib.pyplot as plt
import torch

from datasets.dataset_utils import extract_augmentation_metadata_from_data_loaders
from src.config.paths import get_run_paths
from src.utils.plots import (
    AugmentationPlotData,
    _save_or_show_plot,
    plot_gaussian_blurs_with_augmentations,
)

from .base import Callback

logger = logging.getLogger(__name__)


class TestImageVisualizationCallback(Callback):
    """
    Callback to generate and save test image visualizations during training.

    This callback collects random test images throughout training and creates
    a comprehensive visualization at the end showing how predictions evolve
    over time.
    """

    def __init__(
        self,
        model_run_uuid: str,
        *,
        num_samples: int = 10,
        collection_frequency: int = 2,
        plot_config: Optional[Dict[str, Any]] = None,
    ):
        """
        Initialize the test image visualization callback.

        Args:
            model_run_uuid: UUID of the model run for path generation
            num_samples: Number of samples to collect predictions for (default: 10)
            collection_frequency: How often to collect predictions (every N epochs, default: 2)
            plot_config: Additional plot configuration (max_samples_to_plot, max_epochs_to_plot,
                plot_filename)
        """
        super().__init__()
        self.model_run_uuid = model_run_uuid
        run_paths = get_run_paths(model_run_uuid)
        self.plots_dir = run_paths.plots
        os.makedirs(self.plots_dir, exist_ok=True)

        # Set up plot configuration with defaults
        plot_config = plot_config or {}

        # Configuration parameters grouped
        self.config = {
            "num_samples": num_samples,
            "collection_frequency": collection_frequency,
            "max_samples_to_plot": min(
                plot_config.get("max_samples_to_plot", 5), num_samples
            ),
            "max_epochs_to_plot": plot_config.get("max_epochs_to_plot", 20),
            "plot_filename": plot_config.get("plot_filename", "test_images.png"),
        }

        # State tracking grouped
        self.collected_data = {
            "images": None,
            "labels": None,
            "original_images": None,
            "original_labels": None,
            "prediction_history": [],
            "last_collection_epoch": -1,
            "has_augmentations": False,
        }

    def on_epoch_end(
        self, epoch: int, logs: Optional[Dict[str, Any]] = None
    ) -> None:  # pylint: disable=unused-argument
        """Collect test image predictions at specified intervals during training."""
        # Quick exit if trainer is not available
        if not self.trainer:
            return

        # Check if we should collect data this epoch
        if (epoch + 1) % self.config["collection_frequency"] == 0 or epoch == 0:
            try:
                self._collect_predictions_safely(epoch)
            except Exception as e:
                logger.warning(
                    "Error collecting predictions at epoch %d: %s",
                    epoch,
                    str(e),
                )

    def on_train_end(
        self, logs: Optional[Dict[str, Any]] = None
    ) -> None:  # pylint: disable=unused-argument
        """Generate comprehensive test image plots at the end of training."""
        # Quick exit if trainer is not available
        if not self.trainer:
            logger.debug(
                "TestImageVisualizationCallback: No trainer available, skipping"
            )
            return

        # Collect final predictions if we haven't collected any yet
        if not self.collected_data["prediction_history"]:
            try:
                final_epoch = getattr(self.trainer, "current_epoch", 0)
                self._collect_predictions_safely(final_epoch)
            except Exception as e:
                logger.warning("Error collecting final predictions: %s", str(e))

        try:
            self._safe_generate_visualizations()
        except Exception as e:
            logger.error(
                "Unexpected error in test image visualization: %s",
                str(e),
                exc_info=True,
            )

    def _collect_predictions_safely(self, epoch: int) -> None:
        """Safely collect test image predictions for the given epoch."""
        # Validate trainer and model
        model = self._validate_and_get_model()
        if model is None:
            return

        # Get test data (only once, reuse the same images)
        if (
            self.collected_data["images"] is None
            or self.collected_data["labels"] is None
        ):
            test_data = self._get_test_data_safely()
            if test_data is None:
                return

            self.collected_data["images"], self.collected_data["labels"] = test_data
            logger.debug(
                "Collected %d test images for visualization",
                len(self.collected_data["images"]),
            )

            # Try to get original images for augmentation comparison
            self._try_collect_original_images()

        # Generate predictions for this epoch
        predictions = self._generate_predictions_safely(
            model, self.collected_data["images"]
        )
        if predictions is None:
            return

        # Store predictions with epoch info
        predictions_cpu = predictions.cpu()
        self.collected_data["prediction_history"].append((epoch, predictions_cpu))
        self.collected_data["last_collection_epoch"] = epoch

        logger.debug("Collected predictions for epoch %d", epoch)

    def _safe_generate_visualizations(self) -> None:
        """Safely generate comprehensive visualizations with error handling."""
        # Check if we have collected data
        if (
            not self.collected_data["prediction_history"]
            or self.collected_data["images"] is None
        ):
            logger.warning("No prediction history available for visualization")
            return

        # Move images and labels to CPU for plotting
        try:
            images_cpu = self.collected_data["images"].cpu()
            labels_cpu = self.collected_data["labels"].cpu()
        except Exception as e:
            logger.error("Error moving tensors to CPU: %s", str(e))
            return

        # Generate comprehensive plot with error handling
        self._create_evolution_plot_safely(
            images_cpu, labels_cpu, self.collected_data["prediction_history"]
        )

        # Generate augmentation comparison plot if we have original images
        if (
            self.collected_data["has_augmentations"]
            and self.collected_data["original_images"] is not None
        ):
            self._create_augmentation_comparison_plot_safely(images_cpu, labels_cpu)

        logger.info("Test image visualization completed successfully")

    def _try_collect_original_images(self) -> None:
        """Try to collect original images for augmentation comparison using multiple strategies."""
        try:
            # Strategy 1: Check for original_dataset attribute (AugmentedDatasetWithOriginals)
            if self._try_collect_from_original_dataset():
                return

            # Strategy 2: Check for augmentation metadata in trainer
            if self._try_collect_from_augmentation_metadata():
                return

            # Strategy 3: Detect augmentations without collecting originals
            if self._try_detect_augmentations():
                return

            logger.debug(
                "No original images could be collected - using augmented images only"
            )

        except Exception as e:
            logger.debug("Error collecting original images: %s", str(e))

    def _try_collect_from_original_dataset(self) -> bool:
        """Try to collect original images from dataset.original_dataset attribute."""
        try:
            if (
                not hasattr(self.trainer, "data_loaders")
                or not self.trainer.data_loaders
            ):
                return False

            test_loader = self.trainer.data_loaders.get("test")
            if test_loader is None:
                return False

            dataset = getattr(test_loader, "dataset", None)
            if dataset is None:
                return False

            # Check if the dataset has an original_dataset attribute
            original_dataset = getattr(dataset, "original_dataset", None)
            if original_dataset is None:
                return False

            # Collect original images corresponding to our test samples
            original_images = []
            original_labels = []

            num_samples = min(self.config["num_samples"], len(original_dataset))

            for i in range(num_samples):
                try:
                    orig_img, orig_label = original_dataset[i]
                    original_images.append(orig_img.unsqueeze(0))
                    original_labels.append(orig_label)
                except Exception as e:
                    logger.warning("Error accessing original image %d: %s", i, str(e))
                    continue

            if original_images:
                self.collected_data["original_images"] = torch.cat(
                    original_images, dim=0
                )
                self.collected_data["original_labels"] = torch.stack(original_labels)
                self.collected_data["has_augmentations"] = True
                logger.debug(
                    "Collected %d original images from original_dataset",
                    len(original_images),
                )
                return True

        except Exception as e:
            logger.debug("Error collecting from original_dataset: %s", str(e))

        return False

    def _try_collect_from_augmentation_metadata(self) -> bool:
        """Try to collect original images using augmentation metadata from trainer."""
        try:
            # Check if trainer has data loaders
            if (
                not hasattr(self.trainer, "data_loaders")
                or not self.trainer.data_loaders
            ):
                return False

            # Use centralized function to extract augmentation metadata
            augmentation_metadata = extract_augmentation_metadata_from_data_loaders(
                self.trainer.data_loaders
            )

            # Check if any split has augmentation metadata
            has_augmentations = any(
                metadata is not None and metadata.get("augmentation_config")
                for metadata in augmentation_metadata.values()
            )

            if not has_augmentations:
                return False

            # If we have augmentation metadata, we know augmentations are applied
            # Try to get the raw data and apply only the basic transforms (no augmentations)
            test_loader = self.trainer.data_loaders.get("test")
            if test_loader is None:
                return False

            # Get the dataset and check if we can access raw data
            dataset = getattr(test_loader, "dataset", None)
            if dataset is None:
                return False

            # Try to access raw images and labels from the dataset
            raw_images = getattr(dataset, "images", None)
            raw_labels = getattr(dataset, "labels", None)

            if raw_images is not None and raw_labels is not None:
                # Use the same samples we collected for augmented images
                num_samples = min(self.config["num_samples"], len(raw_images))

                # Create original images (just the raw data without augmentations)
                original_images = []
                original_labels = []

                for i in range(num_samples):
                    try:
                        # Get raw image and label
                        orig_img = raw_images[i]
                        orig_label = raw_labels[i]

                        # Ensure proper tensor format
                        if not isinstance(orig_img, torch.Tensor):
                            orig_img = torch.tensor(orig_img)
                        if not isinstance(orig_label, torch.Tensor):
                            orig_label = torch.tensor(orig_label)

                        original_images.append(orig_img.unsqueeze(0))
                        original_labels.append(orig_label)

                    except Exception as e:
                        logger.warning("Error processing raw image %d: %s", i, str(e))
                        continue

                if original_images:
                    self.collected_data["original_images"] = torch.cat(
                        original_images, dim=0
                    )
                    self.collected_data["original_labels"] = torch.stack(
                        original_labels
                    )
                    self.collected_data["has_augmentations"] = True
                    logger.debug(
                        "Collected %d original images from raw dataset",
                        len(original_images),
                    )
                    return True

        except Exception as e:
            logger.debug("Error collecting from augmentation metadata: %s", str(e))

        return False

    def _try_detect_augmentations(self) -> bool:
        """Try to detect if augmentations are being applied without collecting original images."""
        try:
            if not hasattr(self.trainer, "data_loaders"):
                return False

            # Use centralized function to extract augmentation metadata
            augmentation_metadata = extract_augmentation_metadata_from_data_loaders(
                self.trainer.data_loaders
            )

            # Check if any split has augmentation metadata with actual augmentations
            total_augmentations = 0
            for metadata in augmentation_metadata.values():
                if metadata and metadata.get("augmentation_config"):
                    augmentations = metadata.get("augmentation_config", [])
                    total_augmentations += len(augmentations)

            if total_augmentations > 0:
                # We know augmentations are applied, but we can't get originals
                # Set the flag so we show "Augmented" labels instead of "Test"
                self.collected_data["has_augmentations"] = True
                logger.debug(
                    "Detected %d augmentations but cannot collect original images",
                    total_augmentations,
                )
                return True

        except Exception as e:
            logger.debug("Error detecting augmentations: %s", str(e))

        return False

    def _create_augmentation_comparison_plot_safely(self, images, labels) -> None:
        """Create and save the augmentation comparison plot with error handling."""
        try:
            self._create_augmentation_comparison_plot(images, labels)
        except Exception as e:
            logger.error("Error creating augmentation comparison plot: %s", str(e))

    def _create_augmentation_comparison_plot(self, images, labels):
        """Create and save the augmentation comparison plot."""
        # Get the final predictions for the comparison plot
        if not self.collected_data["prediction_history"]:
            logger.warning(
                "No prediction history available for augmentation comparison"
            )
            return

        # Use the final epoch predictions
        _, final_predictions = self.collected_data["prediction_history"][-1]

        # Limit to 10 samples for the augmentation comparison (standard format)
        num_samples_for_aug = min(
            10, len(images), len(self.collected_data["original_images"])
        )

        # Move original images to CPU
        try:
            original_images_cpu = self.collected_data["original_images"][
                :num_samples_for_aug
            ].cpu()
            original_labels_cpu = self.collected_data["original_labels"][
                :num_samples_for_aug
            ].cpu()
        except Exception as e:
            logger.error("Error moving original images to CPU: %s", str(e))
            return

        # Create the augmentation plot data
        try:
            plot_data = AugmentationPlotData(
                y_true=labels[:num_samples_for_aug],
                y_pred=final_predictions[:num_samples_for_aug],
                augmented_images=images[:num_samples_for_aug],
                original_images=original_images_cpu,
                original_labels=original_labels_cpu,
            )

            # Create the augmentation comparison plot
            aug_output_path = os.path.join(
                self.plots_dir, "test_images_augmentation_comparison.png"
            )
            plot_gaussian_blurs_with_augmentations(
                plot_data,
                output_path=aug_output_path,
            )

            logger.debug("Augmentation comparison plot saved to: %s", aug_output_path)

        except ImportError as e:
            logger.warning(
                "Could not import augmentation plotting functions: %s", str(e)
            )
        except Exception as e:
            logger.error("Error creating augmentation comparison plot: %s", str(e))

    def _plot_single_image(self, ax, image_tensor, title):
        """Helper method to plot a single image on the given axis."""
        # Handle different image formats (grayscale vs RGB)
        if image_tensor.shape[0] == 1:  # Grayscale
            img = torch.squeeze(image_tensor[0, :, :]).detach()
            cmap = "gray"
        elif image_tensor.shape[0] == 3:  # RGB
            # Convert from CHW to HWC for matplotlib
            img = image_tensor.permute(1, 2, 0).detach()
            cmap = None
        else:
            # Fallback: use first channel
            img = torch.squeeze(image_tensor[0, :, :]).detach()
            cmap = "gray"

        # Plot the image
        if cmap:
            ax.imshow(img, cmap=cmap)
        else:
            # For RGB, ensure values are in [0, 1] range
            img = torch.clamp(img, 0, 1)
            ax.imshow(img)

        ax.set_title(title, fontsize=10)
        ax.set_xticks([])
        ax.set_yticks([])

    def _create_evolution_plot_safely(self, images, labels, prediction_history) -> None:
        """Create and save the prediction evolution plot with error handling."""
        try:
            self._create_evolution_plot(images, labels, prediction_history)
        except Exception as e:
            logger.error("Error creating evolution plot: %s", str(e))

    def _validate_and_get_model(self):
        """Validate trainer and return model if available."""
        if not hasattr(self.trainer, "components") or not self.trainer.components:
            logger.warning("Trainer components not available")
            return None

        model = self.trainer.components.model
        if model is None:
            logger.warning("Model not available in trainer components")
            return None

        return model

    def _create_evolution_plot(self, images, labels, prediction_history):
        """Create and save the prediction evolution visualization plot."""
        output_path = os.path.join(self.plots_dir, self.config["plot_filename"])
        logger.debug("Saving prediction evolution plot to: %s", output_path)

        try:
            # Use the enhanced plotting function
            self._plot_prediction_evolution(
                images=images,
                true_labels=labels,
                prediction_history=prediction_history,
                output_path=output_path,
            )
            logger.debug("Prediction evolution plot saved successfully")
        except Exception as e:
            logger.error("Error creating prediction evolution plot: %s", str(e))

    def _get_test_data_safely(self) -> Optional[tuple]:
        """Safely get test data from the trainer."""
        # Check if test loader is available
        if not hasattr(self.trainer, "data_loaders") or not self.trainer.data_loaders:
            logger.warning("Data loaders not available in trainer")
            return None

        test_loader = self.trainer.data_loaders.get("test")
        if test_loader is None:
            logger.warning("Test loader not found in trainer data loaders")
            return None

        logger.debug("Found test loader with %d batches", len(test_loader))

        # Get a batch from test loader
        try:
            x_batch, y_batch = next(iter(test_loader))

            # Move to device safely
            device = self.trainer.device
            x_batch = x_batch.to(device)
            y_batch = y_batch.to(device)

            logger.debug("Got batch with shape: %s", x_batch.shape)

            # Limit to specified number of samples for visualization
            num_samples = min(self.config["num_samples"], len(x_batch))
            x_sample = x_batch[:num_samples]
            y_sample = y_batch[:num_samples]

            return x_sample, y_sample
        except StopIteration:
            logger.warning("Test loader is empty (no batches)")
            return None
        except Exception as e:
            logger.error("Error getting batch from test loader: %s", str(e))
            return None

    def _generate_predictions_safely(self, model, x_sample) -> Optional[torch.Tensor]:
        """Safely generate model predictions for the test samples."""
        try:
            # Set model to evaluation mode
            model.eval()

            # Generate predictions
            with torch.no_grad():
                predictions = model(x_sample)

            logger.debug("Generated predictions with shape: %s", predictions.shape)
            return predictions

        except Exception as e:
            logger.error("Error generating predictions: %s", str(e))
            return None

    def _plot_prediction_evolution(
        self, images, true_labels, prediction_history, output_path=None
    ):
        """Create a comprehensive plot showing original vs augmented images
        with prediction evolution."""
        plot_config = self._prepare_plot_config(images, prediction_history)
        if plot_config is None:
            return

        fig, _ = self._create_figure_and_title(plot_config)
        self._plot_samples_and_evolution(fig, images, true_labels, plot_config)
        self._finalize_plot(fig, output_path)

    def _prepare_plot_config(self, images, prediction_history):
        """Prepare configuration for the prediction evolution plot."""
        num_samples_to_plot = min(self.config["max_samples_to_plot"], len(images))

        # Limit epochs to plot (take most recent epochs if we have too many)
        limited_history = prediction_history
        if len(prediction_history) > self.config["max_epochs_to_plot"]:
            limited_history = prediction_history[-self.config["max_epochs_to_plot"] :]

        num_epochs = len(limited_history)

        if num_epochs == 0:
            logger.warning("No prediction history to plot")
            return None

        has_originals = (
            self.collected_data["has_augmentations"]
            and self.collected_data["original_images"] is not None
            and len(self.collected_data["original_images"]) >= num_samples_to_plot
        )

        return {
            "num_samples_to_plot": num_samples_to_plot,
            "limited_history": limited_history,
            "num_epochs": num_epochs,
            "has_originals": has_originals,
            "total_epochs": len(prediction_history),
        }

    def _create_figure_and_title(self, plot_config):
        """Create figure and title based on plot configuration."""
        num_samples_to_plot = plot_config["num_samples_to_plot"]
        has_originals = plot_config["has_originals"]
        limited_history = plot_config["limited_history"]
        total_epochs = plot_config["total_epochs"]

        if has_originals:
            # Layout: Original | Augmented | Evolution Plot
            fig = plt.figure(figsize=(24, max(8, num_samples_to_plot * 2)))
            title = (
                f"Original vs Augmented Images with Prediction Evolution "
                f"({num_samples_to_plot} samples)"
            )
        else:
            # Layout: Augmented | Evolution Plot
            fig = plt.figure(figsize=(20, max(8, num_samples_to_plot * 2)))
            title = f"Test Image Prediction Evolution ({num_samples_to_plot} samples)"

        # Add epoch information to title
        epochs_str = ", ".join([str(epoch) for epoch, _ in limited_history])
        if total_epochs > self.config["max_epochs_to_plot"]:
            title += (
                f"\nShowing last {self.config['max_epochs_to_plot']}/{total_epochs} "
                f"epochs: {epochs_str}"
            )
        else:
            title += f"\nEpochs: {epochs_str}"

        fig.suptitle(title, fontsize=14, y=0.98)
        return fig, title

    def _plot_samples_and_evolution(self, fig, images, true_labels, plot_config):
        """Plot samples and their prediction evolution."""
        num_samples_to_plot = plot_config["num_samples_to_plot"]
        has_originals = plot_config["has_originals"]
        limited_history = plot_config["limited_history"]

        for sample_idx in range(num_samples_to_plot):
            row_data = {
                "sample_idx": sample_idx,
                "images": images,
                "true_labels": true_labels,
                "limited_history": limited_history,
                "num_samples_to_plot": num_samples_to_plot,
                "has_originals": has_originals,
                "fig": fig,
            }
            self._plot_single_sample_row(row_data)

    def _plot_single_sample_row(self, row_data):
        """Plot a single sample row with images and evolution."""
        sample_idx = row_data["sample_idx"]
        images = row_data["images"]
        true_labels = row_data["true_labels"]
        limited_history = row_data["limited_history"]
        num_samples_to_plot = row_data["num_samples_to_plot"]
        has_originals = row_data["has_originals"]
        fig = row_data["fig"]

        col_idx = 1

        # First column: Original image (if available)
        if has_originals:
            ax_orig = fig.add_subplot(num_samples_to_plot, 3, sample_idx * 3 + col_idx)
            self._plot_single_image(
                ax_orig,
                self.collected_data["original_images"][sample_idx].cpu(),
                f"Original {sample_idx}\nTrue: "
                f"{int(self.collected_data['original_labels'][sample_idx].item())}",
            )
            col_idx += 1

        # Second column: Augmented/Test image
        ax_aug = fig.add_subplot(
            num_samples_to_plot,
            3 if has_originals else 2,
            sample_idx * (3 if has_originals else 2) + col_idx,
        )
        true_label = int(true_labels[sample_idx].item())
        title = f"{'Augmented' if has_originals else 'Test'} {sample_idx}\nTrue: {true_label}"
        self._plot_single_image(ax_aug, images[sample_idx], title)
        col_idx += 1

        # Last column: Prediction evolution
        ax_pred = fig.add_subplot(
            num_samples_to_plot,
            3 if has_originals else 2,
            sample_idx * (3 if has_originals else 2) + col_idx,
        )
        self._plot_prediction_evolution_for_sample(
            ax_pred, sample_idx, true_label, limited_history
        )

    def _plot_prediction_evolution_for_sample(
        self, ax_pred, sample_idx, true_label, limited_history
    ):
        """Plot prediction evolution for a single sample."""
        epochs = []
        predictions = []
        confidences = []

        for epoch, pred_batch in limited_history:
            epochs.append(epoch)
            pred_value = pred_batch[sample_idx]

            # Handle different prediction formats
            if pred_value.numel() == 1:  # Binary classification
                raw_pred = pred_value.item()
                confidence = torch.sigmoid(pred_value).item()
                pred_label = int(raw_pred > 0.5)
            else:  # Multi-class
                pred_label = int(torch.argmax(pred_value).item())
                confidence = torch.softmax(pred_value, dim=0).max().item()

            predictions.append(pred_label)
            confidences.append(confidence)

        # Plot prediction evolution
        ax_pred.plot(
            epochs,
            predictions,
            "bo-",
            label="Predicted Label",
            linewidth=2,
            markersize=6,
        )
        ax_pred.axhline(
            y=true_label,
            color="r",
            linestyle="--",
            label=f"True Label ({true_label})",
            linewidth=2,
        )

        # Add confidence as secondary y-axis
        ax_conf = ax_pred.twinx()
        ax_conf.plot(
            epochs,
            confidences,
            "go-",
            alpha=0.7,
            label="Confidence",
            linewidth=1,
            markersize=4,
        )
        ax_conf.set_ylabel("Confidence", color="g", fontsize=9)
        ax_conf.set_ylim(0, 1)

        ax_pred.set_xlabel("Epoch", fontsize=9)
        ax_pred.set_ylabel("Predicted Label", fontsize=9)
        ax_pred.set_title("Prediction Evolution", fontsize=10)
        ax_pred.legend(loc="upper left", fontsize=8)
        ax_conf.legend(loc="upper right", fontsize=8)
        ax_pred.grid(True, alpha=0.3)

    def _finalize_plot(self, fig, output_path):
        """Finalize and save the plot."""
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)

        _save_or_show_plot(fig, output_path)
