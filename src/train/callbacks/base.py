# src/train/callbacks/base.py
"""
Base callback class for the ModelTrainer callback system.
"""

from __future__ import annotations

from abc import ABC
from typing import TYPE_CHECKING, Any, Dict, Generic, Optional, TypeVar

# Define our own base callback class to avoid circular imports
T = TypeVar("T")


class BaseCallback(Generic[T], ABC):
    """Base callback class to avoid circular imports."""

    def __init__(self) -> None:
        """Initialize the callback."""
        self.owner: Optional[T] = None

    def set_owner(self, owner: T) -> None:
        """Set the owner instance for this callback."""
        self.owner = owner


if TYPE_CHECKING:
    from src.train.trainer import ModelTrainer


class Callback(BaseCallback["ModelTrainer"]):
    """
    Abstract base class for creating callbacks.

    Callbacks can be used to customize the behavior of the ModelTrainer during training.
    Subclasses can override the methods for the events they are interested in.
    """

    def __init__(self) -> None:
        super().__init__()
        self.trainer: Optional["ModelTrainer"] = None

    def set_trainer(self, trainer: ModelTrainer) -> None:
        """Sets the trainer instance for the callback."""
        self.trainer = trainer
        self.set_owner(trainer)

    def on_train_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of training."""

    def on_train_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of training."""

    def on_epoch_begin(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of an epoch."""

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of an epoch."""

    def on_batch_begin(self, batch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of a training batch."""

    def on_batch_end(self, batch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of a training batch."""

    def on_validation_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of a validation run."""

    def on_validation_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of a validation run."""
