"""
Augmented Dataset for applying transforms during training and inference.

This module provides a custom PyTorch Dataset class that applies augmentation
transforms to images, replacing the current TensorDataset usage in the training pipeline.
"""

from typing import Optional, Tuple

import torch
from torch.utils.data import Dataset
from torchvision import transforms


class AugmentedDataset(Dataset):
    """
    Custom dataset that applies augmentation transforms to images.

    This dataset class is designed to work with the augmentations module,
    applying different transform pipelines for training and inference while
    maintaining compatibility with the existing training infrastructure.
    """

    def __init__(
        self,
        images: torch.Tensor,
        labels: torch.Tensor,
        transform: Optional[transforms.Compose] = None,
        target_transform: Optional[transforms.Compose] = None,
    ):
        """
        Initialize the augmented dataset.

        Args:
            images: Tensor of images with shape (N, C, H, W) or (N, H, W)
            labels: Tensor of labels with shape (N,) or (N, 1)
            transform: Optional transform pipeline to apply to images
            target_transform: Optional transform pipeline to apply to labels
        """
        self.images = images
        self.labels = labels
        self.transform = transform
        self.target_transform = target_transform

        # Validate input shapes
        self._validate_inputs()

    def _validate_inputs(self) -> None:
        """Validate that images and labels have compatible shapes."""
        if len(self.images) != len(self.labels):
            raise ValueError(
                f"Images and labels must have the same length. "
                f"Got {len(self.images)} images and {len(self.labels)} labels."
            )

        # Ensure labels are in the correct shape
        if self.labels.dim() == 2 and self.labels.size(1) == 1:
            # Convert (N, 1) to (N,) for compatibility
            self.labels = self.labels.squeeze(1)
        elif self.labels.dim() > 1:
            raise ValueError(
                f"Labels must be 1D or 2D with shape (N,) or (N, 1). "
                f"Got shape {self.labels.shape}."
            )

    def __len__(self) -> int:
        """Return the number of samples in the dataset."""
        return len(self.images)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get a sample from the dataset.

        Args:
            idx: Index of the sample to retrieve

        Returns:
            Tuple of (image, label) after applying transforms
        """
        # Get the raw image and label
        image = self.images[idx]
        label = self.labels[idx]

        # Apply image transforms if provided
        if self.transform is not None:
            image = self.transform(image)

        # Apply label transforms if provided
        if self.target_transform is not None:
            label = self.target_transform(label)

        return image, label

    def get_raw_sample(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get a raw sample without applying transforms.

        Args:
            idx: Index of the sample to retrieve

        Returns:
            Tuple of (raw_image, raw_label) without transforms
        """
        return self.images[idx], self.labels[idx]

    def update_transform(self, transform: Optional[transforms.Compose]) -> None:
        """
        Update the transform pipeline for this dataset.

        This is useful for switching between training and inference modes
        or updating augmentation parameters during training.

        Args:
            transform: New transform pipeline to apply
        """
        self.transform = transform

    def get_dataset_info(self) -> dict:
        """
        Get information about the dataset.

        Returns:
            Dictionary containing dataset statistics and information
        """
        return {
            "num_samples": len(self),
            "image_shape": tuple(self.images[0].shape),
            "label_shape": (
                tuple(self.labels[0].shape) if self.labels[0].dim() > 0 else ()
            ),
            "has_transform": self.transform is not None,
            "has_target_transform": self.target_transform is not None,
            "image_dtype": str(self.images.dtype),
            "label_dtype": str(self.labels.dtype),
        }


class AugmentedDatasetFactory:
    """
    Factory class for creating AugmentedDataset instances with proper configuration.

    This factory provides convenient methods for creating datasets with
    appropriate transforms for different use cases.
    """

    @classmethod
    def create_training_dataset(
        cls,
        images: torch.Tensor,
        labels: torch.Tensor,
        train_transform: Optional[transforms.Compose] = None,
    ) -> AugmentedDataset:
        """
        Create a dataset configured for training.

        Args:
            images: Training images tensor
            labels: Training labels tensor
            train_transform: Transform pipeline for training (with augmentations)

        Returns:
            AugmentedDataset configured for training
        """
        return AugmentedDataset(
            images=images,
            labels=labels,
            transform=train_transform,
        )

    @classmethod
    def create_validation_dataset(
        cls,
        images: torch.Tensor,
        labels: torch.Tensor,
        val_transform: Optional[transforms.Compose] = None,
    ) -> AugmentedDataset:
        """
        Create a dataset configured for validation/testing.

        Args:
            images: Validation images tensor
            labels: Validation labels tensor
            val_transform: Transform pipeline for validation (deterministic only)

        Returns:
            AugmentedDataset configured for validation
        """
        return AugmentedDataset(
            images=images,
            labels=labels,
            transform=val_transform,
        )

    @classmethod
    def create_inference_dataset(
        cls,
        images: torch.Tensor,
        inference_transform: Optional[transforms.Compose] = None,
    ) -> AugmentedDataset:
        """
        Create a dataset configured for inference (no labels required).

        Args:
            images: Images tensor for inference
            inference_transform: Transform pipeline for inference

        Returns:
            AugmentedDataset configured for inference
        """
        # Create dummy labels for inference (not used but required by Dataset)
        dummy_labels = torch.zeros(len(images), dtype=torch.long)

        return AugmentedDataset(
            images=images,
            labels=dummy_labels,
            transform=inference_transform,
        )
