"""
Dataset utility functions for extracting information from data loaders.

This module provides common utilities for working with datasets and data loaders,
particularly for extracting label information and class counts.
"""

from typing import Any, Dict, Optional

from torch.utils.data import DataLoader


class DatasetExtractionError(Exception):
    """Exception raised when dataset information extraction fails."""


def extract_dataset_from_data_loaders(data_loaders: Dict[str, DataLoader]):
    """
    Extract the underlying dataset from data loaders.

    Args:
        data_loaders: Dictionary of data loaders

    Returns:
        The underlying dataset (unwrapped from any augmentation wrappers)

    Raises:
        DatasetExtractionError: If no training data loader found
    """
    if "train" not in data_loaders:
        raise DatasetExtractionError("Training data loader not found in data_loaders")

    train_loader = data_loaders["train"]
    dataset = train_loader.dataset

    # Handle AugmentedDataset wrapper
    if hasattr(dataset, "base_dataset"):
        dataset = dataset.base_dataset

    return dataset


def extract_num_classes_from_data_loaders(data_loaders: Dict[str, DataLoader]) -> int:
    """
    Extract the number of classes from data loaders.

    Args:
        data_loaders: Dictionary of data loaders

    Returns:
        Number of classes detected from the training dataset

    Raises:
        DatasetExtractionError: If unable to determine classes
    """
    dataset = extract_dataset_from_data_loaders(data_loaders)

    # Check if it's an ImageDataset
    if hasattr(dataset, "get_num_classes"):
        return dataset.get_num_classes()

    # Fallback: try to determine from dataset attributes
    if hasattr(dataset, "coin_side_to_label"):
        return len(dataset.coin_side_to_label)

    # Fallback for simple datasets: sample some data to determine unique labels
    try:
        train_loader = data_loaders["train"]
        unique_labels = set()

        # Sample a few batches to determine unique labels
        sample_count = 0
        for _, labels in train_loader:
            if hasattr(labels, "numpy"):
                unique_labels.update(labels.numpy().tolist())
            else:
                unique_labels.update(labels.tolist())

            sample_count += len(labels)
            # Stop after sampling enough data or reaching reasonable limit
            if sample_count >= 1000 or len(unique_labels) > 10:
                break

        if unique_labels:
            return len(unique_labels)
    except Exception:
        pass

    raise DatasetExtractionError(
        "Unable to determine number of classes from dataset. "
        "Dataset must be ImageDataset or have coin_side_to_label attribute."
    )


def extract_label_info_from_data_loaders(
    data_loaders: Dict[str, DataLoader],
) -> Dict[str, Any]:
    """
    Extract comprehensive label information from data loaders.

    Args:
        data_loaders: Dictionary of data loaders

    Returns:
        Dictionary containing label mapping, class names, and statistics

    Raises:
        DatasetExtractionError: If unable to extract label information
    """
    dataset = extract_dataset_from_data_loaders(data_loaders)

    # Check if it's an ImageDataset
    if hasattr(dataset, "get_label_mapping") and hasattr(dataset, "get_class_names"):
        return {
            "num_classes": dataset.get_num_classes(),
            "label_mapping": dataset.get_label_mapping(),
            "class_names": dataset.get_class_names(),
            "dataset_info": dataset.get_dataset_info(),
        }

    # Fallback for simple datasets (like TensorDataset) - try to infer from data
    try:
        num_classes = extract_num_classes_from_data_loaders(data_loaders)
        return {
            "num_classes": num_classes,
            "label_mapping": {str(i): i for i in range(num_classes)},
            "class_names": [f"class_{i}" for i in range(num_classes)],
            "dataset_info": {
                "dataset_type": type(dataset).__name__,
                "num_samples": len(dataset),
            },
        }
    except DatasetExtractionError:
        pass

    raise DatasetExtractionError(
        "Unable to extract label information from dataset. "
        "Dataset must be ImageDataset with label extraction methods."
    )


def extract_augmentation_metadata_from_data_loaders(
    data_loaders: Dict[str, DataLoader],
) -> Dict[str, Optional[Dict[str, Any]]]:
    """
    Extract augmentation metadata from data loaders.

    This function centralizes the logic for extracting augmentation metadata,
    first checking for global metadata in the data_loaders dict, then falling
    back to individual dataset metadata.

    Args:
        data_loaders: Dictionary of data loaders, potentially containing
                     '_augmentation_metadata' key with global metadata

    Returns:
        Dictionary mapping split names to their augmentation metadata.
        Each value is either a dict with augmentation info or None if no
        augmentation metadata is found for that split.
    """
    # First, check if there's global augmentation metadata in data_loaders
    if "_augmentation_metadata" in data_loaders:
        global_metadata = data_loaders["_augmentation_metadata"]

        # Convert global metadata to per-split format
        if isinstance(global_metadata, dict) and "augmentations" in global_metadata:
            # Create metadata for each split using the global augmentation config
            result = {}
            for split_name, data_loader in data_loaders.items():
                if split_name.startswith("_"):  # Skip metadata keys
                    continue
                result[split_name] = {
                    "augmentation_config": global_metadata.get("augmentations", []),
                    "transforms": global_metadata.get(f"{split_name}_transforms"),
                }
            return result

    # Fallback: extract from individual datasets
    augmentation_metadata = {}
    for split_name, data_loader in data_loaders.items():
        if split_name.startswith("_"):  # Skip metadata keys
            continue

        try:
            dataset = data_loader.dataset

            # Check if dataset has augmentation metadata
            if hasattr(dataset, "get_augmentation_metadata"):
                metadata = dataset.get_augmentation_metadata()
                augmentation_metadata[split_name] = metadata
            else:
                augmentation_metadata[split_name] = None

        except Exception:
            # If extraction fails for any reason, set to None
            augmentation_metadata[split_name] = None

    return augmentation_metadata
