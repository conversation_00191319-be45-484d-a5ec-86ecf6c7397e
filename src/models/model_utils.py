"""
Utility functions for model creation and manipulation.

This module provides helper functions used across different model-related
modules to promote code reuse and single responsibility.
"""

from typing import Any, Dict, Optional, Type

import torch
from torch import nn, optim
from torch.optim import lr_scheduler

OPTIMIZERS: Dict[str, Type[optim.Optimizer]] = {
    "adam": optim.Adam,
    "adamw": optim.AdamW,
    "sgd": optim.SGD,
}

SCHEDULERS: Dict[str, Type[lr_scheduler._LRScheduler]] = {
    "step": lr_scheduler.StepLR,
    "cosine": lr_scheduler.CosineAnnealingLR,
    "plateau": lr_scheduler.ReduceLROnPlateau,
}


class BCEWithLogitsLossWrapper(nn.Module):
    """
    Wrapper for BCEWithLogitsLoss that handles shape mismatches between model output and labels.

    This wrapper automatically handles common shape issues in binary classification:
    - Squeezes model output if it has shape (N, 1) to match target shape (N,)
    - Squeezes target if it has shape (N, 1) to match input shape (N,)
    - Ensures target is float type for compatibility with BCEWithLogitsLoss
    """

    def __init__(self, pos_weight=None):
        super().__init__()
        self.loss_fn = nn.BCEWithLogitsLoss(pos_weight=pos_weight)

    def forward(self, model_input, target):
        """Forward pass for BCE loss with shape handling."""
        # Squeeze model output to match target shape
        if model_input.dim() > 1 and model_input.size(1) == 1:
            model_input = model_input.squeeze(1)

        # Squeeze target to match input shape (handle both (N,) and (N,1) cases)
        if target.dim() > 1 and target.size(1) == 1:
            target = target.squeeze(1)

        # Ensure target is float
        if target.dtype != torch.float32:
            target = target.float()
        return self.loss_fn(model_input, target)


LOSS_FUNCTIONS: Dict[str, Type[nn.Module]] = {
    "bce": nn.BCEWithLogitsLoss,
    "bce_wrapper": BCEWithLogitsLossWrapper,
    "ce": nn.CrossEntropyLoss,
    "mse": nn.MSELoss,
}


def get_activation(activation: Optional[str]) -> nn.Module:
    """Returns an activation function module based on the name provided."""
    if activation is None or activation.lower() == "relu":
        return nn.ReLU(inplace=True)
    if activation.lower() == "leakyrelu":
        return nn.LeakyReLU(0.2, inplace=True)
    # Add other activations as needed, e.g., 'sigmoid', 'tanh'
    raise ValueError(f"Unsupported activation function: {activation}")


def create_loss_function(loss_params: Dict[str, Any]) -> nn.Module:
    """Creates a loss function based on the provided parameters.

    Args:
        loss_params: Dictionary containing loss function parameters:
            - type: Loss function type ('bce', 'ce', 'mse')
            - config: Optional configuration parameters:
                - reduction: Optional reduction method ('mean', 'sum', 'none')
                - weight: Optional weight parameter for BCE loss
                - pos_weight: Optional positive weight parameter for BCE loss
                - ignore_index: Optional ignore index for CrossEntropy loss
                - label_smoothing: Optional label smoothing for CrossEntropy loss

    Returns:
        PyTorch loss function module

    Raises:
        ValueError: If loss type is not supported
    """
    # Extract loss type, default to BCE
    loss_type = loss_params.get("type", "bce").lower()

    # Validate loss type
    if loss_type not in LOSS_FUNCTIONS:
        raise ValueError(f"Unsupported loss function: {loss_type}")

    # Get the loss class
    loss_class = LOSS_FUNCTIONS[loss_type]

    # Extract configuration parameters if available
    config = loss_params.get("config", {})
    kwargs = {}

    # Common parameters
    if "reduction" in config:
        kwargs["reduction"] = config["reduction"]

    # BCE specific parameters
    if loss_type == "bce":
        if "weight" in config:
            kwargs["weight"] = config["weight"]
        if "pos_weight" in config:
            kwargs["pos_weight"] = config["pos_weight"]

    # CrossEntropy specific parameters
    elif loss_type == "ce":
        if "ignore_index" in config:
            kwargs["ignore_index"] = config["ignore_index"]
        if "label_smoothing" in config:
            kwargs["label_smoothing"] = config["label_smoothing"]

    # Create and return the loss function with appropriate parameters
    return loss_class(**kwargs)


def create_optimizer(
    optimizer_params: Dict[str, Any], model_parameters
) -> optim.Optimizer:
    """Creates an optimizer based on the provided parameters."""
    opt_type = optimizer_params.get("type", "adam").lower()
    if opt_type not in OPTIMIZERS:
        raise ValueError(f"Unsupported optimizer: {opt_type}")

    lr = float(optimizer_params.get("learning_rate", 1e-3))
    optimizer_class = OPTIMIZERS[opt_type]
    return optimizer_class(model_parameters, lr=lr)


def create_scheduler(
    scheduler_params: Dict[str, Any], optimizer: optim.Optimizer
) -> Optional[lr_scheduler._LRScheduler]:
    """Creates a learning rate scheduler based on the provided parameters."""
    sched_type = scheduler_params.get("type")
    if not sched_type:
        return None

    sched_type = sched_type.lower()
    if sched_type not in SCHEDULERS:
        raise ValueError(f"Unsupported scheduler: {sched_type}")

    scheduler_class = SCHEDULERS[sched_type]
    scheduler_args = {k: v for k, v in scheduler_params.items() if k != "type"}
    return scheduler_class(optimizer, **scheduler_args)
