"""
Model validation utilities for ensuring model architecture compatibility with datasets.

This module provides utilities to validate that model architectures are compatible
with the dataset they will be trained on, particularly ensuring the output layer
size matches the number of classes in the dataset.
"""

import logging
from typing import Any, Dict

import torch
from torch import nn
from torch.utils.data import DataLoader

from datasets.dataset_utils import (
    DatasetExtractionError,
    extract_label_info_from_data_loaders,
    extract_num_classes_from_data_loaders,
)
from utils.model_utils import has_conv2d_layers, has_linear_layers

logger = logging.getLogger(__name__)


class ModelValidationError(Exception):
    """Exception raised when model validation fails."""


class ModelValidator:
    """
    Validator for ensuring model compatibility with datasets.

    This class provides methods to validate that models are properly configured
    for the datasets they will be trained on, with automatic detection of
    mismatches between model output size and dataset class count.
    """

    @classmethod
    def analyze_model_capabilities(cls, model: nn.Module) -> Dict[str, Any]:
        """
        Analyze model capabilities and properties.

        Args:
            model: PyTorch model to analyze

        Returns:
            Dictionary containing model capabilities and properties
        """
        capabilities = {
            "supports_feature_extraction": has_conv2d_layers(model),
            "output_size": None,
            "has_conv_layers": has_conv2d_layers(model),
            "has_linear_layers": has_linear_layers(model),
            "model_type": type(model).__name__,
        }

        # Try to detect output size
        try:
            capabilities["output_size"] = cls._detect_model_output_size(model)
        except ModelValidationError:
            # Output size detection failed, but that's okay for capability analysis
            pass

        return capabilities

    @classmethod
    def validate_model_dataset_compatibility(
        cls,
        model: nn.Module,
        data_loaders: Dict[str, DataLoader],
        architecture_params: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Validate that a model is compatible with the dataset.

        Args:
            model: PyTorch model to validate
            data_loaders: Dictionary of data loaders
            architecture_params: Architecture parameters used to create the model

        Returns:
            Dictionary containing validation results and detected information

        Raises:
            ModelValidationError: If validation fails
        """
        try:
            # Extract dataset information
            label_info = extract_label_info_from_data_loaders(data_loaders)
            dataset_num_classes = label_info["num_classes"]

            # Detect model output size
            model_output_size = cls._detect_model_output_size(model)

            # Validate compatibility
            is_compatible = cls._validate_output_size_compatibility(
                model_output_size, dataset_num_classes
            )

            validation_result = {
                "is_compatible": is_compatible,
                "dataset_num_classes": dataset_num_classes,
                "model_output_size": model_output_size,
                "label_info": label_info,
                "architecture_params": architecture_params,
            }

            if not is_compatible:
                error_msg = (
                    f"Model output size ({model_output_size}) does not match "
                    f"dataset classes ({dataset_num_classes}). "
                    f"Architecture: {architecture_params.get('name', 'Unknown')}"
                )
                logger.error(error_msg)
                raise ModelValidationError(error_msg)

            logger.info(
                "Model validation successful: %d classes, output size %d",
                dataset_num_classes,
                model_output_size,
            )

            return validation_result

        except Exception as e:
            if isinstance(e, ModelValidationError):
                raise
            raise ModelValidationError(f"Model validation failed: {str(e)}") from e

    @classmethod
    def _detect_model_output_size(cls, model: nn.Module) -> int:
        """
        Detect the output size of a model.

        Args:
            model: PyTorch model

        Returns:
            Number of output features/classes

        Raises:
            ModelValidationError: If unable to detect output size
        """
        # Try direct layer inspection first
        output_size = cls._detect_from_layers(model)
        if output_size is not None:
            return output_size

        # Try forward pass inference as fallback
        output_size = cls._detect_from_forward_pass(model)
        if output_size is not None:
            return output_size

        raise ModelValidationError(
            "Unable to detect model output size. Model must have a detectable "
            "final linear layer (fc, classifier) or support forward pass inference."
        )

    @classmethod
    def _detect_from_layers(cls, model: nn.Module) -> int:
        """Detect output size from model layers."""
        # For Sequential models (like dynamic CNN)
        if isinstance(model, nn.Sequential):
            for layer in reversed(model):
                if isinstance(layer, nn.Linear):
                    return layer.out_features

        # For standard PyTorch models
        if hasattr(model, "fc") and isinstance(model.fc, nn.Linear):
            return model.fc.out_features

        if hasattr(model, "classifier"):
            if isinstance(model.classifier, nn.Linear):
                return model.classifier.out_features
            if isinstance(model.classifier, nn.Sequential):
                for layer in reversed(model.classifier):
                    if isinstance(layer, nn.Linear):
                        return layer.out_features

        return None

    @classmethod
    def _detect_from_forward_pass(cls, model: nn.Module) -> int:
        """Detect output size from forward pass with dummy input."""
        try:
            model.eval()
            with torch.no_grad():
                # Try different input configurations
                for channels in [1, 3]:  # Grayscale and RGB
                    for size in [224, 91, 64, 32]:
                        try:
                            dummy_input = torch.randn(1, channels, size, size)
                            output = model(dummy_input)
                            if output.dim() == 2:  # Batch dimension + features
                                return output.size(1)
                            if output.dim() == 1:  # Just features
                                return output.size(0)
                        except Exception:
                            continue
        except Exception as e:
            logger.warning("Failed to infer output size from forward pass: %s", str(e))

        return None

    @classmethod
    def _validate_output_size_compatibility(
        cls,
        model_output_size: int,
        dataset_num_classes: int,
    ) -> bool:
        """
        Validate that model output size is compatible with dataset classes.

        Args:
            model_output_size: Number of output features in the model
            dataset_num_classes: Number of classes in the dataset

        Returns:
            True if compatible, False otherwise
        """
        # For binary classification, model can have 1 or 2 outputs
        if dataset_num_classes == 2:
            return model_output_size in [1, 2]

        # For multi-class classification, outputs should match classes
        if dataset_num_classes > 2:
            return model_output_size == dataset_num_classes

        # Single class case (unusual but possible)
        if dataset_num_classes == 1:
            return model_output_size == 1

        return False

    @classmethod
    def get_recommended_num_classes(cls, data_loaders: Dict[str, DataLoader]) -> int:
        """
        Get the recommended number of classes for model creation based on dataset.

        Args:
            data_loaders: Dictionary of data loaders

        Returns:
            Recommended number of classes for model output layer

        Raises:
            ModelValidationError: If unable to determine class count
        """
        try:
            dataset_num_classes = extract_num_classes_from_data_loaders(data_loaders)

            # For binary classification, use 1 output (with sigmoid/BCE)
            if dataset_num_classes == 2:
                return 1

            # For multi-class, use the actual number of classes
            return dataset_num_classes

        except DatasetExtractionError as e:
            raise ModelValidationError(
                f"Unable to determine recommended class count: {str(e)}"
            ) from e
