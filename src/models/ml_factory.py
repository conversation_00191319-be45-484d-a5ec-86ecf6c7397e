"""
ML Model Factory for creating, configuring, and managing PyTorch models.
"""

from pathlib import Path
from typing import Any, Dict, Optional, Union

from torch import nn, optim
from torch.optim import lr_scheduler
from torch.utils.data import DataLoader

from .architectures import create_model_from_architecture
from .dynamic_cnn import create_dynamic_cnn
from .model_utils import create_loss_function as create_loss_function_util
from .model_utils import create_optimizer as create_optimizer_util
from .model_utils import create_scheduler as create_scheduler_util
from .persistence import ModelPersistence
from .validation import ModelValidator


class MLModelFactory:
    """
    Factory for creating and configuring ML models, loss functions, and optimizers.
    """

    def __init__(self, persistence_base_dir: Optional[Union[str, Path]] = None):
        self.persistence = ModelPersistence(base_dir=persistence_base_dir)

    def create_model(
        self, architecture_params: Dict[str, Any], num_classes: int = 1
    ) -> nn.Module:
        """
        Create a model based on architecture parameters.
        """
        arch_name = architecture_params.get("name")
        if not arch_name:
            raise ValueError("Architecture 'name' must be specified.")

        variant = architecture_params.get("variant", "")
        full_arch_name = f"{arch_name}{variant}"

        if arch_name.upper() == "CNN":
            # Extract nested parameters for the dynamic CNN builder
            nested_params = architecture_params.get("parameters", {})

            model_version_params = (nested_params.get("model_version") or {}).get(
                "parameters", {}
            )
            model_run_params = (nested_params.get("model_run") or {}).get(
                "parameters", {}
            )

            # Corrected image_size and image_channels retrieval
            current_image_size = nested_params.get("image_size", 91)
            current_image_channels = nested_params.get("image_channels", 1)

            model, _, _ = create_dynamic_cnn(
                {
                    "model_params": model_version_params,
                    "dropout_rate": model_run_params.get("dropout_rate"),
                    "num_classes": num_classes,
                    "image_size": current_image_size,
                    "image_channels": current_image_channels,
                }
            )
            return model

        try:
            model = create_model_from_architecture(
                architecture_name=full_arch_name,
                num_classes=num_classes,
                pretrained=architecture_params.get("pretrained", False),
            )
            return model
        except (ValueError, NotImplementedError) as e:
            raise ValueError(f"Failed to create model '{full_arch_name}': {e}") from e

    def create_model_with_auto_classes(
        self, architecture_params: Dict[str, Any], data_loaders: Dict[str, DataLoader]
    ) -> nn.Module:
        """
        Create a model with automatically detected number of classes from data loaders.

        Args:
            architecture_params: Architecture configuration parameters
            data_loaders: Dictionary of data loaders to extract class count from

        Returns:
            PyTorch model with correct output size for the dataset

        Raises:
            ValueError: If unable to detect classes or create model
        """
        # Automatically detect the number of classes
        num_classes = ModelValidator.get_recommended_num_classes(data_loaders)

        # Create the model with detected class count
        model = self.create_model(architecture_params, num_classes)

        # Validate the created model
        ModelValidator.validate_model_dataset_compatibility(
            model, data_loaders, architecture_params
        )

        return model

    def create_loss_function(self, loss_params: Dict[str, Any]) -> nn.Module:
        """
        Create a loss function.
        """
        return create_loss_function_util(loss_params)

    def create_loss_function_from_architecture(
        self, architecture_params: Dict[str, Any], num_classes: int = 1
    ) -> nn.Module:
        """
        Create an appropriate loss function based on architecture parameters and number of classes.

        This method automatically determines the best loss function for the given configuration:
        - Binary classification (num_classes=1): BCEWithLogitsLossWrapper
        - Multi-class classification (num_classes>1): CrossEntropyLoss

        Args:
            architecture_params: Architecture configuration parameters
            num_classes: Number of output classes

        Returns:
            Appropriate loss function for the model configuration
        """
        # Check if loss function is explicitly specified in architecture_params
        nested_params = architecture_params.get("parameters", {})
        model_run_params = (nested_params.get("model_run") or {}).get("parameters", {})

        if "loss_function" in model_run_params:
            # Use explicitly specified loss function
            loss_config = model_run_params["loss_function"]
            if isinstance(loss_config, str):
                loss_config = {"type": loss_config}
            return self.create_loss_function(loss_config)

        # Auto-determine loss function based on num_classes
        if num_classes == 1:
            # Binary classification - use wrapper to handle shape mismatches
            return create_loss_function_util({"type": "bce_wrapper"})

        # Multi-class classification
        return create_loss_function_util({"type": "ce"})

    def create_optimizer(
        self, optimizer_params: Dict[str, Any], model_parameters
    ) -> optim.Optimizer:
        """
        Create an optimizer.
        """
        return create_optimizer_util(optimizer_params, model_parameters)

    def create_scheduler(
        self, scheduler_params: Dict[str, Any], optimizer: optim.Optimizer
    ) -> Optional[lr_scheduler._LRScheduler]:
        """
        Create a learning rate scheduler.
        """
        return create_scheduler_util(scheduler_params, optimizer)
