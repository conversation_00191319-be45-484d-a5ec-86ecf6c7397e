"""
Model architecture definitions and utilities.

This module provides a mapping between frontend architecture types and their
corresponding implementations in PyTorch and other libraries.
"""

from enum import Enum
from typing import Dict, List, Optional

from pydantic import BaseModel, Field
from torch import nn
from torchvision import models


class ArchitectureCategory(str, Enum):
    """Categories of model architectures."""

    CLASSIC = "classic"
    MODERN = "modern"
    ATTENTION_BASED = "attention_based"
    SPECIALISED = "specialised"
    EMERGING = "emerging"


class ModelArchitecture(str, Enum):
    """
    Supported model architecture types.

    These match the frontend architecture types and are mapped to their
    corresponding implementations in PyTorch or other libraries.
    """

    # Classic architectures
    CNN = "CNN"
    ALEXNET = "AlexNet"
    VGGNET = "VGGNet"
    GOOGLENET = "GoogLeNet"

    # Modern architectures
    RESNET = "ResNet"
    DENSENET = "DenseNet"
    MOBILENET = "MobileNet"

    # Attention-based architectures
    VIT = "ViT"  # Vision Transformer
    HM = "HM"  # Hierarchical Models

    # Specialised architectures
    YOLO = "YOLO"
    UNET = "Unet"
    EFFICIENTNET = "EfficientNet"

    # Emerging architectures
    CFM = "CFM"  # Continuous Flow Models
    SSL = "SSL"  # Self-Supervised Learning
    GM = "GM"  # Generative Models


class ArchitectureInfo(BaseModel):
    """Information about a model architecture."""

    name: ModelArchitecture
    category: ArchitectureCategory
    description: str
    pytorch_class: Optional[str] = None
    requires_custom_implementation: bool = False
    input_channels: int = 3
    default_params: Dict = Field(default_factory=dict)


# Mapping of architecture types to their implementations
ARCHITECTURE_INFO: Dict[ModelArchitecture, ArchitectureInfo] = {
    # Classic architectures
    ModelArchitecture.CNN: ArchitectureInfo(
        name=ModelArchitecture.CNN,
        category=ArchitectureCategory.CLASSIC,
        description="Basic Convolutional Neural Network",
        requires_custom_implementation=True,
        input_channels=1,
    ),
    ModelArchitecture.ALEXNET: ArchitectureInfo(
        name=ModelArchitecture.ALEXNET,
        category=ArchitectureCategory.CLASSIC,
        description="AlexNet architecture (2012)",
        pytorch_class="models.alexnet",
    ),
    ModelArchitecture.VGGNET: ArchitectureInfo(
        name=ModelArchitecture.VGGNET,
        category=ArchitectureCategory.CLASSIC,
        description="VGG architecture family",
        pytorch_class="models.vgg16",
        default_params={"pretrained": False},
    ),
    ModelArchitecture.GOOGLENET: ArchitectureInfo(
        name=ModelArchitecture.GOOGLENET,
        category=ArchitectureCategory.CLASSIC,
        description="GoogLeNet/Inception architecture",
        pytorch_class="models.googlenet",
    ),
    # Modern architectures
    ModelArchitecture.RESNET: ArchitectureInfo(
        name=ModelArchitecture.RESNET,
        category=ArchitectureCategory.MODERN,
        description="Residual Network architecture",
        pytorch_class="models.resnet50",
    ),
    ModelArchitecture.DENSENET: ArchitectureInfo(
        name=ModelArchitecture.DENSENET,
        category=ArchitectureCategory.MODERN,
        description="Densely Connected Convolutional Networks",
        pytorch_class="models.densenet121",
    ),
    ModelArchitecture.MOBILENET: ArchitectureInfo(
        name=ModelArchitecture.MOBILENET,
        category=ArchitectureCategory.MODERN,
        description="MobileNet architecture for mobile devices",
        pytorch_class="models.mobilenet_v3_small",
    ),
    # Attention-based architectures
    ModelArchitecture.VIT: ArchitectureInfo(
        name=ModelArchitecture.VIT,
        category=ArchitectureCategory.ATTENTION_BASED,
        description="Vision Transformer",
        pytorch_class="models.vit_b_16",
    ),
    ModelArchitecture.HM: ArchitectureInfo(
        name=ModelArchitecture.HM,
        category=ArchitectureCategory.ATTENTION_BASED,
        description="Hierarchical Models with attention mechanisms",
        requires_custom_implementation=True,
    ),
    # Specialised architectures
    ModelArchitecture.YOLO: ArchitectureInfo(
        name=ModelArchitecture.YOLO,
        category=ArchitectureCategory.SPECIALISED,
        description="You Only Look Once - object detection architecture",
        requires_custom_implementation=True,
    ),
    ModelArchitecture.UNET: ArchitectureInfo(
        name=ModelArchitecture.UNET,
        category=ArchitectureCategory.SPECIALISED,
        description="U-Net architecture for segmentation tasks",
        requires_custom_implementation=True,
    ),
    ModelArchitecture.EFFICIENTNET: ArchitectureInfo(
        name=ModelArchitecture.EFFICIENTNET,
        category=ArchitectureCategory.SPECIALISED,
        description="EfficientNet architecture",
        pytorch_class="models.efficientnet_b0",
    ),
    # Emerging architectures
    ModelArchitecture.CFM: ArchitectureInfo(
        name=ModelArchitecture.CFM,
        category=ArchitectureCategory.EMERGING,
        description="Continuous Flow Models",
        requires_custom_implementation=True,
    ),
    ModelArchitecture.SSL: ArchitectureInfo(
        name=ModelArchitecture.SSL,
        category=ArchitectureCategory.EMERGING,
        description="Self-Supervised Learning architectures",
        requires_custom_implementation=True,
    ),
    ModelArchitecture.GM: ArchitectureInfo(
        name=ModelArchitecture.GM,
        category=ArchitectureCategory.EMERGING,
        description="Generative Models",
        requires_custom_implementation=True,
    ),
}


def get_architecture_info(architecture_name: str) -> ArchitectureInfo:
    """
    Get information about a model architecture by name.

    Args:
        architecture_name: Name of the architecture

    Returns:
        ArchitectureInfo object with details about the architecture

    Raises:
        ValueError: If the architecture name is not supported
    """
    try:
        # Try to match the exact enum value
        arch = ModelArchitecture(architecture_name)
        return ARCHITECTURE_INFO[arch]
    except ValueError as exc:
        # Try to match case-insensitive
        for arch in ModelArchitecture:
            if arch.value.lower() == architecture_name.lower():
                return ARCHITECTURE_INFO[arch]

        # Handle specific cases like "ResNet50" -> "ResNet"
        for arch in ModelArchitecture:
            if architecture_name.lower().startswith(arch.value.lower()):
                return ARCHITECTURE_INFO[arch]

        raise ValueError(f"Unsupported architecture: {architecture_name}") from exc


def get_all_architectures() -> List[ArchitectureInfo]:
    """
    Get information about all supported architectures.

    Returns:
        List of ArchitectureInfo objects
    """
    return list(ARCHITECTURE_INFO.values())


def get_architectures_by_category(
    category: ArchitectureCategory,
) -> List[ArchitectureInfo]:
    """
    Get information about architectures in a specific category.

    Args:
        category: Category of architectures to retrieve

    Returns:
        List of ArchitectureInfo objects in the specified category
    """
    return [info for info in ARCHITECTURE_INFO.values() if info.category == category]


def create_model_from_architecture(
    architecture_name: str, num_classes: int = 1, pretrained: bool = False, **kwargs
) -> nn.Module:
    """
    Create a PyTorch model instance based on the specified architecture.

    Args:
        architecture_name: Name of the architecture
        num_classes: Number of output classes
        pretrained: Whether to use pretrained weights
        **kwargs: Additional arguments to pass to the model constructor

    Returns:
        PyTorch model instance

    Raises:
        ValueError: If the architecture is not supported or requires custom implementation
        NotImplementedError: If the architecture requires custom implementation
    """
    arch_info = get_architecture_info(architecture_name)

    if arch_info.requires_custom_implementation:
        # Import here to avoid circular imports
        if arch_info.name == ModelArchitecture.CNN:
            # For the basic CNN, we use our new dynamic CNN implementation
            # Import inside function to avoid circular imports
            # pylint: disable=import-outside-toplevel
            from .dynamic_cnn import create_dynamic_cnn

            # Extract parameters for the dynamic CNN
            params = {
                "model_version": kwargs.get("model_version"),
                "model_run": kwargs.get("model_run"),
            }
            image_size = kwargs.get("image_size", 224)
            image_channels = kwargs.get(
                "image_channels", 3
            )  # Default to 3 channels (RGB)

            network, _, _ = create_dynamic_cnn(
                {
                    "model_params": params.get("model_version") or {},
                    "dropout_rate": (params.get("model_run") or {}).get("dropout_rate"),
                    "num_classes": num_classes,
                    "image_size": image_size,
                    "image_channels": image_channels,
                }
            )
            return network

        raise NotImplementedError(
            f"Architecture {architecture_name} requires custom implementation "
            f"which is not yet available"
        )

    # For PyTorch models from torchvision
    if not arch_info.pytorch_class:
        raise ValueError(f"Architecture {architecture_name} is not properly configured")

    # Get the model class dynamically without using eval
    model_class_path = arch_info.pytorch_class.split(".")
    if len(model_class_path) != 2 or model_class_path[0] != "models":
        raise ValueError(f"Invalid model class path: {arch_info.pytorch_class}")

    model_fn = getattr(models, model_class_path[1])

    # Merge default params with provided kwargs
    params = arch_info.default_params.copy()
    params.update(kwargs)

    # Add pretrained parameter if supported
    if (
        "pretrained" not in params
        and hasattr(model_fn, "__code__")
        and "pretrained" in model_fn.__code__.co_varnames
    ):
        params["pretrained"] = pretrained

    # For newer torchvision versions that use weights parameter instead of pretrained
    if (
        "weights" not in params
        and hasattr(model_fn, "__code__")
        and "weights" in model_fn.__code__.co_varnames
    ):
        if pretrained:
            params["weights"] = "DEFAULT"
        else:
            params["weights"] = None

    # Create the model
    model = model_fn(**params)

    # Modify the final layer for our classification task
    if hasattr(model, "fc"):  # ResNet, DenseNet, etc.
        in_features = model.fc.in_features
        model.fc = nn.Linear(in_features, num_classes)
    elif hasattr(model, "classifier") and isinstance(
        model.classifier, nn.Linear
    ):  # ViT, etc.
        in_features = model.classifier.in_features
        model.classifier = nn.Linear(in_features, num_classes)
    elif hasattr(model, "classifier") and isinstance(
        model.classifier, nn.Sequential
    ):  # AlexNet, VGG
        in_features = model.classifier[-1].in_features
        model.classifier[-1] = nn.Linear(in_features, num_classes)
    else:
        raise ValueError(
            f"Don't know how to modify the final layer of {architecture_name}"
        )

    return model
