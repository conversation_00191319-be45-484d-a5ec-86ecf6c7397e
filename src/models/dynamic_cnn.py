"""
Dynamically constructs a Convolutional Neural Network (CNN) from a dictionary of parameters.

This module provides the functionality to build a PyTorch neural network model
dynamically based on a configuration dictionary. This allows for flexible model
architectures that can be defined by parameters stored in a database or configuration file,
rather than being hard-coded.

The main function, `create_dynamic_cnn`, takes a set of parameters and constructs a CNN
with specified convolutional layers, activation functions, pooling, batch normalization,
dropout, and fully connected layers. This is used to replace static model definitions,
such as the one in `CoinsNet.py`.
"""

from typing import Any, Dict, List, Tuple

from torch import nn

from .model_utils import get_activation


def _build_conv_block(config: Dict[str, Any]) -> Tuple[List[nn.Module], int, int]:
    """Builds the convolutional part of the CNN."""
    layers: List[nn.Module] = []
    in_channels = config["in_channels"]
    current_size = config["image_size"]

    for out_channels, kernel_size, stride, padding in config["conv_layers_params"]:
        layers.append(
            nn.Conv2d(
                in_channels,
                out_channels,
                kernel_size=kernel_size,
                stride=stride,
                padding=padding,
            )
        )
        if config.get("use_batch_norm", False):
            layers.append(nn.BatchNorm2d(out_channels))
        layers.append(get_activation(config.get("activation_name")))
        current_size = ((current_size - kernel_size + 2 * padding) // stride) + 1
        in_channels = out_channels

    if config.get("pooling_config"):
        pool_type, pool_kernel, pool_stride = config["pooling_config"]
        if pool_type.lower() == "max":
            layers.append(nn.MaxPool2d(kernel_size=pool_kernel, stride=pool_stride))
            current_size = ((current_size - pool_kernel) // pool_stride) + 1
        else:
            raise ValueError(f"Unsupported pooling type: {pool_type}")

    return layers, in_channels, current_size


def _build_fc_block(config: Dict[str, Any]) -> Tuple[List[nn.Module], int]:
    """Builds the fully connected part of the CNN."""
    layers: List[nn.Module] = []
    in_features = config["in_features"]
    final_feature_dim = in_features  # Default if no FC layers
    dropout_rate = config.get("dropout_rate")

    for fc_out_features in config["fc_layers_params"]:
        layers.append(nn.Linear(in_features, fc_out_features))
        layers.append(get_activation(config.get("activation_name")))
        if dropout_rate is not None and dropout_rate > 0:
            layers.append(nn.Dropout(dropout_rate))
        in_features = fc_out_features
        final_feature_dim = fc_out_features

    layers.append(nn.Linear(in_features, config["num_classes"]))
    return layers, final_feature_dim


def create_dynamic_cnn(params: Dict[str, Any]) -> Tuple[nn.Module, int, int]:
    """
    Creates a dynamic CNN model based on provided parameters.

    Args:
        params: A dictionary containing all parameters needed to build the CNN:
            - model_params: Dictionary containing model architecture parameters
                - convolutional_layers: List of convolutional layer configurations
                - fully_connected_layers: List of fully connected layer sizes
                - batch_norm: Whether to use batch normalization
                - activation: Activation function name
                - pooling: Pooling configuration [type, kernel_size, stride]
            - dropout_rate: Dropout rate for fully connected layers
            - num_classes: Number of output classes
            - image_size: Input image size (assumed square)
            - image_channels: Number of input channels

    Returns:
        Tuple containing:
            - The constructed CNN model
            - The final feature dimension
            - The final spatial size (always 1 after flattening)
    """
    # Extract required parameters with defaults
    model_params = params.get("model_params", {})
    num_classes = params.get("num_classes", 1)
    image_size = params.get("image_size", 91)
    image_channels = params.get("image_channels", 1)
    dropout_rate = params.get("dropout_rate")

    # Extract convolutional layer parameters
    extracted_conv_params = model_params.get("convolutional_layers", [])

    conv_layers, out_channels, final_size = _build_conv_block(
        {
            "conv_layers_params": extracted_conv_params,
            "in_channels": image_channels,
            "image_size": image_size,
            "use_batch_norm": model_params.get("batch_norm", False),
            "activation_name": model_params.get("activation"),
            "pooling_config": model_params.get("pooling"),
        }
    )

    # Extract fully connected layer parameters
    extracted_fc_params = model_params.get("fully_connected_layers", [])

    fc_layers, final_feature_dim = _build_fc_block(
        {
            "fc_layers_params": extracted_fc_params,
            "in_features": out_channels * final_size * final_size,
            "num_classes": num_classes,
            "activation_name": model_params.get("activation"),
            "dropout_rate": dropout_rate,
        }
    )

    model = nn.Sequential(*conv_layers, nn.Flatten(), *fc_layers)
    final_spatial_size = 1  # After flatten, spatial size is 1

    return model, final_feature_dim, final_spatial_size
