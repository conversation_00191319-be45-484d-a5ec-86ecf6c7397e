"""
Middleware for handling Supabase profile management.
Provides functionality to switch between different Supabase environments
(development, staging, production) based on request headers.
"""

import sys
from typing import Callable

from fastapi import Request
from pydantic import ValidationError
from starlette.middleware.base import BaseHTTPMiddleware

from api.config import settings
from api.constants import (
    DEFAULT_SUPABASE_PROFILE,
    SUPABASE_PROFILE_HEADER,
    VALID_SUPABASE_PROFILES,
)


class SupabaseProfileMiddleware(BaseHTTPMiddleware):
    """
    Middleware that manages Supabase profile switching based on request headers.

    Intercepts incoming requests to check for the X-Supabase-Profile header
    and updates the application settings to use the appropriate Supabase
    environment credentials (development, staging, or production).
    """

    async def dispatch(self, request: Request, call_next: Callable):
        """Process the request and handle Supabase profile switching."""
        # Get profile from header, default to development if not provided
        profile = request.headers.get(SUPABASE_PROFILE_HEADER, DEFAULT_SUPABASE_PROFILE)

        # Validate and set profile
        if profile in VALID_SUPABASE_PROFILES:
            try:
                # Update settings with new profile
                new_settings = settings.model_copy(update={"SUPABASE_PROFILE": profile})

                # Update the settings in the config module
                sys.modules["api.config"].settings = new_settings

                # Store settings in request state for access in route handlers
                request.state.settings = new_settings

            except (ValidationError, AttributeError) as e:
                print(f"Settings update error: {e}")

        response = await call_next(request)
        return response
