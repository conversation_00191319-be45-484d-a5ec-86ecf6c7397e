"""
Route handlers for coin identification endpoints.
"""

import torch
from fastapi import APIRouter, Request
from pydantic import BaseModel

from api.utils import get_supabase_profile
from api.utils.error_handlers import api_route_handler
from src.models import MLModelFactory
from src.models.persistence import LoadModelOptions

router = APIRouter(
    prefix="/identify",
    tags=["identification"],  # Groups endpoints under "identification" in the docs
    responses={500: {"description": "Internal Server Error"}},
)


class ModelInput(BaseModel):
    """
    Data model for coin identification input.

    This class defines the structure of the input data expected by the
    coin identification endpoint. It validates that the input data is
    in the correct format before processing.

    Attributes:
        input_data (list): List of pixel values representing the coin image.
                          Expected to be a flattened array of normalized pixel values.
    """

    input_data: list


@router.post(
    "",
    summary="Identify Coin",
    description="Identifies a coin on provided image.",
)
@api_route_handler("identifying coin")
async def identify_coin(request: Request, data: ModelInput):
    """
    Endpoint for coin identification using the trained model.

    Args:
        data (ModelInput): The input data containing the coin image to classify.

    Returns:
        dict: A dictionary containing the prediction result.
              Format: {"prediction": int, "confidence": float}
    """
    # Get the profile from the request header (for logging purposes only)
    _ = get_supabase_profile(request)  # Using _ to indicate intentionally unused

    factory = MLModelFactory(persistence_base_dir="src/models/trained")

    # Next: Load model configuration from a file or database alongside the model
    # instead of hardcoding it here.
    architecture_params = {
        "name": "CNN",
        "image_size": 91,
        "image_channels": 1,
        "model_version": {
            "parameters": {
                "convolutional_layers": [[32, 3, 1, 1], [64, 3, 1, 1]],
                "fully_connected_layers": [128],
                "activation": "relu",
                "batch_norm": True,
                "pooling": ["max", 2, 2],
            }
        },
        "model_run": {"parameters": {"dropout_rate": 0.5}},
    }
    num_classes = 50

    model = factory.create_model(
        architecture_params=architecture_params,
        num_classes=num_classes,
    )

    model_id = "trained_model_script"
    try:
        load_options = LoadModelOptions(
            model=model, optimizer=None
        )  # Create options instance
        model, _, _ = factory.persistence.load_model(
            options=load_options, model_id=model_id  # Use options instance
        )
    except FileNotFoundError as exc:
        raise FileNotFoundError(
            f"Checkpoint for model_id '{model_id}' not found. "
            "Please ensure the model is trained and the ID is correct."
        ) from exc

    model.eval()

    input_tensor = torch.tensor(data.input_data, dtype=torch.float32).view(
        1,
        architecture_params["image_channels"],
        architecture_params["image_size"],
        architecture_params["image_size"],
    )

    with torch.no_grad():
        output = model(input_tensor)
        probabilities = torch.nn.functional.softmax(output, dim=1)
        confidence, predicted_idx = torch.max(probabilities, 1)

    # The predicted_idx should be mapped to a human-readable class name.
    return {"prediction": predicted_idx.item(), "confidence": confidence.item()}
