"""
FastAPI application for the coiny classifier.
"""

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from .constants import SUPABASE_PROFILE_HEADER
from .middleware.supabase_profile import SupabaseProfileMiddleware
from .routes import datasets, identify, train, training_data

app = FastAPI(
    title="Coiny Classifier API",
    description="API for training and using coin classification models",
    version="0.1.0",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Modify this in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*", SUPABASE_PROFILE_HEADER],
)

# Add Supabase profile middleware
app.add_middleware(SupabaseProfileMiddleware)

# Include routers
app.include_router(identify.router, prefix="/api/v1")
app.include_router(datasets.router, prefix="/api/v1")
app.include_router(train.router, prefix="/api/v1")
app.include_router(training_data.router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint to verify API is running."""
    return {"status": "ok", "message": "Coiny Classifier API is running"}
