"""Utility functions for the API."""

from fastapi import Request

from api.constants import DEFAULT_SUPABASE_PROFILE, SUPABASE_PROFILE_HEADER

from .error_handlers import handle_api_error


def get_supabase_profile(request: Request) -> str:
    """
    Extract the Supabase profile from the request headers.

    Args:
        request: The FastAPI request object

    Returns:
        str: The Supabase profile name (development, staging, production)
    """
    profile = request.headers.get(SUPABASE_PROFILE_HEADER, DEFAULT_SUPABASE_PROFILE)
    print(f"Using Supabase profile: {profile}")
    return profile


__all__ = ["handle_api_error", "get_supabase_profile"]
