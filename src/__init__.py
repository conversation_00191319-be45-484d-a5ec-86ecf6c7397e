"""
Root package initialization.
"""

import os
import sys

# Add the src directory to the Python path if it's not already there
src_dir = os.path.dirname(os.path.abspath(__file__))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Import key modules to make them available at the package level
# This helps with imports like `from src import api`
try:
    import api  # noqa: F401 - Import is used for package-level access
except ImportError:
    # If api can't be imported, it might be because we're in a test environment
    # where the package is not installed in development mode
    pass
