"""
Configuration for image locations.

This module provides mappings from location keys stored in the database
to physical directory paths where images are stored.
"""

from enum import Enum

from config.paths import (
    DIR_COINS,
    DIR_FACTS_BANKNOTES,
    DIR_FACTS_COINS,
    DIR_FACTS_MISC,
    DIR_FACTS_STAMPS,
    DIR_FACTS_WATCH,
    DIR_SCRAPES,
)


class ImageLocationKeys(str, Enum):
    """Enum for image location keys stored in the database."""

    COIN = "CS"
    FACT_BANKNOTE = "FB"
    FACT_COIN = "FC"
    FACT_MISC = "FM"
    FACT_STAMP = "FS"
    FACT_WATCH = "FW"
    SCRAPES = "SS"


FACT_IMAGE_LOCATION_KEYS = {
    ImageLocationKeys.FACT_BANKNOTE,
    ImageLocationKeys.FACT_COIN,
    ImageLocationKeys.FACT_MISC,
    ImageLocationKeys.FACT_STAMP,
    ImageLocationKeys.FACT_WATCH,
}

IMAGE_LOCATIONS = {
    ImageLocationKeys.COIN: DIR_COINS,
    ImageLocationKeys.FACT_BANKNOTE: DIR_FACTS_BANKNOTES,
    ImageLocationKeys.FACT_COIN: DIR_FACTS_COINS,
    ImageLocationKeys.FACT_MISC: DIR_FACTS_MISC,
    ImageLocationKeys.FACT_STAMP: DIR_FACTS_STAMPS,
    ImageLocationKeys.FACT_WATCH: DIR_FACTS_WATCH,
    ImageLocationKeys.SCRAPES: DIR_SCRAPES,
}
