"""Custom pylint plugin for handling error checks."""

from typing import Optional

from astroid import nodes
from pylint.checkers import <PERSON><PERSON><PERSON><PERSON>
from pylint.checkers.utils import only_required_for_messages
from pylint.lint.pylinter import PyLinter


class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(BaseChecker):
    """Checker that allows broad exception catching when using handle_api_error."""

    name = "error-handler"
    priority = -1
    msgs = {
        "I9001": (
            "Broad exception is properly handled by handle_api_error",
            "properly-handled-broad-exception",
            "Used when broad exception is handled by handle_api_error",
        ),
    }

    def __init__(self, linter: Optional[PyLinter] = None):
        super().__init__(linter)
        self._function_stack = []

    def _check_handler_body(self, handler_body) -> bool:
        """Check if handle_api_error is called in the handler body."""
        for child in handler_body:
            if not isinstance(child, nodes.Expr):
                continue
            if not isinstance(child.value, nodes.Call):
                continue
            if not isinstance(child.value.func, nodes.Name):
                continue
            if child.value.func.name == "handle_api_error":
                return True
        return False

    @only_required_for_messages("properly-handled-broad-exception")
    def visit_try(self, node: nodes.Try) -> None:
        """Visit try/except block and check for handle_api_error usage."""
        for handler in node.handlers:
            if not isinstance(handler.type, nodes.Name):
                continue
            if handler.type.name != "Exception":
                continue
            if self._check_handler_body(handler.body):
                self.add_message("properly-handled-broad-exception", node=handler)


def register(linter: PyLinter) -> None:
    """Register the checker with pylint."""
    linter.register_checker(ErrorHandlerChecker(linter))
